import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:moroccanaccounting/theme/app_theme.dart';

void main() {
  group('AppTheme Tests', () {
    test('Light color scheme has correct colors', () {
      final scheme = AppTheme.getLightColorScheme();
      
      expect(scheme.primary, const Color(0xFF2563EB));
      expect(scheme.secondary, const Color(0xFF4F46E5));
      expect(scheme.tertiary, const Color(0xFFDB2777));
      expect(scheme.surface, Colors.white);
      expect(scheme.surface, const Color(0xFFF1F5F9));
    });

    test('Dark color scheme has correct colors', () {
      final scheme = AppTheme.getDarkColorScheme();
      
      expect(scheme.primary, const Color(0xFF60A5FA));
      expect(scheme.secondary, const Color(0xFF818CF8));
      expect(scheme.tertiary, const Color(0xFFF472B6));
      expect(scheme.surface, const Color(0xFF1A1B26));
      expect(scheme.surface, const Color(0xFF0F1117));
      expect(scheme.onSurfaceVariant, const Color(0xFFD1D5DB));
      expect(scheme.onPrimaryContainer, const Color(0xFFBFDBFE));
      expect(scheme.onSecondaryContainer, const Color(0xFFC7D2FE));
      expect(scheme.onTertiaryContainer, const Color(0xFFFCE7F3));
      expect(scheme.outline.value, const Color(0xFF374151).withOpacity(0.5).value);
    });

    test('Module colors are generated correctly', () {
      final scheme = AppTheme.getLightColorScheme();
      final colors = AppTheme.getModuleColors(scheme);
      
      expect(colors.length, 5);
      expect(colors[0], scheme.primary);
      expect(colors[1], scheme.secondary);
      expect(colors[2], scheme.tertiary);
      // Interpolated colors
      expect(colors[3], Color.lerp(scheme.primary, scheme.secondary, 0.5));
      expect(colors[4], Color.lerp(scheme.secondary, scheme.tertiary, 0.5));
    });

    testWidgets('Theme applies correctly to widgets', (WidgetTester tester) async {
      final scheme = AppTheme.getLightColorScheme();
      final theme = AppTheme.getThemeData(scheme);
      
      await tester.pumpWidget(
        MaterialApp(
          theme: theme,
          home: Scaffold(
            appBar: AppBar(
              title: const Text('Test'),
            ),
            body: Column(
              children: [
                ElevatedButton(
                  onPressed: () {},
                  child: const Text('Button'),
                ),
                Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Text('Card', style: theme.textTheme.titleMedium),
                  ),
                ),
              ],
            ),
          ),
        ),
      );

      // Verify AppBar theme
      final appBarFinder = find.byType(AppBar);
      expect(appBarFinder, findsOneWidget);
      final AppBar appBar = tester.widget(appBarFinder);
      final Material material = tester.widget<Material>(find.descendant(
        of: find.byType(AppBar),
        matching: find.byType(Material),
      ));
      expect(material.color, scheme.surface);
      expect(material.elevation, 3.0);

      // Verify Button theme
      final buttonFinder = find.byType(ElevatedButton);
      expect(buttonFinder, findsOneWidget);
      final button = tester.widget<ElevatedButton>(buttonFinder);
      final Material buttonMaterial = tester.widget<Material>(find.descendant(
        of: buttonFinder,
        matching: find.byType(Material),
      ));
      expect(buttonMaterial.elevation, 2.0);

      // Verify Card theme
      final cardFinder = find.byType(Card);
      expect(cardFinder, findsOneWidget);
      final card = tester.widget<Card>(cardFinder);
      final Material cardMaterial = tester.widget<Material>(find.descendant(
        of: cardFinder,
        matching: find.byType(Material),
      ));
      expect(cardMaterial.elevation, 2.0);
      expect(cardMaterial.color, scheme.surface);
    });
  });
}
