import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';

import 'package:moroccanaccounting/main.dart';
import 'package:moroccanaccounting/services/theme_service.dart';
import 'package:moroccanaccounting/screens/splash_screen.dart';

void main() {
  testWidgets('App renders correctly', (WidgetTester tester) async {
    // Build our app and trigger a frame
    await tester.pumpWidget(
      MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => ThemeService()),
        ],
        child: const MoroccanAccountingApp(),
      )
    );

    // Verify that splash screen is shown initially
    expect(find.byType(SplashScreen), findsOneWidget);
  });
}
