import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:excel/excel.dart' hide Border, BorderStyle;
import 'package:path_provider/path_provider.dart';
import '../models/immobilisations/amortization_row.dart';

/// Export settings for customizing Excel output
class ExportSettings {
  final bool includeCharts;
  final bool includeFormulas;
  final bool useAlternatingRowColors;
  final bool freezePanes;
  final bool includeSummary;
  final String? companyName;
  final String? companyLogo;
  final String language;
  final Map<String, String>? customHeaders;
  final Map<String, String>? customFooters;

  const ExportSettings({
    this.includeCharts = true,
    this.includeFormulas = true,
    this.useAlternatingRowColors = true,
    this.freezePanes = true,
    this.includeSummary = true,
    this.companyName,
    this.companyLogo,
    this.language = 'fr',
    this.customHeaders,
    this.customFooters,
  });

  ExportSettings copyWith({
    bool? includeCharts,
    bool? includeFormulas,
    bool? useAlternatingRowColors,
    bool? freezePanes,
    bool? includeSummary,
    String? companyName,
    String? companyLogo,
    String? language,
    Map<String, String>? customHeaders,
    Map<String, String>? customFooters,
  }) {
    return ExportSettings(
      includeCharts: includeCharts ?? this.includeCharts,
      includeFormulas: includeFormulas ?? this.includeFormulas,
      useAlternatingRowColors: useAlternatingRowColors ?? this.useAlternatingRowColors,
      freezePanes: freezePanes ?? this.freezePanes,
      includeSummary: includeSummary ?? this.includeSummary,
      companyName: companyName ?? this.companyName,
      companyLogo: companyLogo ?? this.companyLogo,
      language: language ?? this.language,
      customHeaders: customHeaders ?? this.customHeaders,
      customFooters: customFooters ?? this.customFooters,
    );
  }
}

/// Data structure for table export
class TableData {
  final String name;
  final String title;
  final List<String> headers;
  final List<List<String>> rows;
  final Map<String, String>? metadata;
  final List<int>? numericColumns;
  final List<int>? percentageColumns;
  final List<int>? currencyColumns;

  const TableData({
    required this.name,
    required this.title,
    required this.headers,
    required this.rows,
    this.metadata,
    this.numericColumns,
    this.percentageColumns,
    this.currencyColumns,
  });

  /// Create TableData from AmortizationRow list
  factory TableData.fromAmortizationRows(
    List<AmortizationRow> rows, {
    required String name,
    required String title,
    bool isDegressive = false,
  }) {
    if (rows.isEmpty) {
      return TableData(
        name: name,
        title: title,
        headers: [],
        rows: [],
      );
    }

    // Determine headers based on mode
    List<String> headers;
    List<int> numericColumns;
    List<int> percentageColumns;
    List<int> currencyColumns;

    if (isDegressive) {
      headers = [
        'Année',
        'Base d\'amortissement',
        'Taux dégressif',
        'Taux linéaire',
        'Annuités d\'amortissement',
        'Annuités cumulées',
        'Valeur nette comptable',
      ];
      numericColumns = [1, 4, 5, 6];
      percentageColumns = [2, 3];
      currencyColumns = [1, 4, 5, 6];
    } else {
      headers = [
        'Année',
        'Base d\'amortissement',
        'Taux',
        'Annuités',
        'Annuités cumulées',
        'VNC',
      ];
      numericColumns = [1, 3, 4, 5];
      percentageColumns = [2];
      currencyColumns = [1, 3, 4, 5];
    }

    // Convert rows to string data
    final tableRows = rows.map((row) {
      if (isDegressive) {
        return [
          row.yearLabel,
          row.baseAmount.toStringAsFixed(2),
          row.degressiveRate?.toStringAsFixed(2) ?? row.rate.toStringAsFixed(2),
          row.linearRate?.toStringAsFixed(2) ?? '',
          row.annuity.toStringAsFixed(2),
          row.cumulativeAnnuity.toStringAsFixed(2),
          row.netBookValue.toStringAsFixed(2),
        ];
      } else {
        return [
          row.yearLabel,
          row.baseAmount.toStringAsFixed(2),
          row.rate.toStringAsFixed(2),
          row.annuity.toStringAsFixed(2),
          row.cumulativeAnnuity.toStringAsFixed(2),
          row.netBookValue.toStringAsFixed(2),
        ];
      }
    }).toList();

    return TableData(
      name: name,
      title: title,
      headers: headers,
      rows: tableRows,
      numericColumns: numericColumns,
      percentageColumns: percentageColumns,
      currencyColumns: currencyColumns,
    );
  }

  /// Create TableData for derogatory amortization
  factory TableData.fromDerogatory(
    List<AmortizationRow> rows, {
    required String name,
    required String title,
  }) {
    if (rows.isEmpty) {
      return TableData(
        name: name,
        title: title,
        headers: [],
        rows: [],
      );
    }

    const headers = [
      'Année',
      'Amortissement comptable',
      'Amortissement fiscal',
      'Dotations',
      'Reprises',
    ];

    final tableRows = rows.map((row) {
      return [
        row.yearLabel,
        row.accountingAmortization?.toStringAsFixed(2) ?? '0.00',
        row.fiscalAmortization?.toStringAsFixed(2) ?? '0.00',
        row.derogationProvision != null && row.derogationProvision! > 0
            ? row.derogationProvision!.toStringAsFixed(2)
            : '-',
        row.derogationReprise != null && row.derogationReprise! > 0
            ? row.derogationReprise!.toStringAsFixed(2)
            : '-',
      ];
    }).toList();

    return TableData(
      name: name,
      title: title,
      headers: headers,
      rows: tableRows,
      numericColumns: [1, 2, 3, 4],
      currencyColumns: [1, 2, 3, 4],
    );
  }
}

/// Main export data structure for amortization
class AmortizationExportData {
  final String assetName;
  final double originalValue;
  final double residualValue;
  final int duration;
  final String mode;
  final double rate;
  final DateTime acquisitionDate;
  final List<AmortizationRow> amortizationRows;
  final List<AmortizationRow>? derogatoryRows;
  final ExportSettings settings;
  final Map<String, dynamic>? additionalMetadata;

  const AmortizationExportData({
    required this.assetName,
    required this.originalValue,
    required this.residualValue,
    required this.duration,
    required this.mode,
    required this.rate,
    required this.acquisitionDate,
    required this.amortizationRows,
    this.derogatoryRows,
    this.settings = const ExportSettings(),
    this.additionalMetadata,
  });

  /// Get main table data
  TableData get mainTableData => TableData.fromAmortizationRows(
        amortizationRows,
        name: 'amortization',
        title: 'Tableau d\'amortissement',
        isDegressive: mode.toLowerCase() == 'degressif',
      );

  /// Get derogatory table data if available
  TableData? get derogatoryTableData => derogatoryRows != null
      ? TableData.fromDerogatory(
          derogatoryRows!,
          name: 'derogatory',
          title: 'Amortissement dérogatoire',
        )
      : null;

  /// Get summary data
  Map<String, dynamic> get summaryData => {
        'Nom de l\'immobilisation': assetName,
        'Valeur d\'origine': originalValue,
        'Valeur résiduelle': residualValue,
        'Durée d\'amortissement': '$duration ans',
        'Mode d\'amortissement': mode,
        'Taux d\'amortissement': '$rate%',
        'Date d\'acquisition': '${acquisitionDate.day.toString().padLeft(2, '0')}/${acquisitionDate.month.toString().padLeft(2, '0')}/${acquisitionDate.year}',
        'Total amortissements': amortizationRows.isNotEmpty 
            ? amortizationRows.last.cumulativeAnnuity 
            : 0.0,
        'Valeur nette finale': amortizationRows.isNotEmpty 
            ? amortizationRows.last.netBookValue 
            : originalValue,
      };
}

/// Export data structure for comparison
class ComparisonExportData {
  final String title;
  final TableData primaryTable;
  final TableData comparisonTable;
  final String primaryTitle;
  final String comparisonTitle;
  final List<List<String>>? differenceData;
  final ExportSettings settings;

  const ComparisonExportData({
    required this.title,
    required this.primaryTable,
    required this.comparisonTable,
    required this.primaryTitle,
    required this.comparisonTitle,
    this.differenceData,
    this.settings = const ExportSettings(),
  });

  /// Calculate differences between tables
  List<List<String>> calculateDifferences() {
    if (primaryTable.rows.length != comparisonTable.rows.length ||
        primaryTable.headers.length != comparisonTable.headers.length) {
      return [];
    }

    final differences = <List<String>>[];
    
    for (int i = 0; i < primaryTable.rows.length; i++) {
      final diffRow = <String>[];
      final primaryRow = primaryTable.rows[i];
      final comparisonRow = comparisonTable.rows[i];
      
      for (int j = 0; j < primaryRow.length; j++) {
        if (j == 0) {
          // Year column - copy as is
          diffRow.add(primaryRow[j]);
        } else {
          // Numeric columns - calculate difference
          final primary = double.tryParse(primaryRow[j]) ?? 0.0;
          final comparison = double.tryParse(comparisonRow[j]) ?? 0.0;
          final diff = comparison - primary;
          diffRow.add(diff.toStringAsFixed(2));
        }
      }
      differences.add(diffRow);
    }
    
    return differences;
  }
}

/// Enhanced Excel export utilities
class ExcelExportUtils {
  static const String _defaultFontFamily = 'Calibri';
  static const double _defaultFontSize = 11.0;
  static const double _headerFontSize = 12.0;
  static const double _titleFontSize = 14.0;

  /// Main export function for amortization data
  static Future<File> exportAmortizationToExcel(AmortizationExportData data) async {
    try {
      final excel = Excel.createExcel();
      
      // Remove default sheet
      excel.delete('Sheet1');
      
      // Create main amortization sheet
      final mainSheet = excel['Amortissement'];
      await _setupAmortizationSheet(mainSheet, data);
      
      // Create derogatory sheet if data exists
      if (data.derogatoryTableData != null) {
        final derogatorySheet = excel['Dérogatoire'];
        await _setupDerogatorySheet(derogatorySheet, data);
      }
      
      // Create summary sheet if enabled
      if (data.settings.includeSummary) {
        final summarySheet = excel['Résumé'];
        await _setupSummarySheet(summarySheet, data);
      }
      
      // Generate filename and save
      final fileName = _generateFileName('amortissement', data.assetName);
      return await _saveExcelFile(excel, fileName);
      
    } catch (e) {
      throw Exception('Erreur lors de l\'export Excel: $e');
    }
  }

  /// Export comparison data to Excel
  static Future<File> exportComparisonToExcel(ComparisonExportData data) async {
    try {
      final excel = Excel.createExcel();
      excel.delete('Sheet1');
      
      // Create comparison sheet
      final comparisonSheet = excel['Comparaison'];
      await _setupComparisonSheet(comparisonSheet, data);
      
      // Create differences sheet
      final differencesSheet = excel['Différences'];
      await _setupDifferencesSheet(differencesSheet, data);
      
      final fileName = _generateFileName('comparaison', data.title);
      return await _saveExcelFile(excel, fileName);
      
    } catch (e) {
      throw Exception('Erreur lors de l\'export de comparaison: $e');
    }
  }

  /// Export multiple tables to separate sheets
  static Future<File> exportMultipleTablesExcel(
    List<TableData> tables, {
    ExportSettings settings = const ExportSettings(),
    String? fileName,
  }) async {
    try {
      final excel = Excel.createExcel();
      excel.delete('Sheet1');
      
      for (final table in tables) {
        final sheet = excel[table.name];
        await _setupTableSheet(sheet, table, settings);
      }
      
      final finalFileName = fileName ?? _generateFileName('export_multiple', 'tables');
      return await _saveExcelFile(excel, finalFileName);
      
    } catch (e) {
      throw Exception('Erreur lors de l\'export multiple: $e');
    }
  }

  /// Setup main amortization sheet
  static Future<void> _setupAmortizationSheet(
    Sheet sheet,
    AmortizationExportData data,
  ) async {
    int currentRow = 0;
    
    // Add title and metadata
    currentRow = await _addSheetHeader(
      sheet,
      data.mainTableData.title,
      data.summaryData,
      data.settings,
      currentRow,
    );
    
    // Add main table
    currentRow = await _addTable(
      sheet,
      data.mainTableData,
      data.settings,
      currentRow,
    );
    
    // Setup print area and freeze panes
    if (data.settings.freezePanes) {
      // Freeze panes at header row
      // Note: Excel package doesn't directly support freeze panes
      // This would need to be implemented with raw Excel XML manipulation
    }
  }

  /// Setup derogatory amortization sheet
  static Future<void> _setupDerogatorySheet(
    Sheet sheet,
    AmortizationExportData data,
  ) async {
    if (data.derogatoryTableData == null) return;
    
    int currentRow = 0;
    
    // Add title
    currentRow = await _addSheetHeader(
      sheet,
      data.derogatoryTableData!.title,
      data.summaryData,
      data.settings,
      currentRow,
    );
    
    // Add derogatory table
    currentRow = await _addTable(
      sheet,
      data.derogatoryTableData!,
      data.settings,
      currentRow,
    );
  }

  /// Setup summary sheet
  static Future<void> _setupSummarySheet(
    Sheet sheet,
    AmortizationExportData data,
  ) async {
    int currentRow = 0;
    
    // Add title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    titleCell.value = TextCellValue('Résumé de l\'amortissement');
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _titleFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: HorizontalAlign.Center,
    );
    currentRow += 2;
    
    // Add summary data
    for (final entry in data.summaryData.entries) {
      // Label column
      final labelCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: 0,
        rowIndex: currentRow,
      ));
      labelCell.value = TextCellValue(entry.key);
      labelCell.cellStyle = CellStyle(
        bold: true,
        fontFamily: _defaultFontFamily,
        fontSize: _defaultFontSize,
      );
      
      // Value column
      final valueCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: 1,
        rowIndex: currentRow,
      ));
      
      if (entry.value is double) {
        valueCell.value = DoubleCellValue(entry.value as double);
        valueCell.cellStyle = CellStyle(
          fontFamily: _defaultFontFamily,
          fontSize: _defaultFontSize,
          horizontalAlign: HorizontalAlign.Right,
        );
      } else {
        valueCell.value = TextCellValue(entry.value.toString());
        valueCell.cellStyle = CellStyle(
          fontFamily: _defaultFontFamily,
          fontSize: _defaultFontSize,
        );
      }
      
      currentRow++;
    }
    
    // Add calculation formulas if enabled
    if (data.settings.includeFormulas) {
      currentRow += 2;
      await _addCalculationFormulas(sheet, data, currentRow);
    }
  }

  /// Setup comparison sheet
  static Future<void> _setupComparisonSheet(
    Sheet sheet,
    ComparisonExportData data,
  ) async {
    int currentRow = 0;
    
    // Add title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    titleCell.value = TextCellValue(data.title);
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _titleFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: HorizontalAlign.Center,
    );
    currentRow += 2;
    
    // Calculate column positions
    final primaryStartCol = 0;
    final primaryEndCol = data.primaryTable.headers.length - 1;
    final comparisonStartCol = primaryEndCol + 2;
    
    // Add primary table title
    final primaryTitleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: primaryStartCol,
      rowIndex: currentRow,
    ));
    primaryTitleCell.value = TextCellValue(data.primaryTitle);
    primaryTitleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _headerFontSize,
      fontFamily: _defaultFontFamily,
    );
    
    // Add comparison table title
    final comparisonTitleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: comparisonStartCol,
      rowIndex: currentRow,
    ));
    comparisonTitleCell.value = TextCellValue(data.comparisonTitle);
    comparisonTitleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _headerFontSize,
      fontFamily: _defaultFontFamily,
    );
    currentRow += 2;
    
    // Add headers for both tables
    await _addComparisonHeaders(sheet, data, currentRow);
    currentRow++;
    
    // Add data rows
    await _addComparisonData(sheet, data, currentRow);
  }

  /// Setup differences sheet
  static Future<void> _setupDifferencesSheet(
    Sheet sheet,
    ComparisonExportData data,
  ) async {
    final differences = data.differenceData ?? data.calculateDifferences();
    if (differences.isEmpty) return;
    
    int currentRow = 0;
    
    // Add title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    titleCell.value = TextCellValue('Différences (${data.comparisonTitle} - ${data.primaryTitle})');
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _titleFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: HorizontalAlign.Center,
    );
    currentRow += 2;
    
    // Add headers
    for (int col = 0; col < data.primaryTable.headers.length; col++) {
      final headerCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: col,
        rowIndex: currentRow,
      ));
      headerCell.value = TextCellValue(data.primaryTable.headers[col]);
      headerCell.cellStyle = _getHeaderCellStyle();
    }
    currentRow++;
    
    // Add difference data
    for (int row = 0; row < differences.length; row++) {
      for (int col = 0; col < differences[row].length; col++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: col,
          rowIndex: currentRow + row,
        ));
        
        final value = differences[row][col];
        if (col == 0) {
          // Year column
          cell.value = TextCellValue(value);
          cell.cellStyle = CellStyle(
            fontFamily: _defaultFontFamily,
            fontSize: _defaultFontSize,
          );
        } else {
          // Numeric columns
          final numValue = double.tryParse(value) ?? 0.0;
          cell.value = DoubleCellValue(numValue);
          
          // Color code positive/negative differences
          final cellStyle = CellStyle(
            fontFamily: _defaultFontFamily,
            fontSize: _defaultFontSize,
            horizontalAlign: HorizontalAlign.Right,
          );
          
          if (numValue > 0) {
            cellStyle.fontColor = HexColor.fromHex('#008000'); // Green
          } else if (numValue < 0) {
            cellStyle.fontColor = HexColor.fromHex('#FF0000'); // Red
          }
          
          cell.cellStyle = cellStyle;
        }
      }
    }
  }

  /// Add sheet header with title and metadata
  static Future<int> _addSheetHeader(
    Sheet sheet,
    String title,
    Map<String, dynamic> metadata,
    ExportSettings settings,
    int startRow,
  ) async {
    int currentRow = startRow;
    
    // Add company name if provided
    if (settings.companyName != null) {
      final companyCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: 0,
        rowIndex: currentRow,
      ));
      companyCell.value = TextCellValue(settings.companyName!);
      companyCell.cellStyle = CellStyle(
        bold: true,
        fontSize: _headerFontSize,
        fontFamily: _defaultFontFamily,
      );
      currentRow++;
    }
    
    // Add title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    titleCell.value = TextCellValue(title);
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _titleFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: HorizontalAlign.Center,
    );
    currentRow += 2;
    
    // Add generation date
    final dateCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    final now = DateTime.now();
    dateCell.value = TextCellValue(
      'Généré le ${now.day.toString().padLeft(2, '0')}/${now.month.toString().padLeft(2, '0')}/${now.year} à ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}'
    );
    dateCell.cellStyle = CellStyle(
      fontFamily: _defaultFontFamily,
      fontSize: _defaultFontSize - 1,
      fontColor: HexColor.fromHex('#666666'),
    );
    currentRow += 2;
    
    return currentRow;
  }

  /// Add table to sheet
  static Future<int> _addTable(
    Sheet sheet,
    TableData tableData,
    ExportSettings settings,
    int startRow,
  ) async {
    int currentRow = startRow;
    
    // Add headers
    for (int col = 0; col < tableData.headers.length; col++) {
      final headerCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: col,
        rowIndex: currentRow,
      ));
      headerCell.value = TextCellValue(tableData.headers[col]);
      headerCell.cellStyle = _getHeaderCellStyle();
    }
    currentRow++;
    
    // Add data rows
    for (int row = 0; row < tableData.rows.length; row++) {
      final isEvenRow = row % 2 == 0;
      
      for (int col = 0; col < tableData.rows[row].length; col++) {
        final cell = sheet.cell(CellIndex.indexByColumnRow(
          columnIndex: col,
          rowIndex: currentRow + row,
        ));
        
        final value = tableData.rows[row][col];
        final cellStyle = _getDataCellStyle(
          isEvenRow: isEvenRow,
          useAlternatingColors: settings.useAlternatingRowColors,
          isNumeric: tableData.numericColumns?.contains(col) ?? false,
          isPercentage: tableData.percentageColumns?.contains(col) ?? false,
          isCurrency: tableData.currencyColumns?.contains(col) ?? false,
        );
        
        // Set cell value and format
        if (tableData.numericColumns?.contains(col) ?? false) {
          final numValue = double.tryParse(value) ?? 0.0;
          cell.value = DoubleCellValue(numValue);
        } else {
          cell.value = TextCellValue(value);
        }
        
        cell.cellStyle = cellStyle;
      }
    }
    
    // Set column widths
    await _setColumnWidths(sheet, tableData);
    
    return currentRow + tableData.rows.length + 1;
  }

  /// Setup table sheet for multiple tables export
  static Future<void> _setupTableSheet(
    Sheet sheet,
    TableData tableData,
    ExportSettings settings,
  ) async {
    int currentRow = 0;
    
    // Add title
    final titleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    titleCell.value = TextCellValue(tableData.title);
    titleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _titleFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: HorizontalAlign.Center,
    );
    currentRow += 2;
    
    // Add table
    await _addTable(sheet, tableData, settings, currentRow);
  }

  /// Add comparison headers
  static Future<void> _addComparisonHeaders(
    Sheet sheet,
    ComparisonExportData data,
    int row,
  ) async {
    // Primary table headers
    for (int col = 0; col < data.primaryTable.headers.length; col++) {
      final headerCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: col,
        rowIndex: row,
      ));
      headerCell.value = TextCellValue(data.primaryTable.headers[col]);
      headerCell.cellStyle = _getHeaderCellStyle();
    }
    
    // Comparison table headers
    final comparisonStartCol = data.primaryTable.headers.length + 2;
    for (int col = 0; col < data.comparisonTable.headers.length; col++) {
      final headerCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: comparisonStartCol + col,
        rowIndex: row,
      ));
      headerCell.value = TextCellValue(data.comparisonTable.headers[col]);
      headerCell.cellStyle = _getHeaderCellStyle();
    }
  }

  /// Add comparison data
  static Future<void> _addComparisonData(
    Sheet sheet,
    ComparisonExportData data,
    int startRow,
  ) async {
    final maxRows = [data.primaryTable.rows.length, data.comparisonTable.rows.length].reduce((a, b) => a > b ? a : b);
    
    for (int row = 0; row < maxRows; row++) {
      final isEvenRow = row % 2 == 0;
      
      // Primary table data
      if (row < data.primaryTable.rows.length) {
        for (int col = 0; col < data.primaryTable.rows[row].length; col++) {
          final cell = sheet.cell(CellIndex.indexByColumnRow(
            columnIndex: col,
            rowIndex: startRow + row,
          ));
          
          final value = data.primaryTable.rows[row][col];
          final cellStyle = _getDataCellStyle(
            isEvenRow: isEvenRow,
            useAlternatingColors: data.settings.useAlternatingRowColors,
            isNumeric: data.primaryTable.numericColumns?.contains(col) ?? false,
          );
          
          if (data.primaryTable.numericColumns?.contains(col) ?? false) {
            final numValue = double.tryParse(value) ?? 0.0;
            cell.value = DoubleCellValue(numValue);
          } else {
            cell.value = TextCellValue(value);
          }
          
          cell.cellStyle = cellStyle;
        }
      }
      
      // Comparison table data
      if (row < data.comparisonTable.rows.length) {
        final comparisonStartCol = data.primaryTable.headers.length + 2;
        for (int col = 0; col < data.comparisonTable.rows[row].length; col++) {
          final cell = sheet.cell(CellIndex.indexByColumnRow(
            columnIndex: comparisonStartCol + col,
            rowIndex: startRow + row,
          ));
          
          final value = data.comparisonTable.rows[row][col];
          final cellStyle = _getDataCellStyle(
            isEvenRow: isEvenRow,
            useAlternatingColors: data.settings.useAlternatingRowColors,
            isNumeric: data.comparisonTable.numericColumns?.contains(col) ?? false,
          );
          
          if (data.comparisonTable.numericColumns?.contains(col) ?? false) {
            final numValue = double.tryParse(value) ?? 0.0;
            cell.value = DoubleCellValue(numValue);
          } else {
            cell.value = TextCellValue(value);
          }
          
          cell.cellStyle = cellStyle;
        }
      }
    }
  }

  /// Add calculation formulas
  static Future<void> _addCalculationFormulas(
    Sheet sheet,
    AmortizationExportData data,
    int startRow,
  ) async {
    int currentRow = startRow;
    
    // Add formulas section title
    final formulasTitleCell = sheet.cell(CellIndex.indexByColumnRow(
      columnIndex: 0,
      rowIndex: currentRow,
    ));
    formulasTitleCell.value = TextCellValue('Formules de vérification');
    formulasTitleCell.cellStyle = CellStyle(
      bold: true,
      fontSize: _headerFontSize,
      fontFamily: _defaultFontFamily,
    );
    currentRow += 2;
    
    // Add verification formulas
    final formulas = [
      'Total des annuités',
      'Valeur amortissable',
      'Taux moyen effectif',
    ];
    
    for (final formula in formulas) {
      final labelCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: 0,
        rowIndex: currentRow,
      ));
      labelCell.value = TextCellValue(formula);
      labelCell.cellStyle = CellStyle(
        fontFamily: _defaultFontFamily,
        fontSize: _defaultFontSize,
      );
      
      // Add corresponding formula (simplified for this example)
      final formulaCell = sheet.cell(CellIndex.indexByColumnRow(
        columnIndex: 1,
        rowIndex: currentRow,
      ));
      formulaCell.value = TextCellValue('=SUM(...)'); // Placeholder
      formulaCell.cellStyle = CellStyle(
        fontFamily: _defaultFontFamily,
        fontSize: _defaultFontSize,
        horizontalAlign: HorizontalAlign.Right,
      );
      
      currentRow++;
    }
  }

  /// Get header cell style
  static CellStyle _getHeaderCellStyle() {
    return CellStyle(
      bold: true,
      fontSize: _headerFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: HorizontalAlign.Center,
      verticalAlign: VerticalAlign.Center,
      backgroundColorHex: HexColor.fromHex('#4472C4'),
      fontColorHex: HexColor.fromHex('#FFFFFF'),
    );
  }

  /// Get data cell style
  static CellStyle _getDataCellStyle({
    required bool isEvenRow,
    required bool useAlternatingColors,
    required bool isNumeric,
    bool isPercentage = false,
    bool isCurrency = false,
  }) {
    String? backgroundColor;
    if (useAlternatingColors && !isEvenRow) {
      backgroundColor = '#F2F2F2';
    }
    
    return CellStyle(
      fontSize: _defaultFontSize,
      fontFamily: _defaultFontFamily,
      horizontalAlign: isNumeric ? HorizontalAlign.Right : HorizontalAlign.Left,
      verticalAlign: VerticalAlign.Center,
      backgroundColorHex: backgroundColor != null ? HexColor.fromHex(backgroundColor) : null,
    );
  }

  /// Set column widths based on content
  static Future<void> _setColumnWidths(Sheet sheet, TableData tableData) async {
    for (int col = 0; col < tableData.headers.length; col++) {
      double width = 15.0; // Default width
      
      // Adjust width based on header length
      final headerLength = tableData.headers[col].length;
      if (headerLength > 15) {
        width = 20.0;
      } else if (headerLength > 10) {
        width = 18.0;
      }
      
      // Adjust for numeric columns
      if (tableData.numericColumns?.contains(col) ?? false) {
        width = 16.0;
      }
      
      sheet.setColumnWidth(col, width);
    }
  }

  /// Generate unique filename with timestamp
  static String _generateFileName(String prefix, String? suffix) {
    final now = DateTime.now();
    final timestamp = '${now.year}${now.month.toString().padLeft(2, '0')}${now.day.toString().padLeft(2, '0')}_${now.hour.toString().padLeft(2, '0')}${now.minute.toString().padLeft(2, '0')}';
    
    String fileName = '${prefix}_$timestamp';
    if (suffix != null && suffix.isNotEmpty) {
      // Clean suffix for filename
      final cleanSuffix = suffix.replaceAll(RegExp(r'[^\w\s-]'), '').replaceAll(' ', '_');
      fileName += '_$cleanSuffix';
    }
    
    return '$fileName.xlsx';
  }

  /// Save Excel file to documents directory
  static Future<File> _saveExcelFile(Excel excel, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final file = File('${directory.path}/$fileName');
    
    final excelBytes = excel.encode();
    if (excelBytes == null) {
      throw Exception('Impossible d\'encoder le fichier Excel');
    }
    
    await file.writeAsBytes(excelBytes);
    return file;
  }

  /// Utility method to convert Map<String, String> tables to TableData
  static TableData convertLegacyTable(
    List<Map<String, String>> legacyTable,
    String name,
    String title,
  ) {
    if (legacyTable.isEmpty) {
      return TableData(
        name: name,
        title: title,
        headers: [],
        rows: [],
      );
    }
    
    final headers = legacyTable.first.keys.toList();
    final rows = legacyTable.map((row) => headers.map((header) => row[header] ?? '').toList()).toList();
    
    // Detect numeric columns
    final numericColumns = <int>[];
    final percentageColumns = <int>[];
    final currencyColumns = <int>[];
    
    for (int col = 0; col < headers.length; col++) {
      final header = headers[col].toLowerCase();
      if (header.contains('taux') || header.contains('%')) {
        percentageColumns.add(col);
        numericColumns.add(col);
      } else if (header.contains('annuité') || header.contains('base') || header.contains('vnc') || header.contains('valeur')) {
        currencyColumns.add(col);
        numericColumns.add(col);
      }
    }
    
    return TableData(
      name: name,
      title: title,
      headers: headers,
      rows: rows,
      numericColumns: numericColumns,
      percentageColumns: percentageColumns,
      currencyColumns: currencyColumns,
    );
  }

  /// Export legacy table format (for backward compatibility)
  static Future<File> exportLegacyTable(
    List<Map<String, String>> table,
    String type, {
    ExportSettings settings = const ExportSettings(),
  }) async {
    final tableData = convertLegacyTable(table, type, 'Tableau $type');
    return await exportMultipleTablesExcel([tableData], settings: settings);
  }
}