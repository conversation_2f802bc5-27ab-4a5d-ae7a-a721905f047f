import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:provider/provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart' as riverpod;
import 'package:window_manager/window_manager.dart';
import 'dart:io';

import 'package:hive_flutter/hive_flutter.dart'; // Added for Hive

// Import Hive models and adapters
import 'models/hive/user_profile.dart';
import 'models/hive/quiz_attempt.dart';
import 'models/hive/user_progress.dart';
import 'models/hive/active_quiz_state.dart'; // Import quiz active state model
import 'models/exam/exam_attempt.dart'; // Import ExamAttempt model
import 'models/exam/active_exam_state.dart'; // Import exam active state model

// Import the services and providers
import 'services/user_progress_service.dart';
import 'providers/user_progress_provider.dart';
import 'services/quiz_data_service.dart'; // Import QuizDataService
import 'providers/quiz_data_provider.dart'; // Import QuizDataProvider
import 'services/app_version_service.dart'; // Import AppVersionService

import 'screens/home_screen.dart';
import 'screens/splash_screen.dart';
import 'screens/quiz/quiz_screen.dart';
import 'screens/profile/profile_screen.dart'; // Import ProfileScreen
import 'screens/exam/exam_list_screen.dart'; // Import ExamListScreen
import 'services/theme_service.dart';



void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  if (Platform.isWindows) {
    await windowManager.ensureInitialized();

    WindowOptions windowOptions = const WindowOptions(
      size: Size(1280, 720),
      minimumSize: Size(800, 600),
      center: true,
      backgroundColor: Colors.transparent,
      skipTaskbar: false,
      titleBarStyle: TitleBarStyle.hidden,
    );

    await windowManager.waitUntilReadyToShow(windowOptions, () async {
      await windowManager.show();
      await windowManager.focus();
    });
  }

  final themeService = ThemeService();
  await themeService.init();
  
  // Initialize AppVersionService
  final appVersionService = AppVersionService();
  await appVersionService.init();

  // Initialize Hive
  await Hive.initFlutter();

  // Register Hive Adapters
  Hive.registerAdapter(UserProfileAdapter());
  Hive.registerAdapter(QuizAttemptAdapter());
  Hive.registerAdapter(UserProgressAdapter());
  Hive.registerAdapter(ActiveQuizStateAdapter()); // Register quiz active state adapter
  Hive.registerAdapter(ExamAttemptAdapter()); // Register ExamAttempt adapter
  Hive.registerAdapter(ActiveExamStateAdapter()); // Register exam active state adapter

  // Open Hive boxes (Handled within the service now)

  // Initialize UserProgressService
  final userProgressService = UserProgressService();
  await userProgressService.init(); 

  // Initialize QuizDataService
  final quizDataService = QuizDataService();
  await quizDataService.loadQuizData(); // Load data on startup


  runApp(
    riverpod.ProviderScope(
      overrides: [
        // Provide the initialized service instances
        userProgressServiceProvider.overrideWithValue(userProgressService),
        quizDataServiceProvider.overrideWithValue(quizDataService), // Provide QuizDataService
      ],
      child: MultiProvider(
        providers: [
          ChangeNotifierProvider.value(value: themeService),
          ChangeNotifierProvider.value(value: appVersionService), // Provide AppVersionService
          // Consider providing UserProgressService via Provider if needed by non-Riverpod widgets
          // Provider.value(value: userProgressService), 
        ],
        child: const MoroccanAccountingApp(),
      ),
    ),
  );
}

class MoroccanAccountingApp extends StatelessWidget {
  const MoroccanAccountingApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        final robotoTextTheme = GoogleFonts.robotoTextTheme(
          Theme.of(context).textTheme,
        );

        return MaterialApp(
          title: 'Comptabilité Marocaine',
          theme: themeService.theme.copyWith(
            textTheme: robotoTextTheme,
          ),
          initialRoute: '/',
          routes: {
            '/': (context) => const SplashScreen(),
            '/home': (context) => const HomeScreen(),
            '/quiz': (context) => const QuizScreen(),
            '/profile': (context) => const ProfileScreen(), // Add profile route
            '/exam_list': (context) => const ExamListScreen(), // Add exam list route
          },
        );
      },
    );
  }
}
