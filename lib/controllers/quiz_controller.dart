import 'dart:async';
// Ensure Timer is imported
// For debugPrint
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:freezed_annotation/freezed_annotation.dart';
import '../models/quiz_model.dart';
import '../models/hive/active_quiz_state.dart'; // Import active state model

part 'quiz_controller.freezed.dart';

@freezed
abstract class QuizState with _$QuizState {
  const factory QuizState({
    required int currentQuestionIndex,
    required int score,
    required List<int> userAnswers,
    required bool showExplanation,
    required int timeRemaining,
    required double progressValue,
    required bool isCompleted,
    required int totalPoints,
    required int earnedPoints,
    required List<int> timeSpentPerQuestion,
    required int streakCount,
    required bool isSavingResults,
  }) = _QuizState;

  factory QuizState.initial() => const QuizState(
        currentQuestionIndex: 0,
        score: 0,
        userAnswers: [],
        showExplanation: false,
        timeRemaining: 30,
        progressValue: 1.0,
        isCompleted: false,
        totalPoints: 0,
        earnedPoints: 0,
        timeSpentPerQuestion: [],
        streakCount: 0,
        isSavingResults: false,
      );
}

class QuizController extends StateNotifier<QuizState> {
  final QuizLevel level;
  final String categoryName;
  // Removed: final QuizProgressService _progressService = QuizProgressService();
  Timer? _questionTimer;
  int _startTime = 0;
  final ActiveQuizState? initialActiveState; // Optional initial state

  QuizController(this.level, this.categoryName, this.initialActiveState) 
      : super(initialActiveState != null 
              ? QuizState( // Initialize from saved state
                  currentQuestionIndex: initialActiveState.currentQuestionIndex,
                  score: initialActiveState.score,
                  userAnswers: List.from(initialActiveState.userAnswers), // Ensure mutable list
                  showExplanation: false, // Always start without explanation showing
                  timeRemaining: 30, // Will be reset by timer start
                  progressValue: 1.0, // Will be reset by timer start
                  isCompleted: false,
                  totalPoints: 0, // Will be calculated below
                  earnedPoints: initialActiveState.earnedPoints,
                  timeSpentPerQuestion: List.from(initialActiveState.timeSpentPerQuestion), // Ensure mutable list
                  streakCount: initialActiveState.streakCount,
                  isSavingResults: false,
                )
              : QuizState.initial() // Initialize fresh
            ) {
    // Calculate total possible points (always needed)
    int totalPoints = 0;
    for (var question in level.questions) {
      totalPoints += level.pointsPerQuestion * question.difficulty;
    }
    
    state = state.copyWith(
      totalPoints: totalPoints,
      timeRemaining: level.timePerQuestion,
    );
    
    _startQuestionTimer();
  }

  void _startQuestionTimer() {
    _questionTimer?.cancel();
    _startTime = level.timePerQuestion;
    
    state = state.copyWith(
      timeRemaining: level.timePerQuestion,
      progressValue: 1.0,
    );

    _questionTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (state.timeRemaining > 0 && !state.showExplanation) {
        state = state.copyWith(
          timeRemaining: state.timeRemaining - 1,
          progressValue: (state.timeRemaining - 1) / _startTime,
        );
      } else if (state.timeRemaining == 0 && !state.showExplanation) {
        handleAnswer(-1); // Time's up, mark as incorrect
      }
    });
  }

  void handleAnswer(int selectedOption) {
    if (state.showExplanation) return;

    final currentQuestion = level.questions[state.currentQuestionIndex];
    final bool isCorrect = selectedOption == currentQuestion.correct;
    _questionTimer?.cancel();
    
    // Calculate time spent on this question
    final timeSpent = level.timePerQuestion - state.timeRemaining;
    final List<int> updatedTimeSpent = [...state.timeSpentPerQuestion, timeSpent];
    
    // Calculate points based on difficulty and time
    int pointsEarned = 0;
    int newStreakCount = 0;
    
    if (isCorrect) {
      // Base points from difficulty
      pointsEarned = level.pointsPerQuestion * currentQuestion.difficulty;
      
      // Time bonus (faster answers get more points)
      final timeRatio = state.timeRemaining / level.timePerQuestion;
      if (timeRatio > 0.7) {
        pointsEarned = (pointsEarned * 1.5).round(); // 50% bonus for very fast answers
      } else if (timeRatio > 0.4) {
        pointsEarned = (pointsEarned * 1.2).round(); // 20% bonus for moderately fast answers
      }
      
      // Streak bonus
      newStreakCount = state.streakCount + 1;
      if (newStreakCount >= 3) {
        pointsEarned = (pointsEarned * (1 + (newStreakCount * 0.1))).round(); // 10% bonus per streak
      }
    } else {
      newStreakCount = 0; // Reset streak on wrong answer
    }

    state = state.copyWith(
      userAnswers: [...state.userAnswers, selectedOption],
      score: isCorrect ? state.score + 1 : state.score,
      showExplanation: true,
      earnedPoints: state.earnedPoints + pointsEarned,
      timeSpentPerQuestion: updatedTimeSpent,
      streakCount: newStreakCount,
    );
  }

  void nextQuestion() {
    if (state.currentQuestionIndex < level.questions.length - 1) {
      state = state.copyWith(
        currentQuestionIndex: state.currentQuestionIndex + 1,
        showExplanation: false,
        // Reset selected answer for the new question
        userAnswers: List.from(state.userAnswers)..add(-1),  // Add -1 as placeholder for new question
      );
      _startQuestionTimer();
    } else {
      // Quiz is completed, state is handled by the screen showing results
      state = state.copyWith(isCompleted: true);
      // Removed call to _saveQuizResults(); - Saving is handled in QuizQuestionScreen
    }
  }
  
  // Removed: _saveQuizResults() method

  @override
  void dispose() {
    _questionTimer?.cancel();
    super.dispose();
  }
}

// Update the family parameter type to include the optional ActiveQuizState
final quizControllerProvider = StateNotifierProvider.autoDispose.family<QuizController, QuizState, (QuizLevel, String, ActiveQuizState?)>(
  (ref, params) {
    // params is now a tuple: (QuizLevel level, String categoryName, ActiveQuizState? initialState)
    return QuizController(params.$1, params.$2, params.$3); 
  },
);
