import 'package:intl/intl.dart';

// Import dependencies that will be created in other files
import 'tax_bracket.dart';
import 'is_input_data.dart';

/// Model class to hold IS (Impôt sur les Sociétés) calculation results
/// 
/// This class structures calculation outputs and enables easy comparison
/// between different tax regimes for Moroccan corporate tax calculations.
class IsCalculationResult {
  /// The calculated taxable result after reintegrations and deductions
  final double taxableResult;
  
  /// The calculated IS (Impôt sur les Sociétés) amount
  final double isAmount;
  
  /// The calculated CM (Cotisation Minimale) amount
  final double cmAmount;
  
  /// The total payable amount (IS + CM)
  final double totalPayable;
  
  /// The name of the applied tax regime
  final String regimeName;
  
  /// The applied tax rate (for fixed rate regimes)
  final double appliedRate;
  
  /// The type of rate applied: 'progressive' or 'fixed'
  final String rateType;
  
  /// List of tax brackets applied for progressive regimes
  final List<TaxBracket> appliedBrackets;
  
  /// Breakdown of reintegration amounts by category
  final Map<String, double> reintegrationBreakdown;
  
  /// The effective tax rate as a percentage
  final double effectiveRate;
  
  /// Fiscal analysis text providing insights about the calculation
  final String fiscalAnalysis;
  
  /// List of recommendations for tax optimization
  final List<String> recommendations;

  /// Creates a new IS calculation result
  const IsCalculationResult({
    required this.taxableResult,
    required this.isAmount,
    required this.cmAmount,
    required this.totalPayable,
    required this.regimeName,
    required this.appliedRate,
    required this.rateType,
    required this.appliedBrackets,
    required this.reintegrationBreakdown,
    required this.effectiveRate,
    required this.fiscalAnalysis,
    required this.recommendations,
  });

  /// Creates an empty result for initialization
  factory IsCalculationResult.empty() {
    return const IsCalculationResult(
      taxableResult: 0.0,
      isAmount: 0.0,
      cmAmount: 0.0,
      totalPayable: 0.0,
      regimeName: '',
      appliedRate: 0.0,
      rateType: 'fixed',
      appliedBrackets: [],
      reintegrationBreakdown: {},
      effectiveRate: 0.0,
      fiscalAnalysis: '',
      recommendations: [],
    );
  }

  /// Creates an IsCalculationResult from JSON data
  factory IsCalculationResult.fromJson(Map<String, dynamic> json) {
    return IsCalculationResult(
      taxableResult: (json['taxableResult'] as num?)?.toDouble() ?? 0.0,
      isAmount: (json['isAmount'] as num?)?.toDouble() ?? 0.0,
      cmAmount: (json['cmAmount'] as num?)?.toDouble() ?? 0.0,
      totalPayable: (json['totalPayable'] as num?)?.toDouble() ?? 0.0,
      regimeName: json['regimeName'] as String? ?? '',
      appliedRate: (json['appliedRate'] as num?)?.toDouble() ?? 0.0,
      rateType: json['rateType'] as String? ?? 'fixed',
      appliedBrackets: (json['appliedBrackets'] as List<dynamic>?)
          ?.map((bracket) => TaxBracket.fromJson(bracket as Map<String, dynamic>))
          .toList() ?? [],
      reintegrationBreakdown: Map<String, double>.from(
        (json['reintegrationBreakdown'] as Map<String, dynamic>?)?.map(
          (key, value) => MapEntry(key, (value as num).toDouble()),
        ) ?? {},
      ),
      effectiveRate: (json['effectiveRate'] as num?)?.toDouble() ?? 0.0,
      fiscalAnalysis: json['fiscalAnalysis'] as String? ?? '',
      recommendations: List<String>.from(json['recommendations'] as List<dynamic>? ?? []),
    );
  }

  /// Converts the IsCalculationResult to JSON format
  Map<String, dynamic> toJson() {
    return {
      'taxableResult': taxableResult,
      'isAmount': isAmount,
      'cmAmount': cmAmount,
      'totalPayable': totalPayable,
      'regimeName': regimeName,
      'appliedRate': appliedRate,
      'rateType': rateType,
      'appliedBrackets': appliedBrackets.map((bracket) => bracket.toJson()).toList(),
      'reintegrationBreakdown': reintegrationBreakdown,
      'effectiveRate': effectiveRate,
      'fiscalAnalysis': fiscalAnalysis,
      'recommendations': recommendations,
    };
  }

  /// Returns the IS amount formatted as Moroccan currency
  String get formattedIsAmount {
    final formatter = NumberFormat.currency(
      locale: 'fr_MA',
      symbol: 'DH',
      decimalDigits: 2,
    );
    return formatter.format(isAmount);
  }

  /// Returns the CM amount formatted as Moroccan currency
  String get formattedCmAmount {
    final formatter = NumberFormat.currency(
      locale: 'fr_MA',
      symbol: 'DH',
      decimalDigits: 2,
    );
    return formatter.format(cmAmount);
  }

  /// Returns the total payable amount formatted as Moroccan currency
  String get formattedTotalPayable {
    final formatter = NumberFormat.currency(
      locale: 'fr_MA',
      symbol: 'DH',
      decimalDigits: 2,
    );
    return formatter.format(totalPayable);
  }

  /// Returns the taxable result formatted as Moroccan currency
  String get formattedTaxableResult {
    final formatter = NumberFormat.currency(
      locale: 'fr_MA',
      symbol: 'DH',
      decimalDigits: 2,
    );
    return formatter.format(taxableResult);
  }

  /// Returns the effective rate formatted as a percentage
  String get formattedEffectiveRate {
    final formatter = NumberFormat.percentPattern('fr_MA');
    formatter.minimumFractionDigits = 2;
    formatter.maximumFractionDigits = 2;
    return formatter.format(effectiveRate / 100);
  }

  /// Returns the applied rate formatted as a percentage
  String get formattedAppliedRate {
    final formatter = NumberFormat.percentPattern('fr_MA');
    formatter.minimumFractionDigits = 2;
    formatter.maximumFractionDigits = 2;
    return formatter.format(appliedRate / 100);
  }

  /// Checks if this result represents a better tax outcome than another
  bool isBetterThan(IsCalculationResult other) {
    return totalPayable < other.totalPayable;
  }

  /// Calculates the savings compared to another result
  double savingsComparedTo(IsCalculationResult other) {
    return other.totalPayable - totalPayable;
  }

  /// Returns formatted savings compared to another result
  String formattedSavingsComparedTo(IsCalculationResult other) {
    final savings = savingsComparedTo(other);
    final formatter = NumberFormat.currency(
      locale: 'fr_MA',
      symbol: 'DH',
      decimalDigits: 2,
    );
    return formatter.format(savings);
  }

  /// Creates a copy of this result with updated values
  IsCalculationResult copyWith({
    double? taxableResult,
    double? isAmount,
    double? cmAmount,
    double? totalPayable,
    String? regimeName,
    double? appliedRate,
    String? rateType,
    List<TaxBracket>? appliedBrackets,
    Map<String, double>? reintegrationBreakdown,
    double? effectiveRate,
    String? fiscalAnalysis,
    List<String>? recommendations,
  }) {
    return IsCalculationResult(
      taxableResult: taxableResult ?? this.taxableResult,
      isAmount: isAmount ?? this.isAmount,
      cmAmount: cmAmount ?? this.cmAmount,
      totalPayable: totalPayable ?? this.totalPayable,
      regimeName: regimeName ?? this.regimeName,
      appliedRate: appliedRate ?? this.appliedRate,
      rateType: rateType ?? this.rateType,
      appliedBrackets: appliedBrackets ?? this.appliedBrackets,
      reintegrationBreakdown: reintegrationBreakdown ?? this.reintegrationBreakdown,
      effectiveRate: effectiveRate ?? this.effectiveRate,
      fiscalAnalysis: fiscalAnalysis ?? this.fiscalAnalysis,
      recommendations: recommendations ?? this.recommendations,
    );
  }

  /// Static method to compare multiple tax regimes for the same input data
  /// 
  /// This method will be implemented to work with IsCalculatorService
  /// when that service is enhanced with the new calculation methods.
  /// 
  /// Returns a list of calculation results for different regimes,
  /// sorted by total payable amount (best to worst).
  static Future<List<IsCalculationResult>> compareRegimes(
    IsInputData input,
    List<String> regimes,
  ) async {
    // This will be implemented when IsCalculatorService is enhanced
    // For now, return an empty list to maintain compilation
    // TODO: Implement with IsCalculatorService.compareRegimes()
    return [];
  }

  /// Static method to find the optimal regime from a list of results
  static IsCalculationResult findOptimal(List<IsCalculationResult> results) {
    if (results.isEmpty) {
      return IsCalculationResult.empty();
    }
    
    return results.reduce((current, next) => 
      current.totalPayable <= next.totalPayable ? current : next
    );
  }

  /// Static method to sort results by total payable amount
  static List<IsCalculationResult> sortByTotalPayable(List<IsCalculationResult> results) {
    final sortedResults = List<IsCalculationResult>.from(results);
    sortedResults.sort((a, b) => a.totalPayable.compareTo(b.totalPayable));
    return sortedResults;
  }

  @override
  String toString() {
    return 'IsCalculationResult('
        'regimeName: $regimeName, '
        'taxableResult: ${formattedTaxableResult}, '
        'isAmount: ${formattedIsAmount}, '
        'cmAmount: ${formattedCmAmount}, '
        'totalPayable: ${formattedTotalPayable}, '
        'effectiveRate: ${formattedEffectiveRate}'
        ')';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    
    return other is IsCalculationResult &&
        other.taxableResult == taxableResult &&
        other.isAmount == isAmount &&
        other.cmAmount == cmAmount &&
        other.totalPayable == totalPayable &&
        other.regimeName == regimeName &&
        other.appliedRate == appliedRate &&
        other.rateType == rateType &&
        other.effectiveRate == effectiveRate;
  }

  @override
  int get hashCode {
    return Object.hash(
      taxableResult,
      isAmount,
      cmAmount,
      totalPayable,
      regimeName,
      appliedRate,
      rateType,
      effectiveRate,
    );
  }
}