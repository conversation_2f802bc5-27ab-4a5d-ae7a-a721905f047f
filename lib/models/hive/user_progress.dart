import 'package:hive/hive.dart';
import 'user_profile.dart';
import 'quiz_attempt.dart';
import '../exam/exam_attempt.dart'; // Import ExamAttempt

part 'user_progress.g.dart'; // Generated file

@HiveType(typeId: 2) // Unique typeId for Hive
class UserProgress extends HiveObject {
  @HiveField(0)
  UserProfile userProfile;

  // Using HiveList allows for lazy loading and efficient relationship management
  @HiveField(1)
  HiveList<QuizAttempt>? quizAttempts;

  @HiveField(2) // Next available field index
  HiveList<ExamAttempt>? examAttempts;

  UserProgress({
    required this.userProfile, 
    this.quizAttempts,
    this.examAttempts, // Add to constructor
  });
}
