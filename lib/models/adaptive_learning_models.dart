import 'package:hive/hive.dart';

// part 'adaptive_learning_models.g.dart'; // Will be generated by build_runner

@HiveType(typeId: 10)
class UserPerformance extends HiveObject {
  @HiveField(0)
  final String categoryName;
  
  @HiveField(1)
  final String levelName;
  
  @HiveField(2)
  final int questionsAnswered;
  
  @HiveField(3)
  final int correctAnswers;
  
  @HiveField(4)
  final double averageTimePerQuestion;
  
  @HiveField(5)
  final List<String> weakTopics;
  
  @HiveField(6)
  final List<String> strongTopics;
  
  @HiveField(7)
  final DateTime lastUpdated;
  
  @HiveField(8)
  final int streakCount;
  
  @HiveField(9)
  final Map<String, int> topicAccuracy; // topic -> correct answers count
  
  UserPerformance({
    required this.categoryName,
    required this.levelName,
    required this.questionsAnswered,
    required this.correctAnswers,
    required this.averageTimePerQuestion,
    required this.weakTopics,
    required this.strongTopics,
    required this.lastUpdated,
    this.streakCount = 0,
    required this.topicAccuracy,
  });
  
  double get accuracy => questionsAnswered > 0 ? correctAnswers / questionsAnswered : 0.0;
  
  UserPerformance copyWith({
    String? categoryName,
    String? levelName,
    int? questionsAnswered,
    int? correctAnswers,
    double? averageTimePerQuestion,
    List<String>? weakTopics,
    List<String>? strongTopics,
    DateTime? lastUpdated,
    int? streakCount,
    Map<String, int>? topicAccuracy,
  }) {
    return UserPerformance(
      categoryName: categoryName ?? this.categoryName,
      levelName: levelName ?? this.levelName,
      questionsAnswered: questionsAnswered ?? this.questionsAnswered,
      correctAnswers: correctAnswers ?? this.correctAnswers,
      averageTimePerQuestion: averageTimePerQuestion ?? this.averageTimePerQuestion,
      weakTopics: weakTopics ?? this.weakTopics,
      strongTopics: strongTopics ?? this.strongTopics,
      lastUpdated: lastUpdated ?? this.lastUpdated,
      streakCount: streakCount ?? this.streakCount,
      topicAccuracy: topicAccuracy ?? this.topicAccuracy,
    );
  }
}

@HiveType(typeId: 11)
class LearningPath extends HiveObject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String categoryName;
  
  @HiveField(2)
  final List<String> recommendedTopics;
  
  @HiveField(3)
  final List<String> completedTopics;
  
  @HiveField(4)
  final String currentDifficulty; // beginner, intermediate, expert
  
  @HiveField(5)
  final DateTime createdAt;
  
  @HiveField(6)
  final DateTime lastAccessed;
  
  @HiveField(7)
  final int targetAccuracy; // percentage 0-100
  
  @HiveField(8)
  final bool isActive;
  
  LearningPath({
    required this.id,
    required this.categoryName,
    required this.recommendedTopics,
    required this.completedTopics,
    required this.currentDifficulty,
    required this.createdAt,
    required this.lastAccessed,
    this.targetAccuracy = 80,
    this.isActive = true,
  });
  
  double get completionPercentage {
    if (recommendedTopics.isEmpty) return 0.0;
    return completedTopics.length / recommendedTopics.length;
  }
  
  LearningPath copyWith({
    String? id,
    String? categoryName,
    List<String>? recommendedTopics,
    List<String>? completedTopics,
    String? currentDifficulty,
    DateTime? createdAt,
    DateTime? lastAccessed,
    int? targetAccuracy,
    bool? isActive,
  }) {
    return LearningPath(
      id: id ?? this.id,
      categoryName: categoryName ?? this.categoryName,
      recommendedTopics: recommendedTopics ?? this.recommendedTopics,
      completedTopics: completedTopics ?? this.completedTopics,
      currentDifficulty: currentDifficulty ?? this.currentDifficulty,
      createdAt: createdAt ?? this.createdAt,
      lastAccessed: lastAccessed ?? this.lastAccessed,
      targetAccuracy: targetAccuracy ?? this.targetAccuracy,
      isActive: isActive ?? this.isActive,
    );
  }
}

@HiveType(typeId: 12)
class WeaknessArea extends HiveObject {
  @HiveField(0)
  final String topicName;
  
  @HiveField(1)
  final String categoryName;
  
  @HiveField(2)
  final double accuracyRate;
  
  @HiveField(3)
  final int totalAttempts;
  
  @HiveField(4)
  final List<String> commonMistakes;
  
  @HiveField(5)
  final DateTime identifiedAt;
  
  @HiveField(6)
  final bool needsReview;
  
  @HiveField(7)
  final int priority; // 1-5, 5 being highest priority
  
  WeaknessArea({
    required this.topicName,
    required this.categoryName,
    required this.accuracyRate,
    required this.totalAttempts,
    required this.commonMistakes,
    required this.identifiedAt,
    this.needsReview = true,
    this.priority = 3,
  });
  
  bool get isCritical => accuracyRate < 0.5 && totalAttempts >= 5;
  bool get needsImprovement => accuracyRate < 0.7;
  
  WeaknessArea copyWith({
    String? topicName,
    String? categoryName,
    double? accuracyRate,
    int? totalAttempts,
    List<String>? commonMistakes,
    DateTime? identifiedAt,
    bool? needsReview,
    int? priority,
  }) {
    return WeaknessArea(
      topicName: topicName ?? this.topicName,
      categoryName: categoryName ?? this.categoryName,
      accuracyRate: accuracyRate ?? this.accuracyRate,
      totalAttempts: totalAttempts ?? this.totalAttempts,
      commonMistakes: commonMistakes ?? this.commonMistakes,
      identifiedAt: identifiedAt ?? this.identifiedAt,
      needsReview: needsReview ?? this.needsReview,
      priority: priority ?? this.priority,
    );
  }
}

@HiveType(typeId: 13)
class Achievement extends HiveObject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String title;
  
  @HiveField(2)
  final String description;
  
  @HiveField(3)
  final String iconName;
  
  @HiveField(4)
  final String categoryName;
  
  @HiveField(5)
  final DateTime unlockedAt;
  
  @HiveField(6)
  final bool isUnlocked;
  
  @HiveField(7)
  final int points;
  
  @HiveField(8)
  final String rarity; // common, rare, epic, legendary
  
  @HiveField(9)
  final Map<String, dynamic> criteria; // unlock criteria
  
  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.iconName,
    required this.categoryName,
    required this.unlockedAt,
    this.isUnlocked = false,
    this.points = 0,
    this.rarity = 'common',
    this.criteria = const {},
  });
  
  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    String? iconName,
    String? categoryName,
    DateTime? unlockedAt,
    bool? isUnlocked,
    int? points,
    String? rarity,
    Map<String, dynamic>? criteria,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      iconName: iconName ?? this.iconName,
      categoryName: categoryName ?? this.categoryName,
      unlockedAt: unlockedAt ?? this.unlockedAt,
      isUnlocked: isUnlocked ?? this.isUnlocked,
      points: points ?? this.points,
      rarity: rarity ?? this.rarity,
      criteria: criteria ?? this.criteria,
    );
  }
}

@HiveType(typeId: 14)
class StudySession extends HiveObject {
  @HiveField(0)
  final String id;
  
  @HiveField(1)
  final String categoryName;
  
  @HiveField(2)
  final String levelName;
  
  @HiveField(3)
  final DateTime startTime;
  
  @HiveField(4)
  final DateTime? endTime;
  
  @HiveField(5)
  final int questionsAttempted;
  
  @HiveField(6)
  final int correctAnswers;
  
  @HiveField(7)
  final Duration totalTime;
  
  @HiveField(8)
  final List<String> topicsStudied;
  
  @HiveField(9)
  final bool isCompleted;
  
  StudySession({
    required this.id,
    required this.categoryName,
    required this.levelName,
    required this.startTime,
    this.endTime,
    this.questionsAttempted = 0,
    this.correctAnswers = 0,
    this.totalTime = Duration.zero,
    this.topicsStudied = const [],
    this.isCompleted = false,
  });
  
  double get accuracy => questionsAttempted > 0 ? correctAnswers / questionsAttempted : 0.0;
  double get averageTimePerQuestion => questionsAttempted > 0 ? totalTime.inSeconds / questionsAttempted : 0.0;
  
  StudySession copyWith({
    String? id,
    String? categoryName,
    String? levelName,
    DateTime? startTime,
    DateTime? endTime,
    int? questionsAttempted,
    int? correctAnswers,
    Duration? totalTime,
    List<String>? topicsStudied,
    bool? isCompleted,
  }) {
    return StudySession(
      id: id ?? this.id,
      categoryName: categoryName ?? this.categoryName,
      levelName: levelName ?? this.levelName,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      questionsAttempted: questionsAttempted ?? this.questionsAttempted,
      correctAnswers: correctAnswers ?? this.correctAnswers,
      totalTime: totalTime ?? this.totalTime,
      topicsStudied: topicsStudied ?? this.topicsStudied,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}