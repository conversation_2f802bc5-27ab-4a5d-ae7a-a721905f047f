import 'package:flutter/material.dart';

@immutable
class QuizData {
  final String title;
  final String description;
  final String version;
  final String lastUpdated;
  final List<QuizCategory> categories;

  const QuizData({
    required this.title,
    required this.description,
    required this.version,
    required this.lastUpdated,
    required this.categories,
  });

  factory QuizData.fromJson(Map<String, dynamic> json) {
    return QuizData(
      title: json['title'] as String,
      description: json['description'] as String,
      version: json['version'] as String,
      lastUpdated: json['lastUpdated'] as String,
      categories: (json['categories'] as List)
          .map((c) => QuizCategory.fromJson(c))
          .toList(),
    );
  }
}

@immutable
class QuizQuestion {
  final String question;
  final List<String> options;
  final int correct;
  final String explanation;
  final String? reference;
  final int difficulty;

  const QuizQuestion({
    required this.question,
    required this.options,
    required this.correct,
    required this.explanation,
    this.reference,
    this.difficulty = 1,
  });

  factory QuizQuestion.from<PERSON>son(Map<String, dynamic> json) {
    return QuizQuestion(
      question: json['question'] as String,
      options: List<String>.from(json['options']),
      correct: json['correct'] as int,
      explanation: json['explanation'] as String,
      reference: json['reference'] as String?,
      difficulty: json['difficulty'] as int? ?? 1,
    );
  }
}

@immutable
class QuizLevel {
  final String level;
  final String? icon;
  final int pointsPerQuestion;
  final int timePerQuestion;
  final List<QuizQuestion> questions;

  const QuizLevel({
    required this.level,
    this.icon,
    this.pointsPerQuestion = 10,
    this.timePerQuestion = 30,
    required this.questions,
  });

  factory QuizLevel.fromJson(Map<String, dynamic> json) {
    return QuizLevel(
      level: json['level'] as String,
      icon: json['icon'] as String?,
      pointsPerQuestion: json['pointsPerQuestion'] as int? ?? 10,
      timePerQuestion: json['timePerQuestion'] as int? ?? 30,
      questions: (json['questions'] as List)
          .map((q) => QuizQuestion.fromJson(q))
          .toList(),
    );
  }
  
  IconData getIconData() {
    switch (icon) {
      case 'school':
        return Icons.school;
      case 'trending_up':
        return Icons.trending_up;
      case 'psychology':
        return Icons.psychology;
      default:
        return Icons.quiz;
    }
  }
}

@immutable
class QuizCategory {
  final String name;
  final String? icon;
  final String? color;
  final String? description;
  final List<QuizLevel> levels;

  const QuizCategory({
    required this.name,
    this.icon,
    this.color,
    this.description,
    required this.levels,
  });

  factory QuizCategory.fromJson(Map<String, dynamic> json) {
    return QuizCategory(
      name: json['name'] as String,
      icon: json['icon'] as String?,
      color: json['color'] as String?,
      description: json['description'] as String?,
      levels: (json['levels'] as List)
          .map((l) => QuizLevel.fromJson(l))
          .toList(),
    );
  }
  
  Color getColor() {
    if (color != null) {
      try {
        return Color(int.parse(color!.substring(1), radix: 16) | 0xFF000000);
      } catch (e) {
        return Colors.blue;
      }
    }
    return Colors.blue;
  }
  
  IconData getIconData() {
    switch (icon) {
      case 'tax':
        return Icons.account_balance;
      case 'receipt_long':
        return Icons.receipt_long;
      case 'business':
        return Icons.business;
      case 'star':
        return Icons.star;
      case 'account_balance':
        return Icons.account_balance;
      default:
        return Icons.quiz;
    }
  }
}
