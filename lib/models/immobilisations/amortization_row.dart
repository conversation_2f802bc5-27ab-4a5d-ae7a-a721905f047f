class AmortizationRow {
  // Core Properties
  final int year;
  final String yearLabel;
  final double baseAmount;
  final double rate;
  final double annuity;
  final double cumulativeAnnuity;
  final double netBookValue;

  // Degressive Mode Properties
  final double? degressiveRate;
  final double? linearRate;
  final bool isLinearSwitchYear;

  // Derogatory Properties
  final double? accountingAmortization;
  final double? fiscalAmortization;
  final double? derogationProvision;
  final double? derogationReprise;

  const AmortizationRow({
    required this.year,
    required this.yearLabel,
    required this.baseAmount,
    required this.rate,
    required this.annuity,
    required this.cumulativeAnnuity,
    required this.netBookValue,
    this.degressiveRate,
    this.linearRate,
    this.isLinearSwitchYear = false,
    this.accountingAmortization,
    this.fiscalAmortization,
    this.derogationProvision,
    this.derogationReprise,
  });

  /// Convert from existing Map<String, String> format used in the calculator
  factory AmortizationRow.fromTableMap(Map<String, String> map) {
    // Extract year from yearLabel (e.g., "2024 (8 mois)" -> 2024)
    final yearLabel = map['Année'] ?? '';
    final yearMatch = RegExp(r'(\d{4})').firstMatch(yearLabel);
    final year = yearMatch != null ? int.parse(yearMatch.group(1)!) : 0;

    // Parse base amount
    final baseStr = map['Base'] ?? '0';
    final baseAmount = double.tryParse(baseStr) ?? 0.0;

    // Parse rate (handle both "Taux" for linear and "Taux dégressif" for degressive)
    final rateStr = map['Taux'] ?? map['Taux dégressif'] ?? '0';
    final rateMatch = RegExp(r'([\d.]+)').firstMatch(rateStr);
    final rate = rateMatch != null ? double.parse(rateMatch.group(1)!) : 0.0;

    // Parse annuity
    final annuityStr = map['Annuités'] ?? '0';
    final annuity = double.tryParse(annuityStr) ?? 0.0;

    // Parse cumulative annuity
    final cumulativeStr = map['Annuités cumulées'] ?? '0';
    final cumulativeAnnuity = double.tryParse(cumulativeStr) ?? 0.0;

    // Parse net book value (handle special case with "(valeur résiduelle)")
    final vncStr = map['VNC'] ?? '0';
    final vncMatch = RegExp(r'([\d.]+)').firstMatch(vncStr);
    final netBookValue = vncMatch != null ? double.parse(vncMatch.group(1)!) : 0.0;

    // Parse degressive rate if present
    double? degressiveRate;
    if (map.containsKey('Taux dégressif')) {
      final degressiveStr = map['Taux dégressif'] ?? '';
      final degressiveMatch = RegExp(r'([\d.]+)').firstMatch(degressiveStr);
      degressiveRate = degressiveMatch != null ? double.parse(degressiveMatch.group(1)!) : null;
    }

    // Parse linear rate if present
    double? linearRate;
    if (map.containsKey('Taux linéaire')) {
      final linearStr = map['Taux linéaire'] ?? '';
      final linearMatch = RegExp(r'([\d.]+)').firstMatch(linearStr);
      linearRate = linearMatch != null ? double.parse(linearMatch.group(1)!) : null;
    }

    // Parse derogatory amounts if present
    double? accountingAmortization;
    if (map.containsKey('Amortissement comptable')) {
      final accountingStr = map['Amortissement comptable'] ?? '0';
      accountingAmortization = double.tryParse(accountingStr);
    }

    double? fiscalAmortization;
    if (map.containsKey('Amortissement fiscal')) {
      final fiscalStr = map['Amortissement fiscal'] ?? '0';
      fiscalAmortization = double.tryParse(fiscalStr);
    }

    double? derogationProvision;
    if (map.containsKey('Dotations')) {
      final dotationStr = map['Dotations'] ?? '';
      if (dotationStr != '-' && dotationStr.isNotEmpty) {
        derogationProvision = double.tryParse(dotationStr);
      }
    }

    double? derogationReprise;
    if (map.containsKey('Reprises')) {
      final repriseStr = map['Reprises'] ?? '';
      if (repriseStr != '-' && repriseStr.isNotEmpty) {
        derogationReprise = double.tryParse(repriseStr);
      }
    }

    // Determine if this is a linear switch year (when linear rate > degressive rate)
    final isLinearSwitchYear = linearRate != null && 
                               degressiveRate != null && 
                               linearRate > degressiveRate;

    return AmortizationRow(
      year: year,
      yearLabel: yearLabel,
      baseAmount: baseAmount,
      rate: rate,
      annuity: annuity,
      cumulativeAnnuity: cumulativeAnnuity,
      netBookValue: netBookValue,
      degressiveRate: degressiveRate,
      linearRate: linearRate,
      isLinearSwitchYear: isLinearSwitchYear,
      accountingAmortization: accountingAmortization,
      fiscalAmortization: fiscalAmortization,
      derogationProvision: derogationProvision,
      derogationReprise: derogationReprise,
    );
  }

  /// Convert to existing Map<String, String> format for backward compatibility
  Map<String, String> toTableMap() {
    final map = <String, String>{};

    // Always include core fields
    map['Année'] = yearLabel;
    map['Base'] = baseAmount.toStringAsFixed(2);
    map['Annuités'] = annuity.toStringAsFixed(2);
    map['Annuités cumulées'] = cumulativeAnnuity.toStringAsFixed(2);
    
    // Handle VNC with special formatting for residual value
    if (yearLabel.contains('(') && yearLabel.contains('mois)') && 
        netBookValue < baseAmount * 0.1) { // Likely residual value
      map['VNC'] = '${netBookValue.toStringAsFixed(2)} (valeur résiduelle)';
    } else {
      map['VNC'] = netBookValue.toStringAsFixed(2);
    }

    // Add rate fields based on available data
    if (degressiveRate != null && linearRate != null) {
      // Degressive mode
      map['Taux dégressif'] = '${degressiveRate!.toStringAsFixed(2)} %';
      map['Taux linéaire'] = '${linearRate!.toStringAsFixed(2)} %';
    } else {
      // Linear mode
      map['Taux'] = '${rate.toString()} %';
    }

    // Add derogatory fields if present
    if (accountingAmortization != null) {
      map['Amortissement comptable'] = accountingAmortization!.toStringAsFixed(2);
    }
    if (fiscalAmortization != null) {
      map['Amortissement fiscal'] = fiscalAmortization!.toStringAsFixed(2);
    }
    if (derogationProvision != null && derogationProvision! > 0) {
      map['Dotations'] = derogationProvision!.toStringAsFixed(2);
    } else {
      map['Dotations'] = '-';
    }
    if (derogationReprise != null && derogationReprise! > 0) {
      map['Reprises'] = derogationReprise!.toStringAsFixed(2);
    } else {
      map['Reprises'] = '-';
    }

    return map;
  }

  /// Create from JSON data
  factory AmortizationRow.fromJson(Map<String, dynamic> json) {
    return AmortizationRow(
      year: json['year'] as int,
      yearLabel: json['yearLabel'] as String,
      baseAmount: (json['baseAmount'] as num).toDouble(),
      rate: (json['rate'] as num).toDouble(),
      annuity: (json['annuity'] as num).toDouble(),
      cumulativeAnnuity: (json['cumulativeAnnuity'] as num).toDouble(),
      netBookValue: (json['netBookValue'] as num).toDouble(),
      degressiveRate: json['degressiveRate'] != null 
          ? (json['degressiveRate'] as num).toDouble() 
          : null,
      linearRate: json['linearRate'] != null 
          ? (json['linearRate'] as num).toDouble() 
          : null,
      isLinearSwitchYear: json['isLinearSwitchYear'] as bool? ?? false,
      accountingAmortization: json['accountingAmortization'] != null 
          ? (json['accountingAmortization'] as num).toDouble() 
          : null,
      fiscalAmortization: json['fiscalAmortization'] != null 
          ? (json['fiscalAmortization'] as num).toDouble() 
          : null,
      derogationProvision: json['derogationProvision'] != null 
          ? (json['derogationProvision'] as num).toDouble() 
          : null,
      derogationReprise: json['derogationReprise'] != null 
          ? (json['derogationReprise'] as num).toDouble() 
          : null,
    );
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'year': year,
      'yearLabel': yearLabel,
      'baseAmount': baseAmount,
      'rate': rate,
      'annuity': annuity,
      'cumulativeAnnuity': cumulativeAnnuity,
      'netBookValue': netBookValue,
      'degressiveRate': degressiveRate,
      'linearRate': linearRate,
      'isLinearSwitchYear': isLinearSwitchYear,
      'accountingAmortization': accountingAmortization,
      'fiscalAmortization': fiscalAmortization,
      'derogationProvision': derogationProvision,
      'derogationReprise': derogationReprise,
    };
  }

  /// Check if this is a partial year (contains months in label)
  bool get isPartialYear {
    return yearLabel.contains('mois');
  }

  /// Check if this row has derogatory amounts
  bool get hasDerogation {
    return accountingAmortization != null || 
           fiscalAmortization != null || 
           derogationProvision != null || 
           derogationReprise != null;
  }

  /// Convert a list of Map<String, String> to List<AmortizationRow>
  static List<AmortizationRow> fromTableList(List<Map<String, String>> tables) {
    return tables.map((table) => AmortizationRow.fromTableMap(table)).toList();
  }

  /// Convert a list of AmortizationRow back to List<Map<String, String>>
  static List<Map<String, String>> toTableList(List<AmortizationRow> rows) {
    return rows.map((row) => row.toTableMap()).toList();
  }

  /// Create a copy with modified values
  AmortizationRow copyWith({
    int? year,
    String? yearLabel,
    double? baseAmount,
    double? rate,
    double? annuity,
    double? cumulativeAnnuity,
    double? netBookValue,
    double? degressiveRate,
    double? linearRate,
    bool? isLinearSwitchYear,
    double? accountingAmortization,
    double? fiscalAmortization,
    double? derogationProvision,
    double? derogationReprise,
  }) {
    return AmortizationRow(
      year: year ?? this.year,
      yearLabel: yearLabel ?? this.yearLabel,
      baseAmount: baseAmount ?? this.baseAmount,
      rate: rate ?? this.rate,
      annuity: annuity ?? this.annuity,
      cumulativeAnnuity: cumulativeAnnuity ?? this.cumulativeAnnuity,
      netBookValue: netBookValue ?? this.netBookValue,
      degressiveRate: degressiveRate ?? this.degressiveRate,
      linearRate: linearRate ?? this.linearRate,
      isLinearSwitchYear: isLinearSwitchYear ?? this.isLinearSwitchYear,
      accountingAmortization: accountingAmortization ?? this.accountingAmortization,
      fiscalAmortization: fiscalAmortization ?? this.fiscalAmortization,
      derogationProvision: derogationProvision ?? this.derogationProvision,
      derogationReprise: derogationReprise ?? this.derogationReprise,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is AmortizationRow &&
        other.year == year &&
        other.yearLabel == yearLabel &&
        other.baseAmount == baseAmount &&
        other.rate == rate &&
        other.annuity == annuity &&
        other.cumulativeAnnuity == cumulativeAnnuity &&
        other.netBookValue == netBookValue &&
        other.degressiveRate == degressiveRate &&
        other.linearRate == linearRate &&
        other.isLinearSwitchYear == isLinearSwitchYear &&
        other.accountingAmortization == accountingAmortization &&
        other.fiscalAmortization == fiscalAmortization &&
        other.derogationProvision == derogationProvision &&
        other.derogationReprise == derogationReprise;
  }

  @override
  int get hashCode {
    return Object.hash(
      year,
      yearLabel,
      baseAmount,
      rate,
      annuity,
      cumulativeAnnuity,
      netBookValue,
      degressiveRate,
      linearRate,
      isLinearSwitchYear,
      accountingAmortization,
      fiscalAmortization,
      derogationProvision,
      derogationReprise,
    );
  }

  @override
  String toString() {
    return 'AmortizationRow(year: $year, yearLabel: $yearLabel, '
           'baseAmount: $baseAmount, rate: $rate, annuity: $annuity, '
           'cumulativeAnnuity: $cumulativeAnnuity, netBookValue: $netBookValue, '
           'degressiveRate: $degressiveRate, linearRate: $linearRate, '
           'isLinearSwitchYear: $isLinearSwitchYear, '
           'accountingAmortization: $accountingAmortization, '
           'fiscalAmortization: $fiscalAmortization, '
           'derogationProvision: $derogationProvision, '
           'derogationReprise: $derogationReprise)';
  }
}