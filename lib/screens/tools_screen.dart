import 'package:flutter/material.dart';
import 'guides/fiscalite/is/sections/calculator_section.dart';
import 'guides/fiscalite/ir/sections/calculator_section.dart' as ir;
import 'guides/fiscalite/tva/sections/calculateur_section.dart';
import 'guides/fiscalite/tva/widgets/ras_tva_checker_widget.dart';
import 'guides/comptabilite_approfondie/regles_evaluation/sections/calculateur_section.dart' as amort;

class ToolsScreen extends StatelessWidget {
  const ToolsScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Outils de calcul',
          style: theme.textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: theme.colorScheme.onSurface,
          ),
        ),
        elevation: 0,
      ),
      body: LayoutBuilder(
        builder: (context, constraints) {
          final isNarrow = constraints.maxWidth < 600;
          final crossAxisCount = isNarrow ? 1 : 2;
          final childAspectRatio = isNarrow ? 2.2 : 1.5;

          return GridView.count(
            crossAxisCount: crossAxisCount,
            childAspectRatio: childAspectRatio,
            padding: const EdgeInsets.all(16),
            mainAxisSpacing: 16,
            crossAxisSpacing: 16,
            children: [
              _buildToolCard(
                context: context,
                title: 'Calculateur IS',
                subtitle: 'Calcul de l\'impôt sur les sociétés',
                icon: Icons.business,
                color: colorScheme.primary,
                onTap: () => _showCalculator(
                  context: context,
                  title: 'Calculateur IS',
                  calculator: const IsCalculatorSection(),
                ),
              ),
              _buildToolCard(
                context: context,
                title: 'Calculateur IR',
                subtitle: 'Calcul de l\'impôt sur le revenu',
                icon: Icons.person,
                color: colorScheme.secondary,
                onTap: () => _showCalculator(
                  context: context,
                  title: 'Calculateur IR',
                  calculator: const ir.CalculatorSection(),
                ),
              ),
              _buildToolCard(
                context: context,
                title: 'Calculateur TVA',
                subtitle: 'Calcul de la taxe sur la valeur ajoutée',
                icon: Icons.calculate,
                color: colorScheme.tertiary,
                onTap: () => _showCalculator(
                  context: context,
                  title: 'Calculateur TVA',
                  calculator: const CalculateurSection(),
                ),
              ),
              _buildToolCard(
                context: context,
                title: 'Calculateur Amortissement',
                subtitle: 'Calcul des amortissements linéaires et dégressifs',
                icon: Icons.trending_down,
                color: colorScheme.error,
                onTap: () => _showCalculator(
                  context: context,
                  title: 'Calculateur Amortissement',
                  calculator: const amort.CalculateurSection(),
                ),
              ),
              _buildToolCard(
                context: context,
                title: 'Vérificateur RAS TVA',
                subtitle: 'Vérification d\'assujettissement à la RAS TVA',
                icon: Icons.verified,
                color: colorScheme.primaryContainer,
                onTap: () => _showCalculator(
                  context: context,
                  title: 'Vérificateur RAS TVA',
                  calculator: const RASTVACheckerWidget(),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  void _showCalculator({
    required BuildContext context,
    required String title,
    required Widget calculator,
  }) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => Scaffold(
          appBar: AppBar(
            title: Text(title),
          ),
          body: calculator,
        ),
      ),
    );
  }

  Widget _buildToolCard({
    required BuildContext context,
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);

    return Card(
      elevation: 6,
      shadowColor: color.withOpacity(0.4),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        borderRadius: BorderRadius.circular(16),
        onTap: onTap,
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withOpacity(0.1),
                color.withOpacity(0.05),
              ],
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  icon,
                  size: 36,
                  color: color,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                title,
                textAlign: TextAlign.center,
                style: theme.textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                subtitle,
                textAlign: TextAlign.center,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
