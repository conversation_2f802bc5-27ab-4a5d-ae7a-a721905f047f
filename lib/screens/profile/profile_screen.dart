import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:intl/intl.dart'; // For date formatting
import 'package:provider/provider.dart' as provider_pkg; // Renamed import

import '../../models/hive/user_profile.dart';
import '../../models/hive/quiz_attempt.dart';
import '../../models/hive/active_quiz_state.dart';
import '../../models/exam/exam_attempt.dart';
import '../../models/exam/exam.dart';
import '../../providers/user_progress_provider.dart';
import '../../providers/quiz_data_provider.dart';
import '../../providers/exam/exam_data_provider.dart'; // Cleaned import
import '../../controllers/quiz_controller.dart';
import '../../screens/quiz/quiz_question_screen.dart';
import '../../models/quiz_model.dart';
import '../../services/theme_service.dart'; // Import for theme service

// Provider to watch the QuizAttempt box for changes
final quizAttemptsStreamProvider = StreamProvider.autoDispose<BoxEvent>((ref) {
  final box = Hive.box<QuizAttempt>('quizAttemptsBox');
  return box.watch(); 
});

// Provider to watch the active quiz box for changes
final activeQuizStreamProvider = StreamProvider.autoDispose<BoxEvent>((ref) {
  final box = Hive.box<ActiveQuizState>('activeQuizBox');
  return box.watch();
});

// Provider to watch the ExamAttempt box for changes (New)
final examAttemptsStreamProvider = StreamProvider.autoDispose<BoxEvent>((ref) {
  final box = Hive.box<ExamAttempt>('examAttemptsBox');
  return box.watch();
});


class ProfileScreen extends ConsumerWidget {
  const ProfileScreen({super.key});

  // This is a placeholder function and will not work as is.
  QuizLevel? findQuizLevel(WidgetRef ref, String categoryName, String levelName) {
     // Ideally, load quiz data from a service/provider here
     // For now, return null to indicate it's not implemented
     print("Warning: findQuizLevel is not implemented. Cannot resume quiz from profile.");
     return null; 
  }


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userProgressService = ref.watch(userProgressServiceProvider);
    final userProfile = userProgressService.getCurrentUserProgress()?.userProfile; 
    
    // Watch streams to trigger rebuilds, but fetch data directly inside build
    ref.watch(quizAttemptsStreamProvider);
    ref.watch(activeQuizStreamProvider);
    ref.watch(examAttemptsStreamProvider); // Watch exam attempts box

    final theme = Theme.of(context);
    final textTheme = theme.textTheme;
    final colorScheme = theme.colorScheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    // Get current data directly from boxes
    final List<QuizAttempt> quizAttempts = Hive.box<QuizAttempt>('quizAttemptsBox').values.toList().reversed.toList();
    final List<ActiveQuizState> activeQuizzes = Hive.box<ActiveQuizState>('activeQuizBox').values.toList();
    final List<ExamAttempt> examAttempts = Hive.box<ExamAttempt>('examAttemptsBox').values.toList().reversed.toList(); // Fetch exam attempts

    // Visual flag for preview/test status
    final bool isPreviewVersion = true;

    if (userProfile == null) {
      return Scaffold(
        appBar: AppBar(
          title: Text(
            'Profil Utilisateur',
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: colorScheme.surface,
          elevation: isDark ? 0 : 1,
        ),
        backgroundColor: colorScheme.surface,
        body: Center(
          child: CircularProgressIndicator(
            color: colorScheme.primary,
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Profil Utilisateur',
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: isDark ? 0 : 1,
        actions: [
          if (isPreviewVersion)
            Chip(
              label: Text(
                'PREVIEW', 
                style: TextStyle(
                  color: Colors.white, 
                  fontSize: 12
                ),
              ),
              backgroundColor: Colors.purple,
              padding: EdgeInsets.zero,
              labelPadding: EdgeInsets.symmetric(horizontal: 8),
            ),
          SizedBox(width: 8),
        ],
      ),
      backgroundColor: colorScheme.surface,
      body: Stack(
        children: [
          ListView(
            padding: const EdgeInsets.all(16.0),
            children: [
              _buildProfileSection(context, ref, userProfile, textTheme, colorScheme, isDark),
              Divider(
                height: 48, 
                thickness: 1,
                color: colorScheme.outlineVariant.withOpacity(0.5),
              ),
              _buildActiveQuizzesSection(context, ref, activeQuizzes, textTheme, colorScheme, isDark), 
              Divider(
                height: 48, 
                thickness: 1,
                color: colorScheme.outlineVariant.withOpacity(0.5),
              ),
              _buildHistorySection(context, ref, quizAttempts, textTheme, colorScheme, isDark),
              Divider( // Add divider before exam history
                height: 48, 
                thickness: 1,
                color: colorScheme.outlineVariant.withOpacity(0.5),
              ),
              _buildExamHistorySection(context, ref, examAttempts, textTheme, colorScheme, isDark), // Add exam history section
            ],
          ),

          // Preview indicator
          if (isPreviewVersion)
            Positioned(
              top: 0,
              right: 0,
              child: Banner(
                message: 'TEST',
                color: Colors.purple,
                location: BannerLocation.topEnd,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildProfileSection(BuildContext context, WidgetRef ref, UserProfile profile, 
      TextTheme textTheme, ColorScheme colorScheme, bool isDark) {
    // Use surfaceContainerLow for profile card background for subtle difference
    return Column(
      children: [
        Card(
          elevation: isDark ? 0 : 1, 
          color: colorScheme.surfaceContainerLow,
          surfaceTintColor: colorScheme.surfaceTint,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: isDark 
              ? BorderSide(color: colorScheme.outline.withOpacity(0.2)) 
              : BorderSide.none,
          ),
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Row(
              children: [
                CircleAvatar(
                  radius: 30,
                  backgroundColor: colorScheme.primaryContainer,
                  child: Text(
                    profile.username.isNotEmpty ? profile.username[0].toUpperCase() : 'U',
                    style: textTheme.headlineMedium?.copyWith(color: colorScheme.onPrimaryContainer),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    profile.username,
                    style: textTheme.titleLarge?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.edit, color: colorScheme.primary),
                  tooltip: 'Modifier le nom d\'utilisateur',
                  onPressed: () => _showEditUsernameDialog(context, ref, profile.username, colorScheme, textTheme, isDark),
                ),
              ],
            ),
          ),
        ),
        
        // Add the exam score display
        if (profile.hasExamData)
          _buildLastExamScoreDisplay(context, profile, textTheme, colorScheme, isDark)
      ],
    );
  }
  
  // Creative exam score display
  Widget _buildLastExamScoreDisplay(BuildContext context, UserProfile profile, 
      TextTheme textTheme, ColorScheme colorScheme, bool isDark) {
    final percentage = profile.lastExamPercentage;
    final scoreColor = _getExamScoreColor(percentage, colorScheme);
    final isPassing = percentage >= 50;
    
    return Card(
      margin: const EdgeInsets.only(top: 16),
      elevation: isDark ? 0 : 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: BorderSide(
          color: scoreColor.withOpacity(isDark ? 0.4 : 0.6), 
          width: isDark ? 1 : 2
        ),
      ),
      color: colorScheme.surface,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.assignment_outlined, color: scoreColor),
                const SizedBox(width: 8),
                // Wrap the title Text with Expanded
                Expanded(
                  child: Text(
                    'Dernier Examen',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
                // Keep the date Text outside Expanded
                if (profile.lastExamDate != null)
                  Padding(
                    padding: const EdgeInsets.only(left: 8.0), // Add padding for spacing
                    child: Text(
                      DateFormat('dd/MM/yyyy').format(profile.lastExamDate!),
                      style: textTheme.bodySmall?.copyWith(color: colorScheme.onSurfaceVariant),
                    ),
                  ),
              ],
            ),
            Divider(
              height: 24,
              color: colorScheme.outlineVariant.withOpacity(0.5),
            ),
            Row(
              children: [
                // Score visualization
                SizedBox(
                  width: 80,
                  height: 80,
                  child: Stack(
                    alignment: Alignment.center,
                    children: [
                      CircularProgressIndicator(
                        value: percentage / 100,
                        strokeWidth: 8,
                        backgroundColor: colorScheme.surfaceContainerHighest.withOpacity(isDark ? 0.3 : 0.5),
                        valueColor: AlwaysStoppedAnimation<Color>(scoreColor),
                      ),
                      Text(
                        '${percentage.toInt()}%',
                        style: textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: scoreColor,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Correctly placed Score Text
                      Text( 
                        'Score: ${profile.lastExamScore}/${profile.lastExamTotal}',
                        style: textTheme.titleMedium?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 8),
                      // Badge based on score
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                        decoration: BoxDecoration(
                          color: scoreColor.withOpacity(isDark ? 0.1 : 0.1),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              isPassing ? Icons.emoji_events_outlined : Icons.refresh_outlined,
                              size: 16,
                              color: scoreColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              isPassing ? 'Réussi' : 'À revoir',
                              style: textTheme.bodyMedium?.copyWith(
                                color: scoreColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (profile.bestExamPercentage != null && 
                          profile.bestExamPercentage! > percentage)
                        Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: Text(
                            'Meilleur résultat: ${profile.bestExamPercentage!.toInt()}%',
                            style: textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            // Motivational message
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withOpacity(isDark ? 0.2 : 0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colorScheme.outline.withOpacity(isDark ? 0.1 : 0.2)),
              ),
              child: Row(
                children: [
                  Icon(
                    isPassing ? Icons.tips_and_updates_outlined : Icons.lightbulb_outline,
                    color: isPassing ? Colors.amber : colorScheme.primary,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      isPassing 
                          ? 'Excellent! Continuez votre progression.' 
                          : 'Continuez à pratiquer pour améliorer vos résultats.',
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
  
  // Helper function for exam score colors
  Color _getExamScoreColor(double percentage, ColorScheme colorScheme) {
    if (percentage >= 90) return Colors.green.shade700;
    if (percentage >= 70) return Colors.blue.shade600;
    if (percentage >= 50) return Colors.amber.shade700;
    return colorScheme.error;
  }

  Widget _buildActiveQuizzesSection(BuildContext context, WidgetRef ref, List<ActiveQuizState> activeQuizzes, 
      TextTheme textTheme, ColorScheme colorScheme, bool isDark) {

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quiz en Cours',
          style: textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        if (activeQuizzes.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 24.0), 
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.pause_circle_outline, 
                    size: 48, 
                    color: colorScheme.onSurfaceVariant.withOpacity(0.4)
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Aucun quiz en cours.',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurfaceVariant.withOpacity(0.7)
                    ),
                  ),
                ],
              ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: activeQuizzes.length,
            itemBuilder: (context, index) {
              final activeQuiz = activeQuizzes[index];
              final formattedDate = DateFormat('dd/MM/yyyy HH:mm').format(activeQuiz.lastSavedTimestamp);

              return Card(
                elevation: isDark ? 0 : 1,
                margin: const EdgeInsets.only(bottom: 12),
                color: colorScheme.secondaryContainer.withOpacity(isDark ? 0.2 : 0.3),
                surfaceTintColor: colorScheme.secondaryContainer,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: isDark 
                    ? BorderSide(color: colorScheme.outline.withOpacity(0.2)) 
                    : BorderSide.none,
                ),
                child: ListTile(
                  leading: Icon(
                    Icons.pause_circle_filled_outlined, 
                    color: colorScheme.onSecondaryContainer.withOpacity(0.8)
                  ),
                  title: Text(
                    '${activeQuiz.categoryName} - ${activeQuiz.levelName}',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  subtitle: Text(
                    'Question ${activeQuiz.currentQuestionIndex + 1} - Sauvegardé: $formattedDate',
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant
                    ),
                  ),
                  trailing: Icon(
                    Icons.play_arrow_rounded, 
                    color: colorScheme.onSecondaryContainer.withOpacity(0.8)
                  ),
                  onTap: () {
                     // --- Resume Logic ---
                     final quizDataService = ref.read(quizDataServiceProvider);
                     QuizLevel? originalLevel = quizDataService.findQuizLevel(activeQuiz.categoryName, activeQuiz.levelName); 
                     Color categoryColor = quizDataService.findCategoryColor(activeQuiz.categoryName) ?? Colors.blue; // Find color or use default

                     if (originalLevel != null && context.mounted) {
                        // Ensure the provider is initialized with the active state
                        ref.read(quizControllerProvider((originalLevel, activeQuiz.categoryName, activeQuiz))); 
                        
                        Navigator.push(
                           context,
                           MaterialPageRoute(
                           builder: (context) => QuizQuestionScreen(
                              level: originalLevel,
                              categoryName: activeQuiz.categoryName,
                              categoryColor: categoryColor, // Use found color
                           ),
                           ),
                        );
                     } else {
                         ScaffoldMessenger.of(context).showSnackBar(
                           SnackBar(
                             content: Text(
                               'Erreur: Données du quiz introuvables pour reprendre.',
                               style: TextStyle(color: colorScheme.onError),
                             ),
                             backgroundColor: colorScheme.error,
                           )
                         );
                     }
                  },
                ),
              );
            },
          ),
      ],
    );
  }


  Widget _buildHistorySection(BuildContext context, WidgetRef ref, List<QuizAttempt> attempts, 
      TextTheme textTheme, ColorScheme colorScheme, bool isDark) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Historique des Quiz',
          style: textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        if (attempts.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 32.0),
              child: Column(
                 mainAxisSize: MainAxisSize.min,
                 children: [
                   Icon(
                     Icons.history_toggle_off_outlined, 
                     size: 48, 
                     color: colorScheme.onSurfaceVariant.withOpacity(0.4)
                   ),
                   const SizedBox(height: 8),
                   Text(
                     'Aucun quiz terminé pour le moment.',
                     style: textTheme.bodyLarge?.copyWith(
                       color: colorScheme.onSurfaceVariant.withOpacity(0.7)
                     ),
                   ),
                 ],
              ),
            ),
          )
        else
          ListView.builder(
            shrinkWrap: true, 
            physics: const NeverScrollableScrollPhysics(), 
            itemCount: attempts.length,
            itemBuilder: (context, index) {
              final attempt = attempts[index];
              final percentage = (attempt.score / attempt.totalQuestions * 100).round();
              final formattedDate = DateFormat('dd/MM/yyyy HH:mm').format(attempt.timestamp);

              return Card(
                elevation: isDark ? 0 : 1,
                margin: const EdgeInsets.only(bottom: 12),
                color: colorScheme.surfaceContainer, 
                surfaceTintColor: colorScheme.surfaceTint,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                  side: isDark 
                    ? BorderSide(color: colorScheme.outline.withOpacity(0.2)) 
                    : BorderSide.none,
                ),
                child: ListTile(
                  leading: CircleAvatar( 
                    backgroundColor: _getScoreColor(percentage, colorScheme).withOpacity(isDark ? 0.1 : 0.1),
                    child: Icon(
                       ref.read(quizDataServiceProvider).findCategoryIcon(attempt.categoryName) ?? Icons.question_mark,
                       color: _getScoreColor(percentage, colorScheme), 
                       size: 20, 
                    ),
                  ),
                  title: Text(
                    '${attempt.categoryName} - ${attempt.levelName}',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  subtitle: Text(
                    'Score: ${attempt.score}/${attempt.totalQuestions} (${attempt.earnedPoints} pts) - $formattedDate',
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurfaceVariant
                    ),
                  ),
                  trailing: Icon(
                    Icons.check_circle_outline,
                    color: colorScheme.outline.withOpacity(0.6),
                  ),
                ),
              );
            },
          ),
      ],
    );
  }

  // --- Exam History Section ---
  Widget _buildExamHistorySection(BuildContext context, WidgetRef ref, List<ExamAttempt> attempts,
      TextTheme textTheme, ColorScheme colorScheme, bool isDark) {
    
    // Fetch all exams once for title lookup using the correct provider name
    final allExamsAsync = ref.watch(examListProvider); // Corrected provider name

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Historique des Examens',
          style: textTheme.headlineSmall?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 16),
        if (attempts.isEmpty)
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 32.0),
              child: Column(
                 mainAxisSize: MainAxisSize.min,
                 children: [
                   Icon(
                     Icons.assignment_turned_in_outlined, 
                     size: 48, 
                     color: colorScheme.onSurfaceVariant.withOpacity(0.4)
                   ),
                   const SizedBox(height: 8),
                   Text(
                     'Aucun examen terminé pour le moment.',
                     style: textTheme.bodyLarge?.copyWith(
                       color: colorScheme.onSurfaceVariant.withOpacity(0.7)
                     ),
                   ),
                 ],
              ),
            ),
          )
        else
          allExamsAsync.when(
            data: (allExams) {
              // Create a map for quick title lookup
              final examTitleMap = { for (var exam in allExams) exam.id : exam.title };

              return ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: attempts.length,
                itemBuilder: (context, index) {
                  final attempt = attempts[index];
                  final percentage = attempt.percentage; // Use getter
                  final formattedDate = DateFormat('dd/MM/yyyy HH:mm').format(attempt.timestamp);
                  final examTitle = examTitleMap[attempt.examId] ?? 'Examen Inconnu'; // Lookup title
                  final scoreColor = _getExamScoreColor(percentage, colorScheme); // Use exam score color helper

                  return Card(
                    elevation: isDark ? 0 : 1,
                    margin: const EdgeInsets.only(bottom: 12),
                    color: colorScheme.surfaceContainer,
                    surfaceTintColor: colorScheme.surfaceTint,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                      side: isDark
                          ? BorderSide(color: colorScheme.outline.withOpacity(0.2))
                          : BorderSide.none,
                    ),
                    child: ListTile(
                      leading: CircleAvatar(
                        backgroundColor: scoreColor.withOpacity(isDark ? 0.1 : 0.1),
                        child: Icon(
                          Icons.assignment_outlined, // Generic exam icon
                          color: scoreColor,
                          size: 20,
                        ),
                      ),
                      title: Text(
                        examTitle,
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w500,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      subtitle: Text(
                        'Score: ${attempt.score}/${attempt.totalQuestions} (${percentage.toStringAsFixed(1)}%) - $formattedDate',
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      trailing: Icon(
                        percentage >= 50 ? Icons.check_circle : Icons.cancel,
                        color: scoreColor.withOpacity(0.8),
                      ),
                      // Optional: Add onTap to view details later?
                    ),
                  );
                },
              );
            },
            loading: () => Center(child: CircularProgressIndicator(color: colorScheme.primary)),
            error: (error, stack) => Center(child: Text('Erreur chargement examens: $error')),
          ),
      ],
    );
  }


  Color _getScoreColor(int percentage, ColorScheme colorScheme) {
    if (percentage >= 90) return Colors.green.shade700;
    if (percentage >= 70) return Colors.blue.shade700;
    if (percentage >= 50) return Colors.orange.shade700;
    return colorScheme.error;
  }

  void _showEditUsernameDialog(BuildContext context, WidgetRef ref, String currentUsername, 
      ColorScheme colorScheme, TextTheme textTheme, bool isDark) {
    final controller = TextEditingController(text: currentUsername);
    final formKey = GlobalKey<FormState>();

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(
            'Modifier le nom d\'utilisateur',
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          backgroundColor: colorScheme.surface,
          surfaceTintColor: colorScheme.surfaceTint,
          elevation: isDark ? 0 : 4,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
            side: isDark 
              ? BorderSide(color: colorScheme.outline.withOpacity(0.2)) 
              : BorderSide.none,
          ),
          content: Form(
            key: formKey,
            child: TextFormField(
              controller: controller,
              autofocus: true,
              style: TextStyle(color: colorScheme.onSurface),
              decoration: InputDecoration(
                hintText: 'Nouveau nom',
                hintStyle: TextStyle(color: colorScheme.onSurfaceVariant.withOpacity(0.7)),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: colorScheme.outline),
                ),
                focusedBorder: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                  borderSide: BorderSide(color: colorScheme.primary, width: 2),
                ),
                filled: isDark,
                fillColor: isDark ? colorScheme.surfaceContainerHighest.withOpacity(0.3) : null,
              ),
              validator: (value) {
                if (value == null || value.trim().isEmpty) {
                  return 'Le nom ne peut pas être vide.';
                }
                return null;
              },
            ),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Annuler',
                style: TextStyle(color: colorScheme.primary),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                if (formKey.currentState!.validate()) {
                  final newUsername = controller.text.trim();
                  final userProgressService = ref.read(userProgressServiceProvider);
                  final currentProgress = userProgressService.getCurrentUserProgress();

                  if (currentProgress != null) {
                     // Create a new UserProfile object
                     final updatedProfile = UserProfile(username: newUsername);
                     // Update using the service
                     await userProgressService.updateUserProfile(updatedProfile);
                     Navigator.pop(context); // Close dialog
                     ScaffoldMessenger.of(context).showSnackBar(
                       SnackBar(
                         content: Text(
                           'Nom d\'utilisateur mis à jour!',
                           style: TextStyle(color: colorScheme.onPrimaryContainer),
                         ),
                         backgroundColor: colorScheme.primaryContainer,
                       ),
                     );
                  } else {
                     // Handle error case where progress is null
                     Navigator.pop(context);
                     ScaffoldMessenger.of(context).showSnackBar(
                       SnackBar(
                         content: Text(
                           'Erreur: Impossible de trouver le profil.',
                           style: TextStyle(color: colorScheme.onError),
                         ),
                         backgroundColor: colorScheme.error,
                       ),
                     );
                  }
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.primary,
                foregroundColor: colorScheme.onPrimary,
                elevation: isDark ? 0 : 2,
              ),
              child: const Text('Enregistrer'),
            ),
          ],
        );
      },
    );
  }
}
