import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:logging/logging.dart';
import 'package:intl/intl.dart';

class AffectationResultatsScreen extends StatefulWidget {
  const AffectationResultatsScreen({super.key});

  @override
  State<AffectationResultatsScreen> createState() =>
      _AffectationResultatsScreenState();
}

class _AffectationResultatsScreenState
    extends State<AffectationResultatsScreen> {
  static final _logger = Logger('AffectationResultatsScreen');
  Map<String, dynamic>? _data;

  @override
  void initState() {
    super.initState();
    _initializeLogger();
    _logger.info('Initializing AffectationResultatsScreen');
    _loadData();
  }

  void _initializeLogger() {
    Logger.root.level = Level.ALL;
    Logger.root.onRecord.listen((record) {
      debugPrint('${record.level.name}: ${record.time}: ${record.message}');
    });
  }

  Future<void> _loadData() async {
    try {
      _logger.info('Loading affectation resultats data');
      final jsonString = await rootBundle
          .loadString('assets/compta_soc/affectation_resultats.json');
      _logger.fine('JSON data loaded successfully');

      setState(() {
        _data = json.decode(jsonString);
        _logger.info('Data parsed and state updated');
      });
    } catch (e) {
      _logger.severe('Error loading affectation resultats data: $e');
      debugPrint('Error loading affectation resultats data: $e');
    }
  }

  Widget _buildProcedureCard(Map<String, dynamic> procedure,
      ColorScheme colorScheme, TextTheme textTheme) {
    _logger.fine('Building procedure card: ${procedure['etape']}');
    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              colorScheme.surface,
              colorScheme.surfaceContainerHighest.withOpacity(0.5),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primaryContainer,
                    colorScheme.primary.withOpacity(0.1),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.assignment,
                    color: colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          procedure['etape'],
                          style: textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.primary,
                          ),
                        ),
                        if (procedure['description'] != null)
                          Text(
                            procedure['description'],
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurfaceVariant,
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (procedure['delai'] != null) ...[
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: colorScheme.primaryContainer,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'Délai: ${procedure['delai']}',
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onPrimaryContainer,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],
                  if (procedure['formalites'] != null ||
                      procedure['documents'] != null ||
                      procedure['modalites'] != null) ...[
                    Text(
                      'Formalités et Documents',
                      style: textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.primary,
                      ),
                    ),
                    const SizedBox(height: 8),
                    ...(procedure['formalites'] ??
                            procedure['documents'] ??
                            procedure['modalites'])
                        .map<Widget>((item) => Padding(
                              padding: const EdgeInsets.only(bottom: 8),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.check_circle_outline,
                                    color: colorScheme.primary,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      item,
                                      style: textTheme.bodyMedium?.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            )),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCategoriesList(
      List<dynamic> categories, ColorScheme colorScheme, TextTheme textTheme) {
    _logger.fine('Building categories list with ${categories.length} items');
    return Column(
      children: categories
          .map<Widget>((category) => Card(
                elevation: 2,
                margin: const EdgeInsets.only(bottom: 16),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16)),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        colorScheme.surface,
                        colorScheme.surfaceContainerHighest.withOpacity(0.5),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              colorScheme.primaryContainer,
                              colorScheme.primary.withOpacity(0.1),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(16)),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.category,
                              color: colorScheme.primary,
                              size: 24,
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Text(
                                category['title'],
                                style: textTheme.titleMedium?.copyWith(
                                  fontWeight: FontWeight.bold,
                                  color: colorScheme.primary,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      if (category['droits'] != null)
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            children: (category['droits'] as List)
                                .map<Widget>((droit) => Padding(
                                      padding: const EdgeInsets.only(bottom: 8),
                                      child: Row(
                                        children: [
                                          Icon(
                                            Icons.check_circle_outline,
                                            color: colorScheme.primary,
                                            size: 20,
                                          ),
                                          const SizedBox(width: 8),
                                          Expanded(
                                            child: Text(
                                              droit,
                                              style: textTheme.bodyMedium
                                                  ?.copyWith(
                                                color: colorScheme.onSurface,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ))
                                .toList(),
                          ),
                        ),
                    ],
                  ),
                ),
              ))
          .toList(),
    );
  }

  Widget _buildDroitsEnregistrement(
      List<dynamic> droits, ColorScheme colorScheme, TextTheme textTheme) {
    _logger.fine('Building droits enregistrement with ${droits.length} items');
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              colorScheme.surface,
              colorScheme.surfaceContainerHighest.withOpacity(0.5),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primaryContainer,
                    colorScheme.primary.withOpacity(0.1),
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius:
                    const BorderRadius.vertical(top: Radius.circular(16)),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.receipt_long,
                    color: colorScheme.primary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'Droits d\'Enregistrement',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                ],
              ),
            ),
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: droits.length,
              itemBuilder: (context, index) {
                final droit = droits[index];
                return Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    border: index < droits.length - 1
                        ? Border(
                            bottom: BorderSide(
                              color: colorScheme.outline.withOpacity(0.2),
                            ),
                          )
                        : null,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        flex: 2,
                        child: Text(
                          droit['operation'],
                          style: textTheme.bodyMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      Expanded(
                        child: Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color: colorScheme.primaryContainer,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            droit['taux'],
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onPrimaryContainer,
                              fontWeight: FontWeight.bold,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTableauResultat(Map<String, dynamic> tableau,
      ColorScheme colorScheme, TextTheme textTheme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              colorScheme.surface,
              colorScheme.surfaceContainerHighest.withOpacity(0.5),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Tableau d\'Affectation du Résultat',
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: colorScheme.outline.withOpacity(0.2),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildResultatRow(
                    'Bénéfice net de l\'exercice',
                    tableau['benefice_net'].toString(),
                    colorScheme,
                    textTheme,
                    isHeader: true,
                  ),
                  _buildResultatRow(
                    'Report à nouveau antérieur',
                    tableau['report_anterieur'].toString(),
                    colorScheme,
                    textTheme,
                  ),
                  _buildResultatRow(
                    'Bénéfice distribuable',
                    tableau['benefice_distribuable'].toString(),
                    colorScheme,
                    textTheme,
                    isTotal: true,
                  ),
                  const Divider(),
                  ...tableau['affectations'].map<Widget>(
                    (affectation) => _buildResultatRow(
                      affectation['libelle'],
                      affectation['montant'].toString(),
                      colorScheme,
                      textTheme,
                      commentaire: affectation['commentaire'],
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTableauFiscal(Map<String, dynamic> tableau,
      ColorScheme colorScheme, TextTheme textTheme) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              colorScheme.surface,
              colorScheme.surfaceContainerHighest.withOpacity(0.5),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Calcul de la Retenue à la Source',
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.primary,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: colorScheme.outline.withOpacity(0.2),
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                children: [
                  _buildResultatRow(
                    'Dividende brut',
                    tableau['dividende_brut'].toString(),
                    colorScheme,
                    textTheme,
                    isHeader: true,
                  ),
                  _buildResultatRow(
                    'Retenue à la source (${tableau['retenue_source']['taux']})',
                    tableau['retenue_source']['montant'].toString(),
                    colorScheme,
                    textTheme,
                    isNegative: true,
                  ),
                  _buildResultatRow(
                    'Dividende net',
                    tableau['dividende_net'].toString(),
                    colorScheme,
                    textTheme,
                    isTotal: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResultatRow(
    String label,
    String montant,
    ColorScheme colorScheme,
    TextTheme textTheme, {
    bool isHeader = false,
    bool isTotal = false,
    bool isNegative = false,
    String? commentaire,
  }) {
    final formattedMontant =
        NumberFormat('#,##0.00', 'fr_FR').format(double.parse(montant));

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isHeader
            ? colorScheme.primaryContainer.withOpacity(0.2)
            : isTotal
                ? colorScheme.secondaryContainer.withOpacity(0.2)
                : null,
        border: Border(
          bottom: BorderSide(
            color: colorScheme.outline.withOpacity(0.1),
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  label,
                  style: textTheme.bodyMedium?.copyWith(
                    fontWeight: isHeader || isTotal ? FontWeight.bold : null,
                    color: isHeader
                        ? colorScheme.primary
                        : isTotal
                            ? colorScheme.secondary
                            : colorScheme.onSurface,
                  ),
                ),
              ),
              Text(
                '${isNegative ? '-' : ''} $formattedMontant DH',
                style: textTheme.bodyMedium?.copyWith(
                  fontWeight: isHeader || isTotal ? FontWeight.bold : null,
                  color: isNegative
                      ? colorScheme.error
                      : isHeader
                          ? colorScheme.primary
                          : isTotal
                              ? colorScheme.secondary
                              : colorScheme.onSurface,
                ),
              ),
            ],
          ),
          if (commentaire != null) ...[
            const SizedBox(height: 4),
            Text(
              commentaire,
              style: textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildContentSection(Map<String, dynamic> content,
      ColorScheme colorScheme, TextTheme textTheme) {
    _logger.fine('Building content section: ${content['subtitle']}');
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (content['subtitle'] != null) ...[
          Text(
            content['subtitle'],
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
        ],
        if (content['text'] != null) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: colorScheme.outline.withOpacity(0.2),
              ),
            ),
            child: Text(
              content['text'],
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
                height: 1.5,
              ),
            ),
          ),
          const SizedBox(height: 16),
        ],
        if (content['categories'] != null) ...[
          _buildCategoriesList(content['categories'], colorScheme, textTheme),
          const SizedBox(height: 16),
        ],
        if (content['procedures'] != null)
          ...content['procedures'].map<Widget>((procedure) =>
              _buildProcedureCard(procedure, colorScheme, textTheme)),
        if (content['examples'] != null) ...[
          const SizedBox(height: 16),
          ...content['examples'].map<Widget>((example) => Card(
                elevation: 2,
                margin: const EdgeInsets.only(bottom: 16),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16)),
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        colorScheme.surface,
                        colorScheme.surfaceContainerHighest.withOpacity(0.5),
                      ],
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                    ),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              colorScheme.primaryContainer,
                              colorScheme.primary.withOpacity(0.1),
                            ],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: const BorderRadius.vertical(
                              top: Radius.circular(16)),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              example['description'],
                              style: textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: colorScheme.primary,
                              ),
                            ),
                            if (example['scenario'] != null) ...[
                              const SizedBox(height: 8),
                              Text(
                                example['scenario'],
                                style: textTheme.bodyMedium?.copyWith(
                                  color: colorScheme.onSurfaceVariant,
                                ),
                              ),
                            ],
                          ],
                        ),
                      ),
                      if (example['tableau_resultat'] != null) ...[
                        const SizedBox(height: 16),
                        _buildTableauResultat(
                          example['tableau_resultat'],
                          colorScheme,
                          textTheme,
                        ),
                      ],
                      if (example['tableau_fiscal'] != null) ...[
                        const SizedBox(height: 16),
                        _buildTableauFiscal(
                          example['tableau_fiscal'],
                          colorScheme,
                          textTheme,
                        ),
                      ],
                      SingleChildScrollView(
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.all(16),
                        child: Theme(
                          data: Theme.of(context).copyWith(
                            dataTableTheme: DataTableThemeData(
                              headingTextStyle: textTheme.titleSmall?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                              dataTextStyle: textTheme.bodyMedium,
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: colorScheme.outline.withOpacity(0.2),
                                ),
                              ),
                            ),
                          ),
                          child: DataTable(
                            columnSpacing: 24,
                            horizontalMargin: 16,
                            headingRowHeight: 48,
                            dataRowHeight: 52,
                            decoration: BoxDecoration(
                              color: colorScheme.surface,
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: colorScheme.outline.withOpacity(0.2),
                              ),
                            ),
                            columns: [
                              DataColumn(
                                label: Text('Compte',
                                    style: textTheme.titleSmall?.copyWith(
                                      color: colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                    )),
                              ),
                              DataColumn(
                                label: Text('Libellé',
                                    style: textTheme.titleSmall?.copyWith(
                                      color: colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                    )),
                              ),
                              DataColumn(
                                label: Text('Débit',
                                    style: textTheme.titleSmall?.copyWith(
                                      color: colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                    )),
                                numeric: true,
                              ),
                              DataColumn(
                                label: Text('Crédit',
                                    style: textTheme.titleSmall?.copyWith(
                                      color: colorScheme.primary,
                                      fontWeight: FontWeight.bold,
                                    )),
                                numeric: true,
                              ),
                            ],
                            rows: (example['entries'] as List)
                                .map<DataRow>((entry) {
                              final isDebit = entry['debit'] != null &&
                                  entry['debit'].toString().isNotEmpty;
                              final isCredit = entry['credit'] != null &&
                                  entry['credit'].toString().isNotEmpty;

                              return DataRow(
                                cells: [
                                  DataCell(
                                    Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 8,
                                            vertical: 4,
                                          ),
                                          decoration: BoxDecoration(
                                            color: colorScheme.primaryContainer
                                                .withOpacity(0.5),
                                            borderRadius:
                                                BorderRadius.circular(6),
                                          ),
                                          child: Text(
                                            entry['account'],
                                            style:
                                                textTheme.bodyMedium?.copyWith(
                                              fontWeight: FontWeight.w600,
                                              color: colorScheme.primary,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  DataCell(
                                    Text(
                                      entry['label'],
                                      style: textTheme.bodyMedium?.copyWith(
                                        color: colorScheme.onSurface,
                                      ),
                                    ),
                                  ),
                                  DataCell(
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: isDebit
                                          ? BoxDecoration(
                                              color: colorScheme
                                                  .primaryContainer
                                                  .withOpacity(0.2),
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                            )
                                          : null,
                                      child: Text(
                                        entry['debit']?.toString() ?? '',
                                        style: textTheme.bodyMedium?.copyWith(
                                          color: isDebit
                                              ? colorScheme.primary
                                              : null,
                                          fontWeight:
                                              isDebit ? FontWeight.w600 : null,
                                        ),
                                        textAlign: TextAlign.right,
                                      ),
                                    ),
                                  ),
                                  DataCell(
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 8,
                                        vertical: 4,
                                      ),
                                      decoration: isCredit
                                          ? BoxDecoration(
                                              color: colorScheme
                                                  .secondaryContainer
                                                  .withOpacity(0.2),
                                              borderRadius:
                                                  BorderRadius.circular(6),
                                            )
                                          : null,
                                      child: Text(
                                        entry['credit']?.toString() ?? '',
                                        style: textTheme.bodyMedium?.copyWith(
                                          color: isCredit
                                              ? colorScheme.secondary
                                              : null,
                                          fontWeight:
                                              isCredit ? FontWeight.w600 : null,
                                        ),
                                        textAlign: TextAlign.right,
                                      ),
                                    ),
                                  ),
                                ],
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              )),
        ],
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    _logger.fine('Building AffectationResultatsScreen widget');
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (_data == null) {
      _logger.info('Data not yet loaded, showing loading indicator');
    } else {
      _logger.info('Rendering screen with loaded data');
    }

    return Scaffold(
      body: CustomScrollView(
        slivers: [
          SliverAppBar(
            expandedHeight: 200,
            floating: false,
            pinned: true,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.primary,
                      colorScheme.primaryContainer,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                ),
                child: Stack(
                  fit: StackFit.expand,
                  children: [
                    Opacity(
                      opacity: 0.1,
                      child: CustomPaint(
                        painter: GridPainter(),
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.end,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _data?['title'] ?? 'Affectation des Résultats',
                            style: textTheme.headlineMedium?.copyWith(
                              color: colorScheme.onPrimary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            _data?['description'] ?? 'Chargement...',
                            style: textTheme.titleMedium?.copyWith(
                              color: colorScheme.onPrimary.withOpacity(0.8),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (_data != null)
            SliverPadding(
              padding: const EdgeInsets.all(16.0),
              sliver: SliverList(
                delegate: SliverChildBuilderDelegate(
                  (context, sectionIndex) {
                    final sections = _data!['sections'] as List;
                    if (sectionIndex >= sections.length) return null;

                    final section =
                        sections[sectionIndex] as Map<String, dynamic>;
                    _logger.fine('Building section: ${section['title']}');

                    return Card(
                      elevation: 3,
                      margin: const EdgeInsets.only(bottom: 24.0),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      child: Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              colorScheme.surface,
                              colorScheme.surfaceContainerHighest.withOpacity(0.5),
                            ],
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                          ),
                          borderRadius: BorderRadius.circular(20),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Container(
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                gradient: LinearGradient(
                                  colors: [
                                    colorScheme.primaryContainer,
                                    colorScheme.primary.withOpacity(0.1),
                                  ],
                                  begin: Alignment.topLeft,
                                  end: Alignment.bottomRight,
                                ),
                                borderRadius: const BorderRadius.vertical(
                                  top: Radius.circular(20),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Container(
                                    width: 48,
                                    height: 48,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          colorScheme.primary,
                                          colorScheme.primaryContainer,
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Center(
                                      child: Text(
                                        '${sectionIndex + 1}',
                                        style: textTheme.titleLarge?.copyWith(
                                          color: colorScheme.onPrimary,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Text(
                                      section['title'],
                                      style: textTheme.titleLarge?.copyWith(
                                        color: colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  ...(section['content'] as List).map<Widget>(
                                      (content) => _buildContentSection(
                                          content, colorScheme, textTheme)),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            )
          else
            const SliverFillRemaining(
              child: Center(
                child: CircularProgressIndicator(),
              ),
            ),
          if (_data?['droits_enregistrement'] != null)
            SliverPadding(
              padding: const EdgeInsets.all(16.0),
              sliver: SliverToBoxAdapter(
                child: _buildDroitsEnregistrement(
                  _data!['droits_enregistrement'],
                  colorScheme,
                  textTheme,
                ),
              ),
            ),
        ],
      ),
    );
  }

  @override
  void dispose() {
    _logger.info('Disposing AffectationResultatsScreen');
    super.dispose();
  }
}

class GridPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white
      ..strokeWidth = 1.0;

    const spacing = 30.0;

    for (double i = 0; i < size.width; i += spacing) {
      canvas.drawLine(
        Offset(i, 0),
        Offset(i, size.height),
        paint,
      );
    }

    for (double i = 0; i < size.height; i += spacing) {
      canvas.drawLine(
        Offset(0, i),
        Offset(size.width, i),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}
