import 'package:flutter/material.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_societes/fusion/sections/fusion_creation_section.dart';
import 'package:moroccanaccounting/screens/guides/comptabilite_societes/fusion/sections/fusion_absorption_section.dart';
import 'package:moroccanaccounting/widgets/financial_statements/document_details_widget.dart';

class FusionScreen extends StatelessWidget {
  const FusionScreen({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return DefaultTabController(
      length: 5,
      child: Scaffold(
        body: NestedScrollView(
          headerSliverBuilder: (context, innerBoxIsScrolled) => [
            SliverAppBar(
              title: Text(
                'Traitement Comptable des Fusions',
                style: textTheme.titleLarge?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.bold,
                ),
              ),
              pinned: true,
              floating: true,
              backgroundColor: isDark ? colorScheme.surface.withOpacity(0.95) : colorScheme.surface,
              surfaceTintColor: Colors.transparent,
              elevation: isDark ? 0 : 2,
              shadowColor: colorScheme.shadow.withOpacity(isDark ? 0.05 : 0.1),
              bottom: TabBar(
                isScrollable: true,
                labelColor: colorScheme.primary,
                unselectedLabelColor: colorScheme.onSurfaceVariant,
                indicatorColor: colorScheme.primary,
                dividerColor: Colors.transparent,
                indicatorSize: TabBarIndicatorSize.label,
                labelStyle: textTheme.titleSmall?.copyWith(fontWeight: FontWeight.bold),
                unselectedLabelStyle: textTheme.titleSmall,
                tabs: const [
                  Tab(text: 'Introduction'),
                  Tab(text: 'Fusion Création'),
                  Tab(text: 'Fusion Absorption'),
                  Tab(text: 'Comptabilisation'),
                  Tab(text: 'Régularisation TVA'),
                ],
              ),
            ),
          ],
          body: TabBarView(
            children: [
              // Introduction Tab
              SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Introduction',
                              style: textTheme.titleLarge?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              'La fusion peut s\'effectuer selon deux modes :',
                              style: textTheme.bodyLarge,
                            ),
                            const SizedBox(height: 16),
                            DocumentDetailsWidget(
                              document: const DocumentDetails(
                                items: [
                                  DocumentDetailsItem(
                                    title: 'Fusion Création',
                                    content:
                                        'Création d\'une nouvelle société à laquelle plusieurs sociétés apportent leurs actifs. Sur le plan comptable, ce cas est traité comme une constitution par apports en nature et une liquidation des sociétés absorbées.',
                                  ),
                                  DocumentDetailsItem(
                                    title: 'Fusion Absorption',
                                    content:
                                        'Une société existante absorbe une ou plusieurs autres sociétés. Sur le plan comptable, la fusion absorption est assimilée à une augmentation de capital pour la société absorbante et à une liquidation pour la société absorbée.',
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Cas de Fusion Absorption',
                              style: textTheme.titleMedium?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            DocumentDetailsWidget(
                              document: const DocumentDetails(
                                items: [
                                  DocumentDetailsItem(
                                    title: 'Sociétés Indépendantes',
                                    content: 'Les sociétés absorbantes et absorbées sont préalablement indépendantes',
                                  ),
                                  DocumentDetailsItem(
                                    title: 'Participation de l\'Absorbante',
                                    content: 'La société absorbante avait préalablement une participation dans la société absorbée',
                                  ),
                                  DocumentDetailsItem(
                                    title: 'Participation de l\'Absorbée',
                                    content: 'La société absorbée avait préalablement une participation dans la société absorbante',
                                  ),
                                  DocumentDetailsItem(
                                    title: 'Participations Réciproques',
                                    content: 'Les deux sociétés avaient préalablement des participations réciproques',
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Card(
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Aspects Comptables',
                              style: textTheme.titleMedium?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 16),
                            DocumentDetailsWidget(
                              document: const DocumentDetails(
                                items: [
                                  DocumentDetailsItem(
                                    title: 'Éléments Amortissables',
                                    content: 'La société absorbante doit inscrire à son bilan les éléments amortissables reçus pour leur valeur d\'apport. Les amortissements sont calculés sur cette valeur.',
                                  ),
                                  DocumentDetailsItem(
                                    title: 'Éléments Non Amortissables',
                                    content: 'Traitement des éléments comme le fonds commercial, marques, terrains et titres à leur valeur d\'apport.',
                                  ),
                                  DocumentDetailsItem(
                                    title: 'Provisions',
                                    content: 'Traitement spécifique des provisions pour dépréciation selon la nature des éléments.',
                                  ),
                                  DocumentDetailsItem(
                                    title: 'Mali de Fusion',
                                    content: 'Différence négative constatée sur les titres de la société absorbée détenus par la société absorbante.',
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Fusion Création Tab
              const SingleChildScrollView(
                padding: EdgeInsets.all(16.0),
                child: FusionCreationSection(),
              ),

              // Fusion Absorption Tab
              const SingleChildScrollView(
                padding: EdgeInsets.all(16.0),
                child: FusionAbsorptionSection(),
              ),

              // Comptabilisation Tab
              SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Comptabilisation des Opérations de Fusion',
                          style: textTheme.titleLarge?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        DocumentDetailsWidget(
                          document: const DocumentDetails(
                            items: [
                              DocumentDetailsItem(
                                title: 'Éléments Amortissables',
                                content:
                                    'Traitement des immobilisations et leur amortissement',
                              ),
                              DocumentDetailsItem(
                                title: 'Éléments Non Amortissables',
                                content:
                                    'Traitement des éléments non amortissables comme le fonds commercial',
                              ),
                              DocumentDetailsItem(
                                title: 'Provisions',
                                content:
                                    'Traitement des provisions pour dépréciation',
                              ),
                              DocumentDetailsItem(
                                title: 'Mali de Fusion',
                                content:
                                    'Traitement du mali technique et du vrai mali',
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),

              // Régularisation TVA Tab
              SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Card(
                  child: Padding(
                    padding: const EdgeInsets.all(16.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Régularisation de la TVA',
                          style: textTheme.titleLarge?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 16),
                        DocumentDetailsWidget(
                          document: const DocumentDetails(
                            items: [
                              DocumentDetailsItem(
                                title: 'Avant 2013',
                                content:
                                    'Régularisation du cinquième de la TVA initialement déduite',
                              ),
                              DocumentDetailsItem(
                                title: 'Avant 2024',
                                content:
                                    'Taxation de la valeur totale de cession',
                              ),
                              DocumentDetailsItem(
                                title: 'Après 2024',
                                content:
                                    'Nouvelle méthode de régularisation sur 60 mois',
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
