import 'package:flutter/material.dart';

class ExonerationsTvaSection extends StatelessWidget {
  const ExonerationsTvaSection({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: <PERSON>um<PERSON>(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.check_circle_outline,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Exonérations de TVA',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Les différentes catégories d\'exonérations prévues par la loi',
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          _buildExonerationCard(
            context,
            titre: 'Exonérations avec droit à déduction',
            description:
                'Opérations exonérées de la TVA tout en permettant la déduction de la TVA sur les achats.',
            operations: [
              'Exportations de biens et services',
              'Produits livrés et services rendus aux zones franches d\'exportation',
              'Biens d\'investissement à inscrire dans un compte d\'immobilisation',
              'Engins et filets de pêche destinés aux professionnels de la pêche maritime',
              'Biens et services acquis ou livrés à titre de don par les personnes physiques ou morales marocaines ou étrangères',
              'Biens et marchandises placés sous les régimes suspensifs en douane',
            ],
            icon: Icons.check_circle,
            couleur: Colors.green,
          ),
          const SizedBox(height: 16),
          _buildExonerationCard(
            context,
            titre: 'Exonérations sans droit à déduction',
            description:
                'Opérations exonérées de la TVA sans possibilité de déduire la TVA sur les achats.',
            operations: [
              'Pain, couscous, semoules et farines servant à l\'alimentation humaine',
              'Lait frais pasteurisé',
              'Sucre brut (de betterave, de canne et sucres analogues)',
              'Médicaments anticancéreux et antiviraux',
              'Journaux, publications, livres et travaux de composition et d\'impression',
              'Films documentaires ou éducatifs',
              'Actes médicaux',
              'Opérations de crédit foncier et de crédit à la construction',
            ],
            icon: Icons.block,
            couleur: Colors.orange,
          ),
          const SizedBox(height: 24),
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Points importants',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  _buildInfoPoint(
                    context,
                    'Conditions d\'exonération',
                    'Les exonérations sont soumises à des conditions strictes et doivent être justifiées par des documents probants.',
                  ),
                  const SizedBox(height: 12),
                  _buildInfoPoint(
                    context,
                    'Procédure',
                    'Certaines exonérations nécessitent des formalités préalables auprès de l\'administration fiscale.',
                  ),
                  const SizedBox(height: 12),
                  _buildInfoPoint(
                    context,
                    'Régularisation',
                    'En cas de non-respect des conditions d\'exonération, l\'entreprise doit régulariser sa situation fiscale.',
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExonerationCard(
    BuildContext context, {
    required String titre,
    required String description,
    required List<String> operations,
    required IconData icon,
    required Color couleur,
  }) {
    return Card(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: couleur.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: couleur.withOpacity(0.2),
                    shape: BoxShape.circle,
                  ),
                  child: Icon(
                    icon,
                    color: couleur,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        titre,
                        style:
                            Theme.of(context).textTheme.titleMedium?.copyWith(
                                  color: couleur,
                                  fontWeight: FontWeight.bold,
                                ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Opérations concernées :',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                const SizedBox(height: 8),
                ...operations.map((operation) => Padding(
                      padding: const EdgeInsets.only(
                        left: 16,
                        bottom: 8,
                      ),
                      child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Icon(
                            Icons.check_circle_outline,
                            size: 18,
                            color: couleur,
                          ),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(operation),
                          ),
                        ],
                      ),
                    )),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoPoint(
    BuildContext context,
    String titre,
    String description,
  ) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Icon(
          Icons.arrow_right,
          color: Theme.of(context).colorScheme.primary,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                titre,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
              ),
              const SizedBox(height: 4),
              Text(
                description,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
          ),
        ),
      ],
    );
  }
}
