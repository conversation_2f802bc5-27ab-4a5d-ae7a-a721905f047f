import 'package:flutter/material.dart';
import '../../../../../models/tva/tva_line_item.dart';
import '../../../../../models/tva/tva_invoice_summary.dart';
import '../../../../../widgets/tva/tva_item_form_card.dart';
import '../../../../../widgets/tva/tva_invoice_summary.dart';
import '../../../../../services/tva_service.dart';
import '../../../../../utils/calculation_utils.dart';

class CalculatriceTvaSection extends StatefulWidget {
  const CalculatriceTvaSection({super.key});

  @override
  State<CalculatriceTvaSection> createState() => _CalculatriceTvaSectionState();
}

class _CalculatriceTvaSectionState extends State<CalculatriceTvaSection> {
  // Multi-item invoice state
  final List<TvaLineItem> _invoiceItems = [];
  bool _isLoading = false;
  
  // Services
  final TvaService _tvaService = TvaService();

  @override
  void initState() {
    super.initState();
    // Start with one empty item for better UX
    _addItem();
  }

  // Item management methods
  void _addItem() {
    setState(() {
      _invoiceItems.add(TvaLineItem(
        description: '',
        quantity: 1.0,
        unitPrice: 0.0,
        vatRate: 20.0,
        isExempt: false,
      ));
    });
  }

  void _updateItem(int index, TvaLineItem updatedItem) {
    if (index >= 0 && index < _invoiceItems.length) {
      setState(() {
        _invoiceItems[index] = updatedItem;
      });
    }
  }

  void _removeItem(int index) {
    if (index >= 0 && index < _invoiceItems.length) {
      setState(() {
        _invoiceItems.removeAt(index);
      });
      
      // Ensure at least one item remains
      if (_invoiceItems.isEmpty) {
        _addItem();
      }
    }
  }

  void _clearAllItems() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Confirmer la suppression'),
        content: const Text('Êtes-vous sûr de vouloir supprimer tous les articles ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
          FilledButton(
            onPressed: () {
              Navigator.pop(context);
              setState(() {
                _invoiceItems.clear();
                _addItem();
              });
            },
            child: const Text('Supprimer tout'),
          ),
        ],
      ),
    );
  }

  // Calculate invoice summary
  TvaInvoiceSummary get _invoiceSummary {
    return TvaInvoiceSummary.fromItems(_invoiceItems);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            _buildHeader(context),
            const SizedBox(height: 24),
            
            // Items List Section
            _buildItemsList(context),
            const SizedBox(height: 16),
            
            // Actions Bar
            _buildActionsBar(context),
            const SizedBox(height: 24),
            
            // Invoice Summary Section
            TvaInvoiceSummaryWidget(
              summary: _invoiceSummary,
              showBreakdown: true,
              showExportActions: false,
            ),
          ],
        ),
      ),
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _addItem,
        icon: const Icon(Icons.add),
        label: const Text('Ajouter un article'),
        tooltip: 'Ajouter un nouvel article à la facture',
      ),
    );
  }

  // Build header section
  Widget _buildHeader(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.primary,
            colorScheme.primaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.receipt_long,
                color: colorScheme.onPrimary,
                size: 32,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Calculatrice Facture TVA',
                      style: textTheme.headlineMedium?.copyWith(
                        color: colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Créez et calculez des factures multi-articles avec TVA automatique',
                      style: textTheme.titleMedium?.copyWith(
                        color: colorScheme.onPrimary.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: colorScheme.onPrimary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              '${_invoiceItems.length} article${_invoiceItems.length > 1 ? 's' : ''}',
              style: textTheme.labelMedium?.copyWith(
                color: colorScheme.onPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // Build items list section
  Widget _buildItemsList(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (_invoiceItems.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.inventory_2,
              color: colorScheme.primary,
              size: 24,
            ),
            const SizedBox(width: 8),
            Text(
              'Articles de la facture',
              style: textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const Spacer(),
            if (_invoiceItems.length > 1)
              TextButton.icon(
                onPressed: _clearAllItems,
                icon: Icon(
                  Icons.clear_all,
                  color: colorScheme.error,
                ),
                label: Text(
                  'Tout effacer',
                  style: TextStyle(color: colorScheme.error),
                ),
              ),
          ],
        ),
        const SizedBox(height: 16),
        ListView.separated(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _invoiceItems.length,
          separatorBuilder: (context, index) => const SizedBox(height: 16),
          itemBuilder: (context, index) {
            return TvaItemFormCard(
              key: ValueKey('item_$index'),
              initialItem: _invoiceItems[index],
              onChanged: (updatedItem) => _updateItem(index, updatedItem),
              onDelete: _invoiceItems.length > 1 ? () => _removeItem(index) : null,
              showDeleteButton: _invoiceItems.length > 1,
              autoFocus: index == _invoiceItems.length - 1 && _invoiceItems[index].description.isEmpty,
            );
          },
        ),
      ],
    );
  }

  // Build empty state
  Widget _buildEmptyState(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          children: [
            Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: colorScheme.outline,
            ),
            const SizedBox(height: 16),
            Text(
              'Aucun article',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Commencez par ajouter un article à votre facture',
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            FilledButton.icon(
              onPressed: _addItem,
              icon: const Icon(Icons.add),
              label: const Text('Ajouter le premier article'),
            ),
          ],
        ),
      ),
    );
  }

  // Build actions bar
  Widget _buildActionsBar(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 1,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Actions rapides',
              style: textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                OutlinedButton.icon(
                  onPressed: _addItem,
                  icon: const Icon(Icons.add),
                  label: const Text('Nouvel article'),
                ),
                if (_invoiceItems.length > 1)
                  OutlinedButton.icon(
                    onPressed: _clearAllItems,
                    icon: Icon(
                      Icons.clear_all,
                      color: colorScheme.error,
                    ),
                    label: Text(
                      'Tout effacer',
                      style: TextStyle(color: colorScheme.error),
                    ),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: colorScheme.error),
                    ),
                  ),
                OutlinedButton.icon(
                  onPressed: _invoiceSummary.hasItems ? () {
                    // Future: Export functionality
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Fonctionnalité d\'export à venir'),
                      ),
                    );
                  } : null,
                  icon: const Icon(Icons.share),
                  label: const Text('Partager'),
                ),
              ],
            ),
            if (_isLoading) ...[
              const SizedBox(height: 12),
              const LinearProgressIndicator(),
            ],
          ],
        ),
      ),
    );
  }
}
}
