import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:share_plus/share_plus.dart';
import 'dart:convert';

// Data models for checklist
class ChecklistDocument {
  final String id;
  final String title;
  final String description;
  final String category;
  final bool isMandatory;
  final List<String> examples;
  final String? helpText;

  ChecklistDocument({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.isMandatory,
    required this.examples,
    this.helpText,
  });

  factory ChecklistDocument.fromJson(Map<String, dynamic> json) {
    return ChecklistDocument(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      category: json['category'] ?? '',
      isMandatory: json['isMandatory'] ?? false,
      examples: List<String>.from(json['examples'] ?? []),
      helpText: json['helpText'],
    );
  }
}

class ChecklistCategory {
  final String id;
  final String title;
  final String description;
  final List<ChecklistDocument> documents;

  ChecklistCategory({
    required this.id,
    required this.title,
    required this.description,
    required this.documents,
  });

  factory ChecklistCategory.fromJson(Map<String, dynamic> json) {
    return ChecklistCategory(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      documents: (json['documents'] as List<dynamic>?)
              ?.map((doc) => ChecklistDocument.fromJson(doc))
              .toList() ??
          [],
    );
  }
}

class ChecklistData {
  final String title;
  final String description;
  final List<ChecklistCategory> categories;

  ChecklistData({
    required this.title,
    required this.description,
    required this.categories,
  });

  factory ChecklistData.fromJson(Map<String, dynamic> json) {
    return ChecklistData(
      title: json['title'] ?? '',
      description: json['description'] ?? '',
      categories: (json['categories'] as List<dynamic>?)
              ?.map((cat) => ChecklistCategory.fromJson(cat))
              .toList() ??
          [],
    );
  }
}

// Providers for state management
final checklistDataProvider = FutureProvider<ChecklistData>((ref) async {
  // This would normally load from assets/liasse_fiscale/checklist_documents_2025.json
  // For now, returning mock data
  await Future.delayed(const Duration(milliseconds: 500));
  
  final mockData = {
    "title": "Check-list Documents Liasse Fiscale 2025",
    "description": "Liste complète des documents requis pour la préparation de la liasse fiscale",
    "categories": [
      {
        "id": "mandatory",
        "title": "Documents Obligatoires",
        "description": "Documents requis pour tous les contribuables",
        "documents": [
          {
            "id": "bilan",
            "title": "Bilan comptable",
            "description": "État de la situation financière à la clôture de l'exercice",
            "category": "mandatory",
            "isMandatory": true,
            "examples": ["Actif", "Passif", "Capitaux propres"],
            "helpText": "Le bilan doit être certifié par un expert-comptable pour les grandes entreprises"
          },
          {
            "id": "cpc",
            "title": "Compte de Produits et Charges",
            "description": "État des résultats de l'exercice comptable",
            "category": "mandatory",
            "isMandatory": true,
            "examples": ["Produits d'exploitation", "Charges d'exploitation", "Résultat net"],
            "helpText": "Doit correspondre aux écritures comptables de l'exercice"
          },
          {
            "id": "etic",
            "title": "État des Informations Complémentaires (ETIC)",
            "description": "Notes explicatives aux états financiers",
            "category": "mandatory",
            "isMandatory": true,
            "examples": ["Méthodes comptables", "Engagements", "Événements postérieurs"],
            "helpText": "Obligatoire pour toutes les entreprises soumises à la comptabilité normalisée"
          }
        ]
      },
      {
        "id": "supporting",
        "title": "Documents Justificatifs",
        "description": "Documents à l'appui des déclarations",
        "documents": [
          {
            "id": "registre_immobilisations",
            "title": "Registre des Immobilisations",
            "description": "Détail des acquisitions et cessions d'immobilisations",
            "category": "supporting",
            "isMandatory": false,
            "examples": ["Terrains", "Constructions", "Matériel"],
            "helpText": "Recommandé pour justifier les amortissements"
          },
          {
            "id": "inventaire",
            "title": "État d'Inventaire",
            "description": "Valorisation des stocks à la clôture",
            "category": "supporting",
            "isMandatory": false,
            "examples": ["Matières premières", "Produits finis", "Marchandises"],
            "helpText": "Obligatoire si l'entreprise détient des stocks"
          }
        ]
      },
      {
        "id": "industry_specific",
        "title": "Documents Sectoriels",
        "description": "Documents spécifiques selon le secteur d'activité",
        "documents": [
          {
            "id": "autorisation_bancaire",
            "title": "Autorisation Bancaire",
            "description": "Pour les établissements de crédit",
            "category": "industry_specific",
            "isMandatory": false,
            "examples": ["Licence bancaire", "Agrément BAM"],
            "helpText": "Uniquement pour les banques et établissements financiers"
          }
        ]
      }
    ]
  };
  
  return ChecklistData.fromJson(mockData);
});

final checkedItemsProvider = StateProvider<Set<String>>((ref) => <String>{});
final searchQueryProvider = StateProvider<String>((ref) => '');
final selectedCategoryProvider = StateProvider<String?>((ref) => null);
final expandedItemsProvider = StateProvider<Set<String>>((ref) => <String>{});

class ChecklistSection extends ConsumerWidget {
  const ChecklistSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final checklistAsync = ref.watch(checklistDataProvider);
    final checkedItems = ref.watch(checkedItemsProvider);
    final searchQuery = ref.watch(searchQueryProvider);
    final selectedCategory = ref.watch(selectedCategoryProvider);

    return checklistAsync.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Erreur lors du chargement',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 8),
            Text(
              error.toString(),
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
      data: (checklistData) {
        final filteredCategories = _filterCategories(
          checklistData.categories,
          searchQuery,
          selectedCategory,
        );

        return Column(
          children: [
            _buildHeader(context, ref, checklistData, checkedItems),
            const SizedBox(height: 16),
            _buildSearchAndFilter(context, ref, checklistData.categories),
            const SizedBox(height: 16),
            _buildProgressIndicator(context, checklistData, checkedItems),
            const SizedBox(height: 16),
            Expanded(
              child: _buildChecklistContent(context, ref, filteredCategories),
            ),
          ],
        );
      },
    );
  }

  Widget _buildHeader(
    BuildContext context,
    WidgetRef ref,
    ChecklistData data,
    Set<String> checkedItems,
  ) {
    final totalItems = data.categories
        .expand((cat) => cat.documents)
        .length;
    final completedItems = checkedItems.length;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.checklist,
                  color: Theme.of(context).colorScheme.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        data.title,
                        style: Theme.of(context).textTheme.headlineSmall,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        data.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => _exportChecklist(context, data, checkedItems),
                  icon: const Icon(Icons.share),
                  tooltip: 'Exporter la check-list',
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              'Progression: $completedItems/$totalItems documents',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchAndFilter(
    BuildContext context,
    WidgetRef ref,
    List<ChecklistCategory> categories,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            TextField(
              decoration: InputDecoration(
                hintText: 'Rechercher un document...',
                prefixIcon: const Icon(Icons.search),
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                suffixIcon: ref.watch(searchQueryProvider).isNotEmpty
                    ? IconButton(
                        onPressed: () {
                          ref.read(searchQueryProvider.notifier).state = '';
                        },
                        icon: const Icon(Icons.clear),
                      )
                    : null,
              ),
              onChanged: (value) {
                ref.read(searchQueryProvider.notifier).state = value;
              },
            ),
            const SizedBox(height: 12),
            SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  FilterChip(
                    label: const Text('Tous'),
                    selected: ref.watch(selectedCategoryProvider) == null,
                    onSelected: (selected) {
                      ref.read(selectedCategoryProvider.notifier).state = null;
                    },
                  ),
                  const SizedBox(width: 8),
                  ...categories.map((category) => Padding(
                        padding: const EdgeInsets.only(right: 8),
                        child: FilterChip(
                          label: Text(category.title),
                          selected: ref.watch(selectedCategoryProvider) == category.id,
                          onSelected: (selected) {
                            ref.read(selectedCategoryProvider.notifier).state =
                                selected ? category.id : null;
                          },
                        ),
                      )),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildProgressIndicator(
    BuildContext context,
    ChecklistData data,
    Set<String> checkedItems,
  ) {
    final totalItems = data.categories.expand((cat) => cat.documents).length;
    final mandatoryItems = data.categories
        .expand((cat) => cat.documents)
        .where((doc) => doc.isMandatory)
        .length;
    final completedItems = checkedItems.length;
    final completedMandatory = data.categories
        .expand((cat) => cat.documents)
        .where((doc) => doc.isMandatory && checkedItems.contains(doc.id))
        .length;

    final progress = totalItems > 0 ? completedItems / totalItems : 0.0;
    final mandatoryProgress = mandatoryItems > 0 ? completedMandatory / mandatoryItems : 0.0;

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Progression globale',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: progress,
                        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          Theme.of(context).colorScheme.primary,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${(progress * 100).toInt()}% ($completedItems/$totalItems)',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 24),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Documents obligatoires',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: mandatoryProgress,
                        backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          mandatoryProgress == 1.0
                              ? Colors.green
                              : Theme.of(context).colorScheme.error,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${(mandatoryProgress * 100).toInt()}% ($completedMandatory/$mandatoryItems)',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (mandatoryProgress < 1.0) ...[
              const SizedBox(height: 12),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: Theme.of(context).colorScheme.errorContainer,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.warning,
                      color: Theme.of(context).colorScheme.onErrorContainer,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        'Attention: Des documents obligatoires sont manquants',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Theme.of(context).colorScheme.onErrorContainer,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildChecklistContent(
    BuildContext context,
    WidgetRef ref,
    List<ChecklistCategory> categories,
  ) {
    return ListView.builder(
      itemCount: categories.length,
      itemBuilder: (context, index) {
        final category = categories[index];
        return _buildCategoryCard(context, ref, category);
      },
    );
  }

  Widget _buildCategoryCard(
    BuildContext context,
    WidgetRef ref,
    ChecklistCategory category,
  ) {
    final checkedItems = ref.watch(checkedItemsProvider);
    final completedInCategory = category.documents
        .where((doc) => checkedItems.contains(doc.id))
        .length;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        children: [
          ListTile(
            leading: CircleAvatar(
              backgroundColor: Theme.of(context).colorScheme.primaryContainer,
              child: Text(
                '$completedInCategory/${category.documents.length}',
                style: TextStyle(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              category.title,
              style: Theme.of(context).textTheme.titleMedium,
            ),
            subtitle: Text(category.description),
            trailing: Icon(
              completedInCategory == category.documents.length
                  ? Icons.check_circle
                  : Icons.radio_button_unchecked,
              color: completedInCategory == category.documents.length
                  ? Colors.green
                  : Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          ...category.documents.map((document) => _buildDocumentTile(context, ref, document)),
        ],
      ),
    );
  }

  Widget _buildDocumentTile(
    BuildContext context,
    WidgetRef ref,
    ChecklistDocument document,
  ) {
    final checkedItems = ref.watch(checkedItemsProvider);
    final expandedItems = ref.watch(expandedItemsProvider);
    final isChecked = checkedItems.contains(document.id);
    final isExpanded = expandedItems.contains(document.id);

    return Column(
      children: [
        CheckboxListTile(
          value: isChecked,
          onChanged: (value) {
            final newCheckedItems = Set<String>.from(checkedItems);
            if (value == true) {
              newCheckedItems.add(document.id);
            } else {
              newCheckedItems.remove(document.id);
            }
            ref.read(checkedItemsProvider.notifier).state = newCheckedItems;
          },
          title: Row(
            children: [
              Expanded(
                child: Text(
                  document.title,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    decoration: isChecked ? TextDecoration.lineThrough : null,
                  ),
                ),
              ),
              if (document.isMandatory)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.error,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    'OBLIGATOIRE',
                    style: Theme.of(context).textTheme.labelSmall?.copyWith(
                      color: Theme.of(context).colorScheme.onError,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          subtitle: Text(document.description),
          secondary: IconButton(
            onPressed: () {
              final newExpandedItems = Set<String>.from(expandedItems);
              if (isExpanded) {
                newExpandedItems.remove(document.id);
              } else {
                newExpandedItems.add(document.id);
              }
              ref.read(expandedItemsProvider.notifier).state = newExpandedItems;
            },
            icon: Icon(
              isExpanded ? Icons.expand_less : Icons.expand_more,
            ),
          ),
        ),
        if (isExpanded) _buildDocumentDetails(context, document),
      ],
    );
  }

  Widget _buildDocumentDetails(BuildContext context, ChecklistDocument document) {
    return Container(
      margin: const EdgeInsets.only(left: 16, right: 16, bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (document.helpText != null) ...[
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Aide',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              document.helpText!,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
          ],
          if (document.examples.isNotEmpty) ...[
            Row(
              children: [
                Icon(
                  Icons.list_alt,
                  size: 16,
                  color: Theme.of(context).colorScheme.secondary,
                ),
                const SizedBox(width: 8),
                Text(
                  'Exemples',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    color: Theme.of(context).colorScheme.secondary,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            ...document.examples.map((example) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Row(
                    children: [
                      const SizedBox(width: 16),
                      Icon(
                        Icons.circle,
                        size: 6,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          example,
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
        ],
      ),
    );
  }

  List<ChecklistCategory> _filterCategories(
    List<ChecklistCategory> categories,
    String searchQuery,
    String? selectedCategory,
  ) {
    var filtered = categories;

    // Filter by category
    if (selectedCategory != null) {
      filtered = filtered.where((cat) => cat.id == selectedCategory).toList();
    }

    // Filter by search query
    if (searchQuery.isNotEmpty) {
      filtered = filtered.map((category) {
        final filteredDocuments = category.documents.where((doc) {
          return doc.title.toLowerCase().contains(searchQuery.toLowerCase()) ||
              doc.description.toLowerCase().contains(searchQuery.toLowerCase()) ||
              doc.examples.any((example) => 
                  example.toLowerCase().contains(searchQuery.toLowerCase()));
        }).toList();

        return ChecklistCategory(
          id: category.id,
          title: category.title,
          description: category.description,
          documents: filteredDocuments,
        );
      }).where((category) => category.documents.isNotEmpty).toList();
    }

    return filtered;
  }

  void _exportChecklist(
    BuildContext context,
    ChecklistData data,
    Set<String> checkedItems,
  ) {
    final buffer = StringBuffer();
    buffer.writeln('Check-list Liasse Fiscale 2025');
    buffer.writeln('================================');
    buffer.writeln();

    for (final category in data.categories) {
      buffer.writeln('${category.title}:');
      buffer.writeln('-' * category.title.length);
      
      for (final document in category.documents) {
        final status = checkedItems.contains(document.id) ? '✓' : '☐';
        final mandatory = document.isMandatory ? ' (OBLIGATOIRE)' : '';
        buffer.writeln('$status ${document.title}$mandatory');
        buffer.writeln('   ${document.description}');
        if (document.examples.isNotEmpty) {
          buffer.writeln('   Exemples: ${document.examples.join(', ')}');
        }
        buffer.writeln();
      }
      buffer.writeln();
    }

    final totalItems = data.categories.expand((cat) => cat.documents).length;
    final completedItems = checkedItems.length;
    buffer.writeln('Progression: $completedItems/$totalItems documents (${(completedItems / totalItems * 100).toInt()}%)');

    Share.share(
      buffer.toString(),
      subject: 'Check-list Liasse Fiscale 2025',
    );
  }
}