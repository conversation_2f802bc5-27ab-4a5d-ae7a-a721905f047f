import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';

class GuideSection extends StatefulWidget {
  const GuideSection({super.key});

  @override
  State<GuideSection> createState() => _GuideSectionState();
}

class _GuideSectionState extends State<GuideSection> {
  Map<String, dynamic>? _guideData;
  bool _isLoading = true;
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadGuideData();
  }

  Future<void> _loadGuideData() async {
    try {
      final String jsonString = await rootBundle.loadString(
        'assets/liasse_fiscale/guide_liasse_2025.json',
      );
      final Map<String, dynamic> data = json.decode(jsonString);
      setState(() {
        _guideData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _error = 'Erreur lors du chargement des données: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              _error!,
              style: Theme.of(context).textTheme.bodyLarge,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () {
                setState(() {
                  _isLoading = true;
                  _error = null;
                });
                _loadGuideData();
              },
              child: const Text('Réessayer'),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeaderSection(context),
          const SizedBox(height: 24),
          _buildOverviewSection(context),
          const SizedBox(height: 16),
          _buildFinancialStatementsSection(context),
          const SizedBox(height: 16),
          _buildTaxFormsSection(context),
          const SizedBox(height: 16),
          _buildPreparationProcessSection(context),
          const SizedBox(height: 16),
          _buildElectronicFilingSection(context),
          const SizedBox(height: 16),
          _buildCommonErrorsSection(context),
          const SizedBox(height: 16),
          _buildBestPracticesSection(context),
          const SizedBox(height: 16),
          _buildImportantNotesSection(context),
        ],
      ),
    );
  }

  Widget _buildHeaderSection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.primary,
            colorScheme.primaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Liasse Fiscale',
            style: textTheme.headlineMedium?.copyWith(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Guide complet pour la préparation et le dépôt de la liasse fiscale au Maroc',
            style: textTheme.titleMedium?.copyWith(
              color: colorScheme.onPrimary.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOverviewSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Vue d\'ensemble',
      icon: Icons.info_outline,
      content: '''La liasse fiscale est l'ensemble des documents comptables et fiscaux que les entreprises doivent déposer annuellement auprès de l'administration fiscale marocaine. Elle comprend les états financiers, les déclarations fiscales et les annexes obligatoires.

Qui doit déposer :
• Toutes les sociétés soumises à l'IS
• Les entreprises individuelles soumises à l'IR professionnel (régime du résultat net réel)
• Les associations et organismes sans but lucratif exerçant une activité économique

Délais de dépôt :
• Avant le 31 mars de l'année suivant l'exercice clos
• Avant le 31 décembre pour les exercices clos au 31 décembre de l'année précédente
• Dans les 3 mois suivant la date de clôture pour les exercices non coïncidents''',
    );
  }

  Widget _buildFinancialStatementsSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'États Financiers Obligatoires',
      icon: Icons.account_balance_outlined,
      items: [
        'Bilan (actif et passif) - État de synthèse ESG',
        'Compte de Produits et Charges (CPC) - État de synthèse ESG',
        'État des Soldes de Gestion (ESG)',
        'Tableau de Financement (TF)',
        'État des Informations Complémentaires (ETIC)',
        'État des Dérogations (si applicable)',
        'Rapport de gestion (pour certaines sociétés)',
        'Rapport du commissaire aux comptes (si applicable)',
      ],
      additionalContent: '''
Préparation des états financiers :
• Respecter les normes comptables marocaines (CGNC)
• Assurer la cohérence entre tous les états
• Vérifier les reports à nouveau et les provisions
• Contrôler les amortissements et les dépréciations
• Valider les créances et les dettes''',
    );
  }

  Widget _buildTaxFormsSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Déclarations Fiscales',
      icon: Icons.description_outlined,
      items: [
        'Déclaration annuelle de l\'IS (Modèle 8305)',
        'Déclaration de la TVA (Modèle 8306 si applicable)',
        'État des retenues à la source (Modèle 8307)',
        'Déclaration des plus-values (si applicable)',
        'État des provisions (Modèle 8308)',
        'Tableau des amortissements (Modèle 8309)',
        'État des créances et dettes (Modèle 8310)',
        'Déclaration des revenus de source étrangère (si applicable)',
      ],
      additionalContent: '''
Points d'attention :
• Vérifier la concordance avec les états financiers
• Respecter les règles fiscales spécifiques
• Calculer correctement les réintégrations et déductions
• Appliquer les taux d'IS appropriés
• Tenir compte des crédits d'impôt et acomptes versés''',
    );
  }

  Widget _buildPreparationProcessSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Processus de Préparation',
      icon: Icons.timeline_outlined,
      content: '''Étapes recommandées pour la préparation :

1. Préparation préliminaire (Janvier-Février)
   • Clôture des comptes de l'exercice
   • Inventaire physique et valorisation
   • Calcul des provisions et amortissements
   • Révision des écritures de régularisation

2. Établissement des états financiers (Février-Mars)
   • Préparation du bilan et du CPC
   • Calcul de l'ESG et du tableau de financement
   • Rédaction de l'ETIC
   • Contrôle de cohérence

3. Calculs fiscaux (Mars)
   • Détermination du résultat fiscal
   • Calcul de l'IS et des acomptes
   • Préparation des déclarations annexes
   • Vérification des retenues à la source

4. Finalisation et dépôt (Avant le 31 mars)
   • Contrôle final de tous les documents
   • Signature par les dirigeants
   • Dépôt électronique ou physique
   • Conservation des justificatifs''',
    );
  }

  Widget _buildElectronicFilingSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Télédéclaration',
      icon: Icons.cloud_upload_outlined,
      content: '''Procédure de dépôt électronique :

Prérequis techniques :
• Connexion internet stable
• Navigateur web compatible
• Certificat électronique valide
• Scanner pour les pièces jointes

Étapes de télédéclaration :
1. Connexion au portail SIMPL (www.tax.gov.ma)
2. Authentification avec certificat électronique
3. Sélection du service "Liasse fiscale"
4. Saisie ou import des données
5. Vérification et validation
6. Transmission électronique
7. Réception de l'accusé de réception

Avantages :
• Gain de temps et réduction des déplacements
• Contrôles automatiques de cohérence
• Accusé de réception immédiat
• Archivage électronique sécurisé
• Réduction des risques d'erreur''',
    );
  }

  Widget _buildCommonErrorsSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Erreurs Fréquentes',
      icon: Icons.warning_outlined,
      items: [
        'Incohérence entre le bilan et le CPC',
        'Erreurs dans le calcul des amortissements',
        'Omission de provisions obligatoires',
        'Mauvaise application des règles fiscales',
        'Erreurs dans les reports à nouveau',
        'Oubli de déclarations annexes',
        'Non-respect des délais de dépôt',
        'Signatures manquantes ou incorrectes',
        'Documents incomplets ou illisibles',
        'Erreurs de calcul de l\'IS',
      ],
      additionalContent: '''
Prévention des erreurs :
• Utiliser des logiciels de comptabilité certifiés
• Effectuer des contrôles croisés réguliers
• Former le personnel aux évolutions réglementaires
• Faire appel à un expert-comptable si nécessaire
• Conserver une documentation complète''',
    );
  }

  Widget _buildBestPracticesSection(BuildContext context) {
    return _buildSection(
      context,
      title: 'Bonnes Pratiques',
      icon: Icons.star_outline,
      items: [
        'Planifier la clôture dès le début de l\'exercice',
        'Tenir une comptabilité régulière et à jour',
        'Effectuer des rapprochements bancaires mensuels',
        'Documenter toutes les écritures comptables',
        'Conserver tous les justificatifs',
        'Former l\'équipe comptable aux évolutions',
        'Utiliser des outils de contrôle automatisés',
        'Préparer un dossier de révision complet',
        'Anticiper les questions de l\'administration',
        'Maintenir une veille réglementaire active',
      ],
      additionalContent: '''
Conseils professionnels :
• Établir un calendrier de clôture détaillé
• Mettre en place des procédures de contrôle interne
• Documenter les choix comptables et fiscaux
• Préparer les réponses aux contrôles éventuels
• Archiver méthodiquement tous les documents''',
    );
  }

  Widget _buildImportantNotesSection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.tertiaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.tertiary.withOpacity(0.2),
        ),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 24,
            color: colorScheme.tertiary,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Points Importants',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.tertiary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '''• Le dépôt tardif entraîne une pénalité de 500 DH par mois de retard
• Les états financiers doivent être certifiés par un expert-comptable pour certaines sociétés
• La liasse fiscale doit être conservée pendant 10 ans
• En cas d'erreur, une déclaration rectificative peut être déposée
• L'administration peut demander des pièces justificatives complémentaires''',
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.tertiary,
                    height: 1.5,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required IconData icon,
    String? content,
    List<String>? items,
    String? additionalContent,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: ExpansionTile(
        leading: Icon(
          icon,
          color: colorScheme.primary,
          size: 24,
        ),
        title: Text(
          title,
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        children: [
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (content != null)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colorScheme.outlineVariant,
                      ),
                    ),
                    child: Text(
                      content,
                      style: textTheme.bodyLarge?.copyWith(
                        color: colorScheme.onSurface,
                        height: 1.5,
                      ),
                    ),
                  ),
                if (items != null) ...[
                  if (content != null) const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colorScheme.outlineVariant,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: items.map(
                        (item) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                margin: const EdgeInsets.only(top: 8),
                                width: 6,
                                height: 6,
                                decoration: BoxDecoration(
                                  color: colorScheme.primary,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  item,
                                  style: textTheme.bodyLarge?.copyWith(
                                    color: colorScheme.onSurface,
                                    height: 1.5,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      ).toList(),
                    ),
                  ),
                ],
                if (additionalContent != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: colorScheme.secondaryContainer.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colorScheme.secondary.withOpacity(0.2),
                      ),
                    ),
                    child: Text(
                      additionalContent,
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSecondaryContainer,
                        height: 1.5,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
        ],
      ),
    );
  }
}