import 'package:flutter/material.dart';

class IrAgricoleSection extends StatelessWidget {
  const IrAgricoleSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionCard(
            context,
            'Détermination du Revenu',
            Icons.calculate,
            Colors.blue,
            [
              _buildSubSection('Régime du Bénéfice Réel', [
                _buildInfoTile('Conditions', 'Option du contribuable ou CA > 2 000 000 MAD'),
                _buildInfoTile('Calcul', 'Recettes - Charges déductibles'),
                _buildInfoTile('Avantages', 'Étalement sur 4 ans, report déficitaire illimité'),
                _buildInfoTile('Provisions', 'Aléas climatiques autorisées'),
              ]),
              const SizedBox(height: 12),
              _buildSubSection('Régime Forfaitaire', [
                _buildInfoTile('Application', 'Exploitations traditionnelles'),
                _buildInfoTile('Base', 'Surface × Rendement × Prix de référence'),
                _buildInfoTile('Céréales', 'Blé dur: 80, Blé tendre: 75, Orge: 70'),
                _buildInfoTile('Légumineuses', 'Lentilles: 120, Pois chiches: 110'),
              ]),
              const SizedBox(height: 12),
              _buildSubSection('Moyenne Triennale', [
                _buildInfoTile('Usage', 'Revenus très irréguliers'),
                _buildInfoTile('Calcul', 'Somme des 3 derniers revenus ÷ 3'),
                _buildInfoTile('Conditions', 'Demande expresse + cultures saisonnières'),
              ]),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Charges Déductibles',
            Icons.receipt_long,
            Colors.green,
            [
              _buildSubSection('Charges Courantes', [
                _buildInfoTile('Achats consommés', 'Semences, plants, engrais, produits phytosanitaires'),
                _buildInfoTile('Charges externes', 'Travaux par tiers, transports, assurances'),
                _buildInfoTile('Impôts et taxes', 'Taxe professionnelle, droits d\'eau (sauf IR)'),
                _buildInfoTile('Frais vétérinaires', 'Soins et médicaments pour animaux'),
              ]),
              const SizedBox(height: 12),
              _buildSubSection('Amortissements Agricoles', [
                _buildInfoTile('Tracteurs', '20 ans - Matériel de base'),
                _buildInfoTile('Matériel travail du sol', '15 ans - Charrues, herses'),
                _buildInfoTile('Matériel de récolte', '12 ans - Moissonneuses'),
                _buildInfoTile('Installations', 'Serres: 10 ans, Irrigation: 8 ans'),
              ]),
              const SizedBox(height: 12),
              _buildSubSection('Provisions Réglementées', [
                _buildInfoTile('Provision sécheresse', '10% des recettes cultures pluviales'),
                _buildInfoTile('Provision cheptel', '15% valeur cheptel reproducteur'),
                _buildInfoTile('Reprise obligatoire', 'Au bout de 5 ans maximum'),
              ]),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Exonérations et Réductions',
            Icons.discount,
            Colors.orange,
            [
              _buildSubSection('Exonérations Temporaires', [
                _buildInfoTile('Jeunes agriculteurs', '5 ans (âge < 35 ans, diplôme requis)'),
                _buildInfoTile('Agriculture biologique', '10 ans (abattement 50%)'),
                _buildInfoTile('Investissements modernisation', '3 ans (déduction majorée 150%)'),
              ]),
              const SizedBox(height: 12),
              _buildSubSection('Abattements Permanents', [
                _buildInfoTile('Abattement familial', '30 000 MAD + 5 000 par personne à charge'),
                _buildInfoTile('Zone de montagne', '25% (altitude > 800m, revenus < 100 000)'),
                _buildInfoTile('Petites exploitations', '24 000 MAD (revenus < 120 000)'),
              ]),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Barème Progressif 2025',
            Icons.trending_up,
            Colors.purple,
            [
              _buildTaxBracket('0 - 30 000 MAD', '0%', 'Tranche exonérée'),
              _buildTaxBracket('30 001 - 50 000 MAD', '10%', 'Revenus modestes'),
              _buildTaxBracket('50 001 - 60 000 MAD', '20%', 'Revenus moyens'),
              _buildTaxBracket('60 001 - 80 000 MAD', '30%', 'Revenus élevés'),
              _buildTaxBracket('80 001 - 180 000 MAD', '34%', 'Hauts revenus'),
              _buildTaxBracket('> 180 000 MAD', '38%', 'Très hauts revenus'),
              const SizedBox(height: 12),
              _buildCalculatorExample(),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Obligations Déclaratives',
            Icons.assignment,
            Colors.red,
            [
              _buildSubSection('Déclaration Annuelle', [
                _buildInfoTile('Date limite', '31 mars de chaque année'),
                _buildInfoTile('Formulaire', 'Déclaration modèle ADP (Agricole)'),
                _buildInfoTile('Documents', 'État des revenus, justificatifs charges'),
                _buildInfoTile('Inventaire', 'Cheptel début et fin d\'exercice'),
              ]),
              const SizedBox(height: 12),
              _buildSubSection('Tenue de Comptabilité', [
                _buildInfoTile('Régime réel', 'Comptabilité complète CGNC'),
                _buildInfoTile('Régime forfaitaire', 'Livre-journal simplifié'),
                _buildInfoTile('Conservation', '10 ans pour tous documents'),
              ]),
              const SizedBox(height: 12),
              _buildSubSection('Versements Anticipés', [
                _buildInfoTile('Acomptes', '25% × 3 (juin, septembre, décembre)'),
                _buildInfoTile('Base', 'IR de l\'année précédente'),
                _buildInfoTile('Exonération', 'Première année d\'activité'),
              ]),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Cas Particuliers',
            Icons.cases,
            Colors.teal,
            [
              _buildSubSection('Pluriactivité', [
                _buildInfoTile('Définition', 'Activités agricoles et non agricoles simultanées'),
                _buildInfoTile('Traitement', 'Régimes distincts par catégorie'),
                _buildInfoTile('Seuils', 'Activité principale > 60% du CA total'),
              ]),
              const SizedBox(height: 12),
              _buildSubSection('Groupements Agricoles', [
                _buildInfoTile('GAEC', 'Transparence fiscale - imposition des associés'),
                _buildInfoTile('SCEA', 'Société civile - imposition des associés'),
                _buildInfoTile('Coopératives', 'Imposition au niveau coopérative (IS)'),
              ]),
              const SizedBox(height: 12),
              _buildSubSection('Successions et Donations', [
                _buildInfoTile('Valorisation', 'Valeur vénale au jour du décès'),
                _buildInfoTile('Abattement', '50% pour terres agricoles'),
                _buildInfoTile('Report paiement', 'Possible sur 10 ans pour héritiers'),
              ]),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color iconColor,
    List<Widget> children,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: iconColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: iconColor, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildSubSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 16,
            color: Colors.black87,
          ),
        ),
        const SizedBox(height: 8),
        ...children,
      ],
    );
  }

  Widget _buildInfoTile(String title, String subtitle) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 8, right: 12),
            decoration: BoxDecoration(
              color: Colors.grey[400],
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTaxBracket(String range, String rate, String description) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Row(
        children: [
          Expanded(
            flex: 3,
            child: Text(
              range,
              style: const TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 13,
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Text(
              rate,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 14,
                color: Colors.purple,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            flex: 2,
            child: Text(
              description,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.right,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalculatorExample() {
    return Container(
      padding: const EdgeInsets.all(16.0),
      decoration: BoxDecoration(
        color: Colors.blue[50],
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue[200]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.calculate, color: Colors.blue[700], size: 20),
              const SizedBox(width: 8),
              Text(
                'Exemple de Calcul',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.blue[700],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Exploitant marié, 2 enfants, revenus 90 000 MAD',
            style: TextStyle(
              fontWeight: FontWeight.w500,
              fontSize: 14,
              color: Colors.grey[700],
            ),
          ),
          const SizedBox(height: 8),
          _buildCalculationStep('1. Revenus bruts agricoles', '90 000 MAD'),
          _buildCalculationStep('2. Charges déductibles', '- 15 000 MAD'),
          _buildCalculationStep('3. Revenus nets', '75 000 MAD'),
          _buildCalculationStep('4. Abattement familial (30 000 + 2×5 000)', '- 40 000 MAD'),
          _buildCalculationStep('5. Base imposable', '35 000 MAD'),
          _buildCalculationStep('6. IR dû (0 + 5 000×10%)', '500 MAD', isResult: true),
        ],
      ),
    );
  }

  Widget _buildCalculationStep(String description, String amount, {bool isResult = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Text(
              description,
              style: TextStyle(
                fontSize: 13,
                fontWeight: isResult ? FontWeight.bold : FontWeight.normal,
                color: isResult ? Colors.blue[800] : Colors.grey[700],
              ),
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              fontSize: 13,
              fontWeight: FontWeight.w600,
              color: isResult ? Colors.blue[800] : Colors.grey[800],
            ),
          ),
        ],
      ),
    );
  }
}
