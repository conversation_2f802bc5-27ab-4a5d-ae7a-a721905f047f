import 'package:flutter/material.dart';

class IsAgricoleSection extends StatelessWidget {
  const IsAgricoleSection({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionCard(
            context,
            'Coopératives Agricoles',
            Icons.group,
            Colors.green,
            [
              _buildInfoTile('Taux IS préférentiel', '17,5% au lieu de 22,75%'),
              _buildInfoTile('Conditions', 'Statut coopératif agréé + activité agricole'),
              _buildInfoTile('Avantages', 'Exonération opérations avec adhérents'),
              _buildInfoTile('Report déficitaire', 'Illimité pour activités agricoles'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Nouvelles Entreprises Agricoles',
            Icons.new_releases,
            Colors.blue,
            [
              _buildInfoTile('Exonération totale', '5 années consécutives'),
              _buildInfoTile('Conditions création', 'Après 01/01/2020'),
              _buildInfoTile('Investissement minimum', '1 million MAD'),
              _buildInfoTile('Emplois créés', 'Au moins 10 emplois permanents'),
              _buildInfoTile('Après exonération', '17,5% pendant 5 ans puis taux normal'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Zones Agricoles Prioritaires',
            Icons.map,
            Colors.orange,
            [
              _buildInfoTile('Plan Maroc Vert', '15% (Souss-Massa, Oriental, Béni Mellal)'),
              _buildInfoTile('Zones Arides', '10% pendant 15 ans (Drâa-Tafilalet)'),
              _buildInfoTile('Régions Défavorisées', '12,5% pendant 12 ans'),
              _buildInfoTile('Conditions', 'Investissements > 10 millions MAD'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Cotisation Minimale Agricole',
            Icons.calculate,
            Colors.purple,
            [
              _buildInfoTile('Taux réduit', '0,25% du CA (au lieu de 0,5%)'),
              _buildInfoTile('Minimum spécial', '1 500 MAD (au lieu de 3 000 MAD)'),
              _buildInfoTile('Exonération coopératives', 'Permanente si statuts respectés'),
              _buildInfoTile('Nouvelles entreprises', '60 mois d\'exonération'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Déductions Spécifiques',
            Icons.remove_circle_outline,
            Colors.teal,
            [
              _buildInfoTile('Provision aléas climatiques', '5% du CA agricole'),
              _buildInfoTile('Provision cheptel', '20% valeur cheptel reproducteur'),
              _buildInfoTile('Provision replantation', '15% valeur plantations'),
              _buildInfoTile('Amortissements accélérés', 'Matériel moderne 40%'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Investissements Stratégiques',
            Icons.trending_up,
            Colors.indigo,
            [
              _buildInfoTile('Investissement minimum', '50 millions MAD'),
              _buildInfoTile('Emplois créés', 'Au moins 100 emplois'),
              _buildInfoTile('Export minimum', '30% de la production'),
              _buildInfoTile('Exonération', '5 ans puis taux 15% pendant 10 ans'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color iconColor,
    List<Widget> children,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: iconColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: iconColor, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoTile(String title, String subtitle) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 8, right: 12),
            decoration: BoxDecoration(
              color: Colors.grey[400],
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}