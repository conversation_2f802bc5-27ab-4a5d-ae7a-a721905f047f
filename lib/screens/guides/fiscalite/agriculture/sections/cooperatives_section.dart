import 'package:flutter/material.dart';

class CooperativesSection extends StatelessWidget {
  const CooperativesSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: <PERSON>um<PERSON>(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionCard(
            context,
            'Formation et Statut Juridique',
            Icons.business,
            Colors.blue,
            [
              _buildInfoTile('Conditions de création', 'Minimum 7 membres agriculteurs'),
              _buildInfoTile('Capital social', 'Minimum 1000 DH, parts nominatives'),
              _buildInfoTile('Objet social', 'Activité agricole ou para-agricole exclusive'),
              _buildInfoTile('Agrément', 'Ministère de l\'Agriculture obligatoire'),
              _buildInfoTile('Principe coopératif', 'Un homme, une voix (égalité des membres)'),
              _buildInfoTile('Documents requis', 'Statuts, PV AG constitutive, liste membres'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Régime TVA Spécial',
            Icons.receipt,
            Colors.green,
            [
              _buildInfoTile('Ventes produits adhérents', '0% - Exonération totale avec déduction'),
              _buildInfoTile('Achats groupés intrants', '0% - Fournitures aux membres exclusivement'),
              _buildInfoTile('Services aux adhérents', '10% - Stockage, conditionnement'),
              _buildInfoTile('Transformation primaire', '10% - Première transformation des produits'),
              _buildInfoTile('Ventes à non-adhérents', 'Taux normal selon le produit'),
              _buildInfoTile('Seuil franchise', '500 000 DH de CA annuel'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Impôt sur les Sociétés (IS)',
            Icons.calculate,
            Colors.orange,
            [
              _buildInfoTile('Taux préférentiel', '17,5% au lieu du taux normal'),
              _buildInfoTile('Exonération opérations', 'Transactions avec les adhérents'),
              _buildInfoTile('Report déficitaire', 'Illimité pour activités agricoles'),
              _buildInfoTile('Cotisation minimale', '0,25% du CA (taux réduit)'),
              _buildInfoTile('Minimum absolu', '1 500 DH par an'),
              _buildInfoTile('Nouvelles coopératives', 'Exonération 5 ans si conditions remplies'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Distinction Membres / Non-Membres',
            Icons.people_outline,
            Colors.purple,
            [
              _buildInfoTile('Opérations avec adhérents', 'Régime préférentiel, exonérations'),
              _buildInfoTile('Ventes à tiers', 'Taux normal, pas d\'avantage fiscal'),
              _buildInfoTile('Limite légale', 'Max 20% du CA avec non-adhérents'),
              _buildInfoTile('Comptabilisation', 'Comptes séparés obligatoires'),
              _buildInfoTile('Contrôle fiscal', 'Vérification du respect des proportions'),
              _buildInfoTile('Sanctions', 'Perte du statut si dépassement répété'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Obligations Comptables',
            Icons.book,
            Colors.teal,
            [
              _buildInfoTile('Comptabilité simplifiée', 'Autorisée si CA < 2 millions DH'),
              _buildInfoTile('Livre journal', 'Enregistrement chronologique obligatoire'),
              _buildInfoTile('Grand livre', 'Comptes de membres et tiers séparés'),
              _buildInfoTile('Inventaire annuel', 'Stocks et immobilisations'),
              _buildInfoTile('États financiers', 'Bilan, CPC, annexes'),
              _buildInfoTile('Assemblée générale', 'Approbation des comptes annuelle'),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Exemples Pratiques',
            Icons.lightbulb,
            Colors.amber,
            [
              _buildExampleTile(
                'Coopérative Laitière',
                'Collecte lait adhérents: 0% TVA\nVente lait transformé: 10% TVA\nIS: 17,5% sur bénéfices',
              ),
              _buildExampleTile(
                'Coopérative Céréalière',
                'Achat blé adhérents: 0% TVA\nVente farine: Taux normal\nStockage pour membres: 10% TVA',
              ),
              _buildExampleTile(
                'Coopérative Maraîchère',
                'Conditionnement légumes: 0% TVA\nVente directe marché: 0% TVA\nServices logistique: 10% TVA',
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context,
            'Calendrier de Conformité',
            Icons.calendar_today,
            Colors.red,
            [
              _buildInfoTile('Janvier', 'Assemblée générale ordinaire'),
              _buildInfoTile('Mars', 'Déclaration IS annuelle (31 mars)'),
              _buildInfoTile('Mensuel/Trimestriel', 'Déclarations TVA selon CA'),
              _buildInfoTile('Trimestriel', 'Acomptes provisionnels IS'),
              _buildInfoTile('Annuel', 'Rapport d\'activité au Ministère'),
              _buildInfoTile('Permanent', 'Tenue registres membres et parts'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard(
    BuildContext context,
    String title,
    IconData icon,
    Color iconColor,
    List<Widget> children,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: iconColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: iconColor, size: 24),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: textTheme.headlineSmall?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...children,
          ],
        ),
      ),
    );
  }

  Widget _buildInfoTile(String title, String subtitle) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 8, right: 12),
            decoration: BoxDecoration(
              color: Colors.grey[400],
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  subtitle,
                  style: TextStyle(
                    fontSize: 13,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExampleTile(String title, String description) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 4.0),
      padding: const EdgeInsets.all(12.0),
      decoration: BoxDecoration(
        color: Colors.grey[50],
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey[300]!),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontSize: 13,
              color: Colors.grey[700],
              height: 1.3,
            ),
          ),
        ],
      ),
    );
  }
}
