import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:convert';
import '../../../../../utils/calculation_utils.dart';
import '../../../../../utils/input_validators.dart';

class IRCalculatorSection extends StatefulWidget {
  const IRCalculatorSection({super.key});

  @override
  State<IRCalculatorSection> createState() => _IRCalculatorSectionState();
}

class _IRCalculatorSectionState extends State<IRCalculatorSection> {
  final _formKey = GlobalKey<FormState>();
  final _salaryController = TextEditingController();
  bool _isMarried = false;
  int _numberOfChildren = 0;
  bool _hasCNSS = true;
  bool _hasAMO = true;
  double _seniorityYears = 0;

  // Calculation results
  Map<String, dynamic>? _calculationResult;
  bool _isCalculating = false;

  @override
  void dispose() {
    _salaryController.dispose();
    super.dispose();
  }

  Future<void> _calculateIR() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isCalculating = true;
    });

    try {
      final grossSalary = InputValidators.parseMonetaryInput(_salaryController.text);

      // Load tax rates
      final taxRatesJson = await rootBundle.loadString('assets/tax_rates.json');
      final taxRatesData = json.decode(taxRatesJson);

      // Calculate social security contributions
      final cnssContribution = _hasCNSS
        ? CalculationUtils.calculateSocialContribution(grossSalary, 4.48, 6000)
        : 0;
      final amoContribution = _hasAMO
        ? CalculationUtils.calculateSocialContribution(grossSalary, 2.26, null)
        : 0;
      final totalSocialContributions = CalculationUtils.roundMonetary(cnssContribution + amoContribution.toDouble());

      // Calculate professional expenses
      final baseForProfExpenses = CalculationUtils.roundMonetary(grossSalary - totalSocialContributions);
      double profExpensesRate = 35;
      double profExpensesMax = 2500;

      if (baseForProfExpenses > 6500) {
        profExpensesRate = 25;
        profExpensesMax = 2916.67;
      }

      final professionalExpenses = CalculationUtils.calculateProfessionalExpenses(
        baseForProfExpenses,
        profExpensesRate,
        profExpensesMax
      );

      // Calculate taxable income
      final taxableIncome = baseForProfExpenses - professionalExpenses;

      // Calculate IR using brackets
      final irBrackets = taxRatesData['ir_brackets'] as List;
      double rawTax = 0;
      String appliedBracket = '';

      for (final bracket in irBrackets) {
        final min = bracket['min'].toDouble();
        final max = bracket['max']?.toDouble();
        final rate = bracket['rate'].toDouble();
        final deduction = bracket['deduction'].toDouble();

        if (taxableIncome >= min && (max == null || taxableIncome <= max)) {
          rawTax = (taxableIncome * rate / 100) - deduction;
          appliedBracket = '${min.toStringAsFixed(0)} - ${max?.toStringAsFixed(0) ?? '∞'} DH (${rate}%)';
          break;
        }
      }

      // Calculate family charges
      final familyCharges = taxRatesData['family_charges'];
      double familyReduction = 0;

      if (_isMarried) {
        if (_numberOfChildren == 0) {
          familyReduction = familyCharges['spouse'].toDouble();
        } else {
          switch (_numberOfChildren) {
            case 1:
              familyReduction = familyCharges['child_1'].toDouble();
              break;
            case 2:
              familyReduction = familyCharges['child_2'].toDouble();
              break;
            case 3:
              familyReduction = familyCharges['child_3'].toDouble();
              break;
            case 4:
              familyReduction = familyCharges['child_4'].toDouble();
              break;
            default:
              familyReduction = familyCharges['child_5'].toDouble();
              break;
          }
        }
      }

      // Calculate final IR
      final finalIR = (rawTax - familyReduction).clamp(0, double.infinity);

      // Calculate net salary
      final netSalary = grossSalary - totalSocialContributions - finalIR;

      setState(() {
        _calculationResult = {
          'grossSalary': grossSalary,
          'cnssContribution': cnssContribution,
          'amoContribution': amoContribution,
          'totalSocialContributions': totalSocialContributions,
          'professionalExpenses': professionalExpenses,
          'taxableIncome': taxableIncome,
          'rawTax': rawTax,
          'familyReduction': familyReduction,
          'finalIR': finalIR,
          'netSalary': netSalary,
          'appliedBracket': appliedBracket,
        };
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur de calcul: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isCalculating = false;
      });
    }
  }

  Widget _buildResultRow(String label, dynamic value, {bool isAmount = true, bool isHighlighted = false}) {
    String displayValue;
    if (isAmount && value is num) {
      displayValue = '${value.toStringAsFixed(2)} DH';
    } else {
      displayValue = value.toString();
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: TextStyle(
              fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
              fontSize: isHighlighted ? 16 : 14,
            ),
          ),
          Text(
            displayValue,
            style: TextStyle(
              fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
              fontSize: isHighlighted ? 16 : 14,
              color: isHighlighted ? Theme.of(context).colorScheme.primary : null,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCalculationStep(String label, dynamic value, {bool isDeduction = false, bool isFinal = false}) {
    String displayValue;
    if (value is num) {
      displayValue = CalculationUtils.formatMonetary(value.toDouble());
    } else {
      displayValue = value.toString();
    }

    Color? textColor;
    if (isDeduction) {
      textColor = Colors.red[600];
    } else if (isFinal) {
      textColor = Theme.of(context).colorScheme.primary;
    }

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          if (isDeduction) const Icon(Icons.remove, size: 16, color: Colors.red),
          if (!isDeduction && !isFinal) const Icon(Icons.add, size: 16, color: Colors.green),
          if (isFinal) const Icon(Icons.calculate, size: 16),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: TextStyle(
                color: textColor,
                fontWeight: isFinal ? FontWeight.bold : FontWeight.normal,
              ),
            ),
          ),
          Text(
            displayValue,
            style: TextStyle(
              color: textColor,
              fontWeight: isFinal ? FontWeight.bold : FontWeight.normal,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Calculatrice IR',
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
            ),
            const SizedBox(height: 24),

            // Salaire brut
            TextFormField(
              controller: _salaryController,
              decoration: const InputDecoration(
                labelText: 'Salaire Brut Mensuel',
                suffixText: 'DH',
                border: OutlineInputBorder(),
                helperText: 'Saisissez votre salaire brut mensuel',
              ),
              keyboardType: TextInputType.number,
              inputFormatters: InputValidators.getMonetaryInputFormatters(),
              validator: InputValidators.validateSalary,
            ),
            const SizedBox(height: 16),

            // Situation familiale
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Situation Familiale',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    SwitchListTile(
                      title: const Text('Marié(e)'),
                      value: _isMarried,
                      onChanged: (bool value) {
                        setState(() {
                          _isMarried = value;
                        });
                      },
                    ),
                    if (_isMarried) ...[
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          const Text('Nombre d\'enfants:'),
                          const SizedBox(width: 16),
                          SegmentedButton<int>(
                            segments: const [
                              ButtonSegment(value: 0, label: Text('0')),
                              ButtonSegment(value: 1, label: Text('1')),
                              ButtonSegment(value: 2, label: Text('2')),
                              ButtonSegment(value: 3, label: Text('3')),
                              ButtonSegment(value: 4, label: Text('4')),
                              ButtonSegment(value: 5, label: Text('5')),
                              ButtonSegment(value: 6, label: Text('6+')),
                            ],
                            selected: {_numberOfChildren},
                            onSelectionChanged: (Set<int> newSelection) {
                              setState(() {
                                _numberOfChildren = newSelection.first;
                              });
                            },
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Cotisations sociales
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Cotisations Sociales',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 8),
                    SwitchListTile(
                      title: const Text('CNSS'),
                      subtitle: const Text('Cotisation sociale'),
                      value: _hasCNSS,
                      onChanged: (bool value) {
                        setState(() {
                          _hasCNSS = value;
                        });
                      },
                    ),
                    SwitchListTile(
                      title: const Text('AMO'),
                      subtitle: const Text('Assurance Maladie Obligatoire'),
                      value: _hasAMO,
                      onChanged: (bool value) {
                        setState(() {
                          _hasAMO = value;
                        });
                      },
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),

            // Ancienneté
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Ancienneté',
                      style: Theme.of(context).textTheme.titleMedium,
                    ),
                    const SizedBox(height: 16),
                    Slider(
                      value: _seniorityYears,
                      min: 0,
                      max: 30,
                      divisions: 30,
                      label: '${_seniorityYears.round()} ans',
                      onChanged: (double value) {
                        setState(() {
                          _seniorityYears = value;
                        });
                      },
                    ),
                    Center(
                      child: Text(
                        '${_seniorityYears.round()} ans d\'ancienneté',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 24),

            // Bouton de calcul
            SizedBox(
              width: double.infinity,
              child: FilledButton.icon(
                onPressed: _isCalculating ? null : _calculateIR,
                icon: _isCalculating
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Icon(Icons.calculate),
                label: Text(_isCalculating ? 'Calcul en cours...' : 'Calculer l\'IR'),
              ),
            ),

            // Résultats
            if (_calculationResult != null) ...[
              const SizedBox(height: 24),
              Card(
                elevation: 4,
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Résultats du calcul IR',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 16),
                      _buildResultRow('Salaire brut', _calculationResult!['grossSalary']),
                      _buildResultRow('CNSS (4.48%)', _calculationResult!['cnssContribution']),
                      _buildResultRow('AMO (2.26%)', _calculationResult!['amoContribution']),
                      _buildResultRow('Frais professionnels', _calculationResult!['professionalExpenses']),
                      _buildResultRow('Revenu net imposable', _calculationResult!['taxableIncome']),
                      _buildResultRow('Tranche appliquée', _calculationResult!['appliedBracket'], isAmount: false),
                      _buildResultRow('IR brut', _calculationResult!['rawTax']),
                      _buildResultRow('Réduction familiale', _calculationResult!['familyReduction']),
                      const Divider(),
                      _buildResultRow('IR final', _calculationResult!['finalIR'], isHighlighted: true),
                      _buildResultRow('Salaire net', _calculationResult!['netSalary'], isHighlighted: true),
                      const SizedBox(height: 16),
                      ExpansionTile(
                        title: const Text('Détail du calcul'),
                        children: [
                          Padding(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildCalculationStep('1. Salaire brut', _calculationResult!['grossSalary']),
                                _buildCalculationStep('2. Cotisations sociales', _calculationResult!['totalSocialContributions'], isDeduction: true),
                                _buildCalculationStep('3. Frais professionnels', _calculationResult!['professionalExpenses'], isDeduction: true),
                                _buildCalculationStep('4. Revenu net imposable', _calculationResult!['taxableIncome']),
                                _buildCalculationStep('5. IR brut (${_calculationResult!['appliedBracket']})', _calculationResult!['rawTax']),
                                _buildCalculationStep('6. Réduction familiale', _calculationResult!['familyReduction'], isDeduction: true),
                                _buildCalculationStep('7. IR final', _calculationResult!['finalIR'], isFinal: true),
                                _buildCalculationStep('8. Salaire net', _calculationResult!['netSalary'], isFinal: true),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
