import 'package:flutter/material.dart';

class ExemplesSection extends StatelessWidget {
  const ExemplesSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Exemples Pratiques',
                  style: textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Cas concrets de calcul de l\'IR pour différentes situations',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onPrimary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          _buildExemple(
            context,
            title: 'Cas d\'un salarié avec avantages',
            icon: Icons.work_outline,
            content: '''Un cadre dans une entreprise perçoit:
• Salaire mensuel de base: 18.000 DH
• Prime de transport: 2.000 DH
• Avantage en nature (logement): 3.000 DH

Calcul de l'IR mensuel:
1. Base imposable:
• Salaire de base: 18.000 DH
• Prime de transport: 2.000 DH
• Avantage logement: 3.000 DH
• Total: 23.000 DH

2. Calcul IR:
• Tranche > 15.000 DH
• IR = (23.000 × 37%) - 2.283,33
• IR = 8.510 - 2.283,33
• IR = 6.226,67 DH''',
          ),
          const SizedBox(height: 16),
          _buildExemple(
            context,
            title: 'Cas d\'un professionnel libéral',
            icon: Icons.business_center_outlined,
            content: '''Un médecin exerçant en libéral déclare:
• Recettes annuelles: 800.000 DH
• Charges déductibles: 300.000 DH

Calcul de l'IR annuel:
1. Bénéfice net imposable:
• 800.000 - 300.000 = 500.000 DH

2. Calcul IR:
• Tranche > 180.000 DH
• IR = (500.000 × 37%) - 27.400
• IR = 185.000 - 27.400
• IR = 157.600 DH

3. Acomptes provisionnels:
• 25% de l'IR: 39.400 DH (à verser trimestriellement)''',
          ),
          const SizedBox(height: 16),
          _buildExemple(
            context,
            title: 'Cas des revenus fonciers',
            icon: Icons.home_outlined,
            content: '''Un propriétaire loue:
• Un local commercial: 15.000 DH/mois
• Un appartement: 5.000 DH/mois

Calcul de l'IR annuel:
1. Revenu foncier brut annuel:
• Local: 15.000 × 12 = 180.000 DH
• Appartement: 5.000 × 12 = 60.000 DH
• Total: 240.000 DH

2. Abattement de 40%:
• 240.000 × 60% = 144.000 DH (revenu net imposable)

3. Calcul IR:
• Tranche 100.001 - 180.000 DH
• IR = (144.000 × 34%) - 22.000
• IR = 48.960 - 22.000
• IR = 26.960 DH''',
          ),
        ],
      ),
    );
  }

  Widget _buildExemple(
    BuildContext context, {
    required String title,
    required String content,
    required IconData icon,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    colorScheme.primary,
                    colorScheme.primaryContainer,
                  ],
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                children: [
                  Icon(
                    icon,
                    color: colorScheme.onPrimary,
                    size: 24,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      title,
                      style: textTheme.titleLarge?.copyWith(
                        color: colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 20),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: content.split('\n\n').map((section) {
                  final lines = section.split('\n');
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (lines.first.contains(':'))
                        Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Text(
                            lines.first,
                            style: textTheme.titleMedium?.copyWith(
                              color: colorScheme.primary,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ...lines.skip(lines.first.contains(':') ? 1 : 0).map(
                            (line) => Padding(
                              padding: const EdgeInsets.only(bottom: 4),
                              child: Row(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  if (line.startsWith('•'))
                                    Padding(
                                      padding: const EdgeInsets.only(top: 8),
                                      child: Container(
                                        width: 6,
                                        height: 6,
                                        decoration: BoxDecoration(
                                          color: colorScheme.primary,
                                          shape: BoxShape.circle,
                                        ),
                                      ),
                                    )
                                  else
                                    Text(
                                      '${line.split('.').first}.',
                                      style: textTheme.titleSmall?.copyWith(
                                        color: colorScheme.primary,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  const SizedBox(width: 12),
                                  Expanded(
                                    child: Text(
                                      line.startsWith('•')
                                          ? line.substring(2)
                                          : line
                                              .split('.')
                                              .skip(1)
                                              .join('.')
                                              .trim(),
                                      style: textTheme.bodyLarge?.copyWith(
                                        color: colorScheme.onSurface,
                                        height: 1.5,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                      if (section != content.split('\n\n').last)
                        const Padding(
                          padding: EdgeInsets.symmetric(vertical: 12),
                          child: Divider(),
                        ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
