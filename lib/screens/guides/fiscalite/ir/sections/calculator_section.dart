import 'package:flutter/material.dart';
import '../../../../../models/salary/salary_data.dart';
import '../../../../../widgets/ir/real_time_calculator_controller.dart';
import '../../../../../widgets/ir/basic_info_card.dart';
import '../../../../../widgets/ir/allowances_card.dart';
import '../../../../../widgets/ir/overtime_card.dart';
import '../../../../../widgets/ir/bonuses_card.dart';
import '../../../../../widgets/ir/family_status_card.dart';
import '../../../../../widgets/ir/professional_expenses_card.dart';
import '../../../../../widgets/ir/real_time_preview_card.dart';
import '../../../../../widgets/ir/calculation_mode_selector.dart';

class CalculatorSection extends StatefulWidget {
  const CalculatorSection({super.key});

  @override
  State<CalculatorSection> createState() => _CalculatorSectionState();
}

class _CalculatorSectionState extends State<CalculatorSection> {
  // Real-time calculator controller
  late final RealTimeCalculatorController _calculatorController;

  // Form controllers
  final _baseSalaryController = TextEditingController();
  final _transportController = TextEditingController();
  final _housingController = TextEditingController();
  final _otherAllowancesController = TextEditingController();
  final _yearsOfServiceController = TextEditingController();
  final _regularOvertimeController = TextEditingController();
  final _holidayOvertimeController = TextEditingController();
  final _nightOvertimeController = TextEditingController();

  // Form state
  String _employeeType = 'cadre';
  bool _includeCNSS = true;
  bool _isMarried = false;
  bool _isOvertimeExpanded = false;
  
  // Dynamic sections visibility
  final Map<String, bool> _sectionVisibility = {
    'allowances': false,
    'overtime': false,
    'bonuses': false,
    'family': false,
    'professionalExpenses': false,
  };

  // Bonuses management
  final Map<String, (TextEditingController, bool, bool)> _bonuses = {
    'Prime de rendement': (TextEditingController(), false, false),
    'Prime d\'ancienneté': (TextEditingController(), false, false),
    'Prime de risque': (TextEditingController(), false, false),
    'Prime de responsabilité': (TextEditingController(), false, false),
    '13ème mois': (TextEditingController(), false, true),
    'Prime de fin d\'année': (TextEditingController(), false, true),
  };

  // Family status management
  final List<(TextEditingController, bool)> _dependents = [];

  @override
  void initState() {
    super.initState();
    _calculatorController = RealTimeCalculatorController();
    
    // Add listeners to all form controllers for real-time calculation
    _baseSalaryController.addListener(_triggerCalculation);
    _transportController.addListener(_triggerCalculation);
    _housingController.addListener(_triggerCalculation);
    _otherAllowancesController.addListener(_triggerCalculation);
    _yearsOfServiceController.addListener(_triggerCalculation);
    _regularOvertimeController.addListener(_triggerCalculation);
    _holidayOvertimeController.addListener(_triggerCalculation);
    _nightOvertimeController.addListener(_triggerCalculation);
  }

  @override
  void dispose() {
    _calculatorController.dispose();
    _baseSalaryController.dispose();
    _transportController.dispose();
    _housingController.dispose();
    _otherAllowancesController.dispose();
    _yearsOfServiceController.dispose();
    _regularOvertimeController.dispose();
    _holidayOvertimeController.dispose();
    _nightOvertimeController.dispose();

    for (final bonus in _bonuses.values) {
      bonus.$1.dispose();
    }

    for (final dependent in _dependents) {
      dependent.$1.dispose();
    }

    super.dispose();
  }

  void _triggerCalculation() {
    final data = _buildSalaryData();
    _calculatorController.triggerCalculation(data);
  }

  SalaryData _buildSalaryData() {
    return SalaryData(
      baseSalary: double.tryParse(_baseSalaryController.text.replaceAll(' ', '')) ?? 0,
      transportAllowance: double.tryParse(_transportController.text.replaceAll(' ', '')) ?? 0,
      housingAllowance: double.tryParse(_housingController.text.replaceAll(' ', '')) ?? 0,
      otherAllowances: double.tryParse(_otherAllowancesController.text.replaceAll(' ', '')) ?? 0,
      employeeType: _employeeType,
      yearsOfService: int.tryParse(_yearsOfServiceController.text) ?? 0,
      includeCNSS: _includeCNSS,
      overtime: OvertimeHours(
        regularHours: double.tryParse(_regularOvertimeController.text) ?? 0,
        holidayHours: double.tryParse(_holidayOvertimeController.text) ?? 0,
        nightHours: double.tryParse(_nightOvertimeController.text) ?? 0,
      ),
      bonuses: Map.fromEntries(
        _bonuses.entries.map((e) => MapEntry(
          e.key,
          BonusEntry(
            enabled: e.value.$2,
            amount: double.tryParse(e.value.$1.text.replaceAll(' ', '')) ?? 0,
            isAnnual: e.value.$3,
          ),
        )),
      ),
      familyStatus: FamilyStatus(
        isMarried: _isMarried,
        dependents: _dependents
            .map((e) => Dependent(
                  age: int.tryParse(e.$1.text) ?? 0,
                  isDisabled: e.$2,
                ))
            .toList(),
      ),
    );
  }

  void _onModeChanged(bool isAnnual) {
    _calculatorController.useAnnualMode = isAnnual;
  }

  void _onSectionVisibilityChanged(String section, bool isVisible) {
    setState(() {
      _sectionVisibility[section] = isVisible;
    });
  }

  void _onBonusToggled(String bonusName, bool enabled) {
    setState(() {
      final (controller, _, isAnnual) = _bonuses[bonusName]!;
      _bonuses[bonusName] = (controller, enabled, isAnnual);
    });
    _triggerCalculation();
  }

  void _onAddBonus() {
    final controller = TextEditingController();
    controller.addListener(_triggerCalculation);
    
    showDialog(
      context: context,
      builder: (context) {
        String bonusName = '';
        bool isAnnual = false;
        
        return AlertDialog(
          title: Text('Ajouter une prime'),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                decoration: InputDecoration(
                  labelText: 'Nom de la prime',
                  border: OutlineInputBorder(),
                ),
                onChanged: (value) => bonusName = value,
              ),
              const SizedBox(height: 16),
              CheckboxListTile(
                title: Text('Prime annuelle'),
                value: isAnnual,
                onChanged: (value) {
                  setState(() {
                    isAnnual = value ?? false;
                  });
                },
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text('Annuler'),
            ),
            FilledButton(
              onPressed: () {
                if (bonusName.isNotEmpty) {
                  setState(() {
                    _bonuses[bonusName] = (controller, false, isAnnual);
                  });
                  Navigator.of(context).pop();
                }
              },
              child: Text('Ajouter'),
            ),
          ],
        );
      },
    );
  }

  void _onRemoveBonus(String bonusName) {
    setState(() {
      final bonus = _bonuses[bonusName];
      if (bonus != null) {
        bonus.$1.dispose();
        _bonuses.remove(bonusName);
      }
    });
    _triggerCalculation();
  }

  void _onAddDependent() {
    final controller = TextEditingController();
    controller.addListener(_triggerCalculation);
    setState(() {
      _dependents.add((controller, false));
    });
  }

  void _onRemoveDependent(int index) {
    if (index < _dependents.length) {
      setState(() {
        _dependents[index].$1.dispose();
        _dependents.removeAt(index);
      });
      _triggerCalculation();
    }
  }

  void _onDependentDisabilityChanged(int index, bool isDisabled) {
    if (index < _dependents.length) {
      setState(() {
        final (controller, _) = _dependents[index];
        _dependents[index] = (controller, isDisabled);
      });
      _triggerCalculation();
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: LayoutBuilder(
        builder: (context, constraints) {
          final isTablet = constraints.maxWidth > 600;
          final isDesktop = constraints.maxWidth > 900;
          
          if (isDesktop) {
            return _buildDesktopLayout();
          } else if (isTablet) {
            return _buildTabletLayout();
          } else {
            return _buildMobileLayout();
          }
        },
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: _buildFormColumn(),
        ),
        const SizedBox(width: 24),
        Expanded(
          flex: 1,
          child: Column(
            children: [
              CalculationModeSelector(
                isAnnualMode: _calculatorController.useAnnualMode,
                onModeChanged: _onModeChanged,
                enabled: !_calculatorController.isCalculatingNotifier.value,
              ),
              const SizedBox(height: 16),
              RealTimePreviewCard(
                resultNotifier: _calculatorController.resultNotifier,
                isCalculatingNotifier: _calculatorController.isCalculatingNotifier,
                errorNotifier: _calculatorController.errorNotifier,
                showDetailedBreakdown: true,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildTabletLayout() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          child: _buildFormColumn(),
        ),
        const SizedBox(width: 16),
        SizedBox(
          width: 300,
          child: Column(
            children: [
              CalculationModeSelector(
                isAnnualMode: _calculatorController.useAnnualMode,
                onModeChanged: _onModeChanged,
                enabled: !_calculatorController.isCalculatingNotifier.value,
              ),
              const SizedBox(height: 16),
              RealTimePreviewCard(
                resultNotifier: _calculatorController.resultNotifier,
                isCalculatingNotifier: _calculatorController.isCalculatingNotifier,
                errorNotifier: _calculatorController.errorNotifier,
                isCompactView: true,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    return Column(
      children: [
        CalculationModeSelector(
          isAnnualMode: _calculatorController.useAnnualMode,
          onModeChanged: _onModeChanged,
          enabled: !_calculatorController.isCalculatingNotifier.value,
        ),
        const SizedBox(height: 16),
        RealTimePreviewCard(
          resultNotifier: _calculatorController.resultNotifier,
          isCalculatingNotifier: _calculatorController.isCalculatingNotifier,
          errorNotifier: _calculatorController.errorNotifier,
          isCompactView: true,
        ),
        const SizedBox(height: 16),
        _buildFormColumn(),
      ],
    );
  }

  Widget _buildFormColumn() {
    return Column(
      children: [
        _buildHeader(),
        const SizedBox(height: 16),
        _buildSectionToggleControls(),
        const SizedBox(height: 16),
        BasicInfoCard(
          baseSalaryController: _baseSalaryController,
          yearsController: _yearsOfServiceController,
          selectedEmployeeType: _employeeType,
          includeCNSS: _includeCNSS,
          onEmployeeTypeChanged: (type) {
            setState(() {
              _employeeType = type;
            });
            _triggerCalculation();
          },
          onCNSSChanged: (value) {
            setState(() {
              _includeCNSS = value;
            });
            _triggerCalculation();
          },
          onFieldChanged: _triggerCalculation,
        ),
        if (_sectionVisibility['allowances'] == true) ...[
          const SizedBox(height: 16),
          AllowancesCard(
            transportController: _transportController,
            housingController: _housingController,
            otherController: _otherAllowancesController,
            onFieldChanged: _triggerCalculation,
            isVisible: true,
          ),
        ],
        if (_sectionVisibility['overtime'] == true) ...[
          const SizedBox(height: 16),
          OvertimeCard(
            regularController: _regularOvertimeController,
            holidayController: _holidayOvertimeController,
            nightController: _nightOvertimeController,
            isExpanded: _isOvertimeExpanded,
            onExpandedChanged: (expanded) {
              setState(() {
                _isOvertimeExpanded = expanded;
              });
            },
            onFieldChanged: _triggerCalculation,
            baseSalary: double.tryParse(_baseSalaryController.text.replaceAll(' ', '')) ?? 0,
          ),
        ],
        if (_sectionVisibility['bonuses'] == true) ...[
          const SizedBox(height: 16),
          BonusesCard(
            bonuses: _bonuses,
            onBonusToggled: _onBonusToggled,
            onFieldChanged: _triggerCalculation,
            isVisible: true,
            onAddBonus: _onAddBonus,
            onRemoveBonus: _onRemoveBonus,
          ),
        ],
        if (_sectionVisibility['family'] == true) ...[
          const SizedBox(height: 16),
          FamilyStatusCard(
            isMarried: _isMarried,
            onMarriedChanged: (value) {
              setState(() {
                _isMarried = value;
              });
              _triggerCalculation();
            },
            dependents: _dependents,
            onAddDependent: _onAddDependent,
            onRemoveDependent: _onRemoveDependent,
            onDependentDisabilityChanged: _onDependentDisabilityChanged,
            onFieldChanged: _triggerCalculation,
          ),
        ],
        if (_sectionVisibility['professionalExpenses'] == true) ...[
          const SizedBox(height: 16),
          ValueListenableBuilder(
            valueListenable: _calculatorController.resultNotifier,
            builder: (context, result, child) {
              return ProfessionalExpensesCard(
                baseAmount: result?.irResult.professionalExpensesBase,
                rate: result?.irResult.professionalExpensesRate,
                calculatedAmount: result?.irResult.professionalExpenses,
                isVisible: true,
              );
            },
          ),
        ],
      ],
    );
  }

  Widget _buildHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor,
            Theme.of(context).primaryColor.withOpacity(0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Calculateur IR 2025',
            style: Theme.of(context).textTheme.headlineMedium?.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Calcul en temps réel avec les nouveaux barèmes 2025',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.white.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionToggleControls() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Sections optionnelles',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Wrap(
              spacing: 8,
              runSpacing: 8,
              children: [
                _buildSectionToggle('allowances', 'Indemnités', Icons.card_giftcard),
                _buildSectionToggle('overtime', 'Heures supp.', Icons.schedule),
                _buildSectionToggle('bonuses', 'Primes', Icons.stars),
                _buildSectionToggle('family', 'Famille', Icons.family_restroom),
                _buildSectionToggle('professionalExpenses', 'Frais prof.', Icons.receipt_long),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionToggle(String section, String label, IconData icon) {
    final isVisible = _sectionVisibility[section] ?? false;
    
    return FilterChip(
      label: Text(label),
      avatar: Icon(icon, size: 16),
      selected: isVisible,
      onSelected: (selected) => _onSectionVisibilityChanged(section, selected),
      selectedColor: Theme.of(context).primaryColor.withOpacity(0.2),
      checkmarkColor: Theme.of(context).primaryColor,
    );
  }
}