// IR Screen - Main screen for Income Tax (IR) information and calculations
import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

// Theme import

// Import sections
import 'sections/presentation_section.dart';
import 'sections/revenus_section.dart';
import 'sections/bareme_section.dart';
import 'sections/deductions_section.dart';
import 'sections/cnss_section.dart';
import 'sections/obligations_section.dart';
import 'sections/exercices_section.dart';
import 'sections/calculator_section.dart';

class IRScreen extends StatefulWidget {
  const IRScreen({super.key});

  @override
  State<IRScreen> createState() => _IRScreenState();
}

class _IRScreenState extends State<IRScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<Map<String, dynamic>> _tabs = [];
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadTabs();
  }

  Future<void> _loadTabs() async {
    try {
      final jsonString = await rootBundle.loadString('assets/ir/tabs.json');
      final json = jsonDecode(jsonString);
      setState(() {
        _tabs = List<Map<String, dynamic>>.from(json['tabs']);
        _tabController = TabController(length: _tabs.length, vsync: this);
        _isLoading = false;
      });
    } catch (e) {
      debugPrint('Error loading tabs: $e');
      setState(() {
        _isLoading = false;
        _errorMessage =
            'Impossible de charger les onglets. Veuillez réessayer.';
      });
    }
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Widget _buildTabContent(Map<String, dynamic> tab) {
    switch (tab['id']) {
      case 'presentation':
        return const PresentationSection();
      case 'revenus':
        return const RevenusSection();
      case 'deductions':
        return const DeductionsSection();
      case 'cnss':
        return const CnssSection();
      case 'bareme':
        return const BaremeSection();
      case 'obligations':
        return const ObligationsSection();
      case 'exercices':
        return const ExercicesSection();
      case 'calculator':
        return const CalculatorSection();
      default:
        return Center(
          child: Text('Section ${tab['id']} non implémentée'),
        );
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (_isLoading) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
              ),
              const SizedBox(height: 16),
              Text(
                'Chargement des données fiscales...',
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (_errorMessage.isNotEmpty) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: colorScheme.errorContainer.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: colorScheme.error.withOpacity(0.2),
                  ),
                ),
                child: Column(
                  children: [
                    Icon(
                      Icons.error_outline,
                      color: colorScheme.error,
                      size: 64,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _errorMessage,
                      style: textTheme.titleMedium?.copyWith(
                        color: colorScheme.error,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              ElevatedButton.icon(
                onPressed: _loadTabs,
                icon: Icon(
                  Icons.refresh,
                  color: colorScheme.onPrimary,
                ),
                label: Text(
                  'Réessayer',
                  style: textTheme.labelLarge?.copyWith(
                    color: colorScheme.onPrimary,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Impôt sur le Revenu',
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(kToolbarHeight),
          child: Container(
            decoration: BoxDecoration(
              color: colorScheme.surface,
              border: Border(
                bottom: BorderSide(
                  color: colorScheme.outlineVariant,
                  width: 1,
                ),
              ),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withOpacity(0.05),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              indicatorColor: colorScheme.primary,
              indicatorWeight: 3,
              labelColor: colorScheme.primary,
              unselectedLabelColor: colorScheme.onSurfaceVariant,
              labelStyle: textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelStyle: textTheme.titleSmall,
              indicatorSize: TabBarIndicatorSize.label,
              padding: const EdgeInsets.symmetric(horizontal: 8),
              tabs: _tabs.map((tab) {
                return Tab(
                  icon: Icon(
                    _getIconData(tab['icon']),
                    color: _getTabIconColor(context, tab['id']),
                  ),
                  text: tab['text'],
                );
              }).toList(),
            ),
          ),
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: _tabs.map((tab) => _buildTabContent(tab)).toList(),
      ),
    );
  }

  Color _getTabIconColor(BuildContext context, String tabId) {
    final colorScheme = Theme.of(context).colorScheme;
    switch (tabId) {
      case 'presentation':
        return Colors.blue;
      case 'revenus':
        return Colors.green;
      case 'deductions':
        return Colors.red;
      case 'cnss':
        return Colors.teal;
      case 'bareme':
        return Colors.orange;
      case 'obligations':
        return Colors.purple;
      case 'exercices':
        return Colors.indigo;
      case 'calculator':
        return colorScheme.primary;
      default:
        return colorScheme.onSurfaceVariant;
    }
  }

  IconData _getIconData(String iconName) {
    switch (iconName) {
      case 'info':
        return Icons.info_outline;
      case 'attach_money':
        return Icons.payments_outlined;
      case 'remove_circle':
        return Icons.remove_circle_outline;
      case 'health_and_safety':
        return Icons.health_and_safety_outlined;
      case 'calculate':
        return Icons.calculate_outlined;
      case 'assignment':
        return Icons.assignment_outlined;
      case 'school':
        return Icons.school_outlined;
      default:
        return Icons.error_outline;
    }
  }
}
