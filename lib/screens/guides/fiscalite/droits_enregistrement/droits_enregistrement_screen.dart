import 'package:flutter/material.dart';
import 'package:moroccanaccounting/screens/guides/fiscalite/droits_enregistrement/sections/nouveautes_section.dart';
import 'package:moroccanaccounting/screens/guides/fiscalite/droits_enregistrement/sections/taux_section.dart';
import 'package:moroccanaccounting/screens/guides/fiscalite/droits_enregistrement/sections/sanctions_section.dart';
import 'package:moroccanaccounting/screens/guides/fiscalite/droits_enregistrement/sections/obligations_section.dart';
import 'package:moroccanaccounting/screens/guides/fiscalite/droits_enregistrement/sections/exemples_section.dart';
import 'package:moroccanaccounting/widgets/custom_tab_view.dart';

class DroitsEnregistrementScreen extends StatelessWidget {
  const DroitsEnregistrementScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return const CustomTabView(
      jsonPath: 'assets/droits_enregistrement/tabs.json',
      sections: [
        NouveautesSection(),
        TauxSection(),
        SanctionsSection(),
        ObligationsSection(),
        ExemplesSection(),
      ],
    );
  }
}
