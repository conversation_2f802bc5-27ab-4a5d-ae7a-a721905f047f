import 'package:flutter/material.dart';
import '../../../../../models/is/is_exercices_avances.dart' as models;
import '../../../../../services/is_exercices_avances_service.dart';
import '../../../../../widgets/loading_indicator.dart';
import '../../../../../widgets/error_display.dart';
import '../../../../../widgets/journal_comptable_widget.dart';

class ExercicesAvancesIsSection extends StatelessWidget {
  const ExercicesAvancesIsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<models.IsExercicesAvances>(
      future: IsExercicesAvancesService().getExercicesAvances(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingIndicator();
        }

        if (snapshot.hasError) {
          return ErrorDisplay(
            error: snapshot.error.toString(),
            onRetry: () {
              IsExercicesAvancesService().clearCache();
              (context as Element).markNeedsBuild();
            },
          );
        }

        final data = snapshot.data!;
        final colorScheme = Theme.of(context).colorScheme;
        final textTheme = Theme.of(context).textTheme;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.primary,
                      colorScheme.primaryContainer,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.shadow.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      data.title,
                      style: textTheme.headlineMedium?.copyWith(
                        color: colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Cas complexes et opérations internationales',
                      style: textTheme.titleMedium?.copyWith(
                        color: colorScheme.onPrimary.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              ...data.sections.map((section) => Column(
                    children: [
                      _buildExercice(
                        context,
                        title: section.title,
                        content: section.content,
                        solution: section.solution,
                        entries: section.entries,
                      ),
                      const SizedBox(height: 24),
                    ],
                  )),
            ],
          ),
        );
      },
    );
  }

  Widget _buildExercice(
    BuildContext context, {
    required String title,
    required String content,
    required String solution,
    required List<models.JournalEntry> entries,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isSolutionVisible = ValueNotifier(false);

    Widget buildContentSection(String text) {
      final lines = text.split('\n');
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: lines.map((line) {
          if (line.trim().isEmpty) return const SizedBox(height: 8);

          Widget contentWidget;
          if (line.startsWith('•') || line.startsWith('-')) {
            contentWidget = Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 8),
                  width: 6,
                  height: 6,
                  decoration: BoxDecoration(
                    color: colorScheme.primary,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    line.substring(1).trim(),
                    style: textTheme.bodyLarge?.copyWith(
                      height: 1.5,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            );
          } else if (RegExp(r'^\d+\.').hasMatch(line)) {
            contentWidget = Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 8,
              ),
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '${line.split('.').first}.',
                    style: textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      line.substring(line.indexOf('.') + 1).trim(),
                      style: textTheme.bodyLarge?.copyWith(
                        height: 1.5,
                        color: colorScheme.onSurface,
                      ),
                    ),
                  ),
                ],
              ),
            );
          } else {
            contentWidget = Container(
              margin: const EdgeInsets.only(bottom: 8),
              child: Text(
                line,
                style: textTheme.bodyLarge?.copyWith(
                  height: 1.5,
                  color: colorScheme.onSurface,
                ),
              ),
            );
          }

          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: contentWidget,
          );
        }).toList(),
      );
    }

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.school,
                  color: colorScheme.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    title,
                    style: textTheme.titleLarge?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const Divider(height: 24),

            // Exercise Content
            buildContentSection(content),
            const SizedBox(height: 24),

            // Solution Section
            ValueListenableBuilder(
              valueListenable: isSolutionVisible,
              builder: (context, visible, _) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: ElevatedButton.icon(
                        onPressed: () => isSolutionVisible.value = !visible,
                        icon: Icon(
                          visible ? Icons.visibility_off : Icons.visibility,
                          color: colorScheme.onPrimary,
                        ),
                        label: Text(
                          visible
                              ? 'Masquer la solution'
                              : 'Afficher la solution',
                          style: TextStyle(color: colorScheme.onPrimary),
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colorScheme.primary,
                          padding: const EdgeInsets.symmetric(
                            horizontal: 24,
                            vertical: 12,
                          ),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    if (visible) ...[
                      const SizedBox(height: 24),
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: colorScheme.primaryContainer.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: colorScheme.primary.withOpacity(0.2),
                          ),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Icon(
                                  Icons.lightbulb_outline,
                                  color: colorScheme.primary,
                                  size: 20,
                                ),
                                const SizedBox(width: 8),
                                Text(
                                  'Solution détaillée',
                                  style: textTheme.titleMedium?.copyWith(
                                    color: colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            buildContentSection(solution),
                            const SizedBox(height: 16),
                            ...entries.map((entry) => Padding(
                                  padding: const EdgeInsets.only(bottom: 16),
                                  child: JournalComptableWidget(
                                    title: 'Écritures comptables',
                                    entries: [
                                      JournalEntry(
                                        date: entry.date,
                                        lines: entry.lines
                                            .map((line) => JournalLine(
                                                  account: line.account,
                                                  label: line.label,
                                                  debit: line.debit,
                                                  credit: line.credit,
                                                ))
                                            .toList(),
                                      ),
                                    ],
                                  ),
                                )),
                          ],
                        ),
                      ),
                    ],
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }
}
