import 'package:flutter/material.dart';
import '../../../../../models/is/is_data.dart';
import '../../../../../services/is_service.dart';
import '../../../../../widgets/loading_indicator.dart';
import '../../../../../widgets/error_display.dart';

class BaseImposableSection extends StatelessWidget {
  const BaseImposableSection({super.key});

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<IsData>(
      future: IsService().getBaseImposable(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingIndicator();
        }

        if (snapshot.hasError) {
          return ErrorDisplay(
            error: snapshot.error.toString(),
            onRetry: () {
              IsService().clearCache();
              (context as Element).markNeedsBuild();
            },
          );
        }

        final data = snapshot.data!;
        final colorScheme = Theme.of(context).colorScheme;
        final textTheme = Theme.of(context).textTheme;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.primary,
                      colorScheme.primaryContainer,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.shadow.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      data.title,
                      style: textTheme.headlineMedium?.copyWith(
                        color: colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'Calcul et détermination',
                      style: textTheme.titleMedium?.copyWith(
                        color: colorScheme.onPrimary.withOpacity(0.8),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),
              if (data.sections != null) ...[
                ...data.sections!.map((section) {
                  List<Widget> contentWidgets = [];
                  final lines = section.formattedContent.split('\n');

                  for (var line in lines) {
                    if (line.trim().isEmpty) continue;

                    if (line.startsWith('-')) {
                      contentWidgets.add(
                        Container(
                          margin: const EdgeInsets.only(left: 16, bottom: 12),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Container(
                                margin: const EdgeInsets.only(top: 8),
                                width: 6,
                                height: 6,
                                decoration: BoxDecoration(
                                  color: colorScheme.primary,
                                  shape: BoxShape.circle,
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  line.substring(1).trim(),
                                  style: textTheme.bodyLarge?.copyWith(
                                    height: 1.5,
                                    color: colorScheme.onSurface,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    } else if (line.startsWith('Note:') ||
                        line.startsWith('Attention:')) {
                      contentWidgets.add(
                        Container(
                          margin: const EdgeInsets.symmetric(vertical: 8),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: line.startsWith('Attention:')
                                ? colorScheme.errorContainer.withOpacity(0.2)
                                : colorScheme.tertiaryContainer
                                    .withOpacity(0.1),
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: line.startsWith('Attention:')
                                  ? colorScheme.error.withOpacity(0.2)
                                  : colorScheme.tertiary.withOpacity(0.2),
                            ),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(
                                line.startsWith('Attention:')
                                    ? Icons.warning_amber_rounded
                                    : Icons.info_outline,
                                color: line.startsWith('Attention:')
                                    ? colorScheme.error
                                    : colorScheme.tertiary,
                                size: 20,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  line.split(':')[1].trim(),
                                  style: textTheme.bodyMedium?.copyWith(
                                    fontStyle: FontStyle.italic,
                                    height: 1.5,
                                    color: line.startsWith('Attention:')
                                        ? colorScheme.error
                                        : colorScheme.onSurface,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                      );
                    } else {
                      contentWidgets.add(
                        Container(
                          margin: const EdgeInsets.only(bottom: 16),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          decoration: BoxDecoration(
                            color: line.endsWith(':')
                                ? colorScheme.primaryContainer.withOpacity(0.1)
                                : null,
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            line,
                            style: textTheme.bodyLarge?.copyWith(
                              height: 1.5,
                              color: colorScheme.onSurface,
                              fontWeight:
                                  line.endsWith(':') ? FontWeight.bold : null,
                            ),
                          ),
                        ),
                      );
                    }
                  }

                  return Card(
                    margin: const EdgeInsets.only(bottom: 16),
                    elevation: 2,
                    surfaceTintColor: colorScheme.surfaceTint,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(20.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                _getSectionIcon(section.title),
                                color: colorScheme.primary,
                                size: 28,
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: Text(
                                  section.title,
                                  style: textTheme.titleLarge?.copyWith(
                                    color: colorScheme.primary,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const Divider(height: 24),
                          ...contentWidgets,
                        ],
                      ),
                    ),
                  );
                }),
              ],
            ],
          ),
        );
      },
    );
  }

  IconData _getSectionIcon(String title) {
    switch (title) {
      case 'Détermination du Résultat Fiscal':
        return Icons.calculate;
      case 'Produits Imposables':
        return Icons.add_circle_outline;
      case 'Charges Déductibles':
        return Icons.remove_circle_outline;
      case 'Déficits Reportables':
        return Icons.history;
      default:
        return Icons.article;
    }
  }
}
