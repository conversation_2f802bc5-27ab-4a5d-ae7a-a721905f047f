import 'package:flutter/material.dart';
import 'sections/exercices_is_section.dart';
import 'sections/exercices_avances_is_section.dart';
import 'sections/taux_is_section.dart';
import 'sections/reintegrations_deductions_section.dart';
import 'sections/presentation_section.dart';
import 'sections/base_imposable_section.dart';
import 'sections/obligations_section.dart';
import 'sections/calculator_section.dart';

class IsScreen extends StatefulWidget {
  const IsScreen({super.key});

  @override
  State<IsScreen> createState() => _IsScreenState();
}

class _IsScreenState extends State<IsScreen>
    with SingleTickerProviderStateMixin {
  int _selectedIndex = 0;
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 8, vsync: this);
    _tabController.addListener(() {
      setState(() {
        _selectedIndex = _tabController.index;
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isWideScreen = MediaQuery.of(context).size.width >= 600;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Impôt sur les Sociétés (IS)',
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        bottom: !isWideScreen
            ? TabBar(
                controller: _tabController,
                isScrollable: true,
                tabAlignment: TabAlignment.start,
                dividerColor: colorScheme.primary.withOpacity(0.2),
                labelColor: colorScheme.primary,
                unselectedLabelColor: colorScheme.onSurfaceVariant,
                indicatorColor: colorScheme.primary,
                indicatorWeight: 3,
                labelStyle: textTheme.bodySmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                unselectedLabelStyle: textTheme.bodySmall,
                tabs: const [
                  Tab(
                    icon: Icon(Icons.info_outline),
                    text: 'Intro',
                  ),
                  Tab(
                    icon: Icon(Icons.calculate_outlined),
                    text: 'Calcul',
                  ),
                  Tab(
                    icon: Icon(Icons.calculate),
                    text: 'Base',
                  ),
                  Tab(
                    icon: Icon(Icons.add_circle_outline),
                    text: 'Réint.',
                  ),
                  Tab(
                    icon: Icon(Icons.percent),
                    text: 'Taux',
                  ),
                  Tab(
                    icon: Icon(Icons.assignment),
                    text: 'Oblig.',
                  ),
                  Tab(
                    icon: Icon(Icons.school),
                    text: 'Ex.',
                  ),
                  Tab(
                    icon: Icon(Icons.psychology),
                    text: 'Ex. Av.',
                  ),
                ],
              )
            : null,
      ),
      body: Row(
        children: [
          if (isWideScreen)
            NavigationRail(
              selectedIndex: _selectedIndex,
              onDestinationSelected: (index) {
                setState(() {
                  _selectedIndex = index;
                  _tabController.animateTo(index);
                });
              },
              labelType: NavigationRailLabelType.all,
              backgroundColor: colorScheme.surface,
              selectedIconTheme: IconThemeData(
                color: colorScheme.primary,
              ),
              unselectedIconTheme: IconThemeData(
                color: colorScheme.onSurfaceVariant,
              ),
              selectedLabelTextStyle: textTheme.bodyMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelTextStyle: textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
              useIndicator: true,
              indicatorColor: colorScheme.primaryContainer,
              destinations: [
                NavigationRailDestination(
                  icon: const Icon(Icons.info_outline),
                  label: Text('Présentation'),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.calculate_outlined),
                  label: Text('Calculateur'),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.calculate),
                  label: Text('Base Imposable'),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.add_circle_outline),
                  label: Text('Réintégrations'),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.percent),
                  label: Text('Taux'),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.assignment),
                  label: Text('Obligations'),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.school),
                  label: Text('Exercices'),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
                NavigationRailDestination(
                  icon: const Icon(Icons.psychology),
                  label: Text('Exercices Avancés'),
                  padding: const EdgeInsets.symmetric(vertical: 8),
                ),
              ],
            ),
          Expanded(
            child: Container(
              color: colorScheme.surface,
              child: !isWideScreen
                  ? TabBarView(
                      controller: _tabController,
                      children: [
                        const PresentationSection(),
                        const IsCalculatorSection(),
                        const BaseImposableSection(),
                        const ReintegrationsDeductionsSection(),
                        const TauxIsSection(),
                        const ObligationsSection(),
                        const ExercicesIsSection(),
                        const ExercicesAvancesIsSection(),
                      ],
                    )
                  : _buildSelectedSection(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSelectedSection() {
    switch (_selectedIndex) {
      case 0:
        return const PresentationSection();
      case 1:
        return const IsCalculatorSection();
      case 2:
        return const BaseImposableSection();
      case 3:
        return const ReintegrationsDeductionsSection();
      case 4:
        return const TauxIsSection();
      case 5:
        return const ObligationsSection();
      case 6:
        return const ExercicesIsSection();
      case 7:
        return const ExercicesAvancesIsSection();
      default:
        return Center(
          child: Text(
            'Section non trouvée',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.error,
                ),
          ),
        );
    }
  }
}
