import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../../../widgets/journal_comptable_widget.dart';
import '../../../../../../widgets/loading_indicator.dart';
import '../../../../../../widgets/error_display.dart';

class SectionBase extends StatefulWidget {
  final String jsonPath;

  const SectionBase({
    super.key,
    required this.jsonPath,
  });

  @override
  State<SectionBase> createState() => _SectionBaseState();
}

class _SectionBaseState extends State<SectionBase> {
  late Future<dynamic> _dataFuture;

  @override
  void initState() {
    super.initState();
    _dataFuture = _loadData();
  }

  Future<dynamic> _loadData() async {
    try {
      final String jsonString = await rootBundle.loadString(widget.jsonPath);
      return json.decode(jsonString);
    } catch (e) {
      throw Exception('Failed to load data: $e');
    }
  }

  List<JournalEntry> _convertToJournalEntries(dynamic journalData) {
    if (journalData is List) {
      return journalData
          .map((entry) => _createJournalEntry(entry as Map<String, dynamic>))
          .toList();
    } else if (journalData is Map<String, dynamic>) {
      if (journalData.containsKey('entries')) {
        final entries = journalData['entries'];
        if (entries is List) {
          return entries
              .map(
                  (entry) => _createJournalEntry(entry as Map<String, dynamic>))
              .toList();
        }
      }
      return [_createJournalEntry(journalData)];
    }
    return [];
  }

  JournalEntry _createJournalEntry(Map<String, dynamic> journalMap) {
    final lines = journalMap['lines'];
    if (lines == null) {
      return JournalEntry(lines: []);
    }

    return JournalEntry(
      date: journalMap['date'] as String?,
      lines: (lines as List).map((lineMap) {
        final map = lineMap as Map<String, dynamic>;
        return JournalLine(
          account: map['account']?.toString() ?? '',
          label: map['label']?.toString() ?? '',
          debit: map['debit']?.toString(),
          credit: map['credit']?.toString(),
        );
      }).toList(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return FutureBuilder<dynamic>(
      future: _dataFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const LoadingIndicator();
        }

        if (snapshot.hasError) {
          return ErrorDisplay(
            error: snapshot.error.toString(),
            onRetry: () {
              setState(() {
                _dataFuture = _loadData();
              });
            },
          );
        }

        if (!snapshot.hasData) {
          return ErrorDisplay(
            error: 'No data available',
            onRetry: () {
              setState(() {
                _dataFuture = _loadData();
              });
            },
          );
        }

        final data = snapshot.data!;
        final entries = _convertToJournalEntries(data);
        final colorScheme = Theme.of(context).colorScheme;
        final textTheme = Theme.of(context).textTheme;

        return SingleChildScrollView(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Container(
                width: double.infinity,
                padding: const EdgeInsets.all(20),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.primary,
                      colorScheme.primaryContainer,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: colorScheme.shadow.withOpacity(0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      data['title'] ?? '',
                      style: textTheme.headlineMedium?.copyWith(
                        color: colorScheme.onPrimary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (data['subtitle'] != null) ...[
                      const SizedBox(height: 8),
                      Text(
                        data['subtitle'],
                        style: textTheme.titleMedium?.copyWith(
                          color: colorScheme.onPrimary.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // Description
              if (data['description'] != null)
                Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(16),
                    child: Text(
                      data['description'],
                      style: textTheme.bodyLarge?.copyWith(
                        color: colorScheme.onSurface.withOpacity(0.8),
                        height: 1.5,
                      ),
                    ),
                  ),
                ),

              // Elements Section
              if (data['elements'] != null && data['elements'] is List) ...[
                Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.list_alt,
                              color: colorScheme.primary,
                              size: 28,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Éléments à considérer',
                              style: textTheme.titleLarge?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const Divider(height: 24),
                        Wrap(
                          spacing: 16,
                          runSpacing: 16,
                          children:
                              (data['elements'] as List).map<Widget>((element) {
                            return Container(
                              width: 300,
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: colorScheme.primaryContainer
                                    .withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: colorScheme.primary.withOpacity(0.2),
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    element['title'] ?? '',
                                    style: textTheme.titleMedium?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: colorScheme.primary,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    element['description'] ?? '',
                                    style: textTheme.bodyMedium?.copyWith(
                                      color: colorScheme.onSurface
                                          .withOpacity(0.8),
                                      height: 1.5,
                                    ),
                                  ),
                                ],
                              ),
                            );
                          }).toList(),
                        ),
                      ],
                    ),
                  ),
                ),
              ],

              // Notes Section
              if (data['notes'] != null && data['notes'] is List) ...[
                Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.info_outline,
                              color: colorScheme.primary,
                              size: 28,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Notes importantes',
                              style: textTheme.titleLarge?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const Divider(height: 24),
                        ...(data['notes'] as List).map<Widget>((note) {
                          return Padding(
                            padding: const EdgeInsets.only(bottom: 16),
                            child: Row(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Icon(
                                  Icons.check_circle_outline,
                                  color: colorScheme.primary,
                                  size: 20,
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: Text(
                                    note.toString(),
                                    style: textTheme.bodyMedium?.copyWith(
                                      color: colorScheme.onSurface
                                          .withOpacity(0.8),
                                      height: 1.5,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ],

              // Examples Section
              if (data['examples'] != null && data['examples'] is List) ...[
                Card(
                  margin: const EdgeInsets.only(bottom: 16),
                  child: Padding(
                    padding: const EdgeInsets.all(20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              Icons.lightbulb_outline,
                              color: colorScheme.primary,
                              size: 28,
                            ),
                            const SizedBox(width: 12),
                            Text(
                              'Exemples pratiques',
                              style: textTheme.titleLarge?.copyWith(
                                color: colorScheme.primary,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                        const Divider(height: 24),
                        ...(data['examples'] as List).map<Widget>((example) {
                          return Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                example['title'] ?? 'Exemple',
                                style: textTheme.titleMedium?.copyWith(
                                  color: colorScheme.primary,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              if (example['description'] != null) ...[
                                const SizedBox(height: 8),
                                Text(
                                  example['description'].toString(),
                                  style: textTheme.bodyMedium?.copyWith(
                                    color:
                                        colorScheme.onSurface.withOpacity(0.8),
                                    height: 1.5,
                                  ),
                                ),
                              ],
                              if (example['data'] != null &&
                                  example['data'] is Map) ...[
                                const SizedBox(height: 16),
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: colorScheme.surfaceContainerHighest
                                        .withOpacity(0.3),
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color:
                                          colorScheme.outline.withOpacity(0.2),
                                    ),
                                  ),
                                  child: Column(
                                    children: [
                                      ...(example['data'] as Map)
                                          .entries
                                          .where(
                                              (entry) => entry.key != 'total')
                                          .map(
                                            (entry) => Padding(
                                              padding: const EdgeInsets.only(
                                                  bottom: 8),
                                              child: Row(
                                                mainAxisAlignment:
                                                    MainAxisAlignment
                                                        .spaceBetween,
                                                children: [
                                                  Text(
                                                    entry.key.toString(),
                                                    style: textTheme.bodyMedium
                                                        ?.copyWith(
                                                      color:
                                                          colorScheme.onSurface,
                                                    ),
                                                  ),
                                                  Text(
                                                    '${entry.value} DH',
                                                    style: textTheme.bodyMedium
                                                        ?.copyWith(
                                                      color:
                                                          colorScheme.onSurface,
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ),
                                      if ((example['data'] as Map)['total'] !=
                                          null) ...[
                                        const Divider(),
                                        Padding(
                                          padding:
                                              const EdgeInsets.only(top: 8),
                                          child: Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            children: [
                                              Text(
                                                'Total',
                                                style: textTheme.titleSmall
                                                    ?.copyWith(
                                                  color: colorScheme.primary,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                              Text(
                                                '${(example['data'] as Map)['total']} DH',
                                                style: textTheme.titleSmall
                                                    ?.copyWith(
                                                  color: colorScheme.primary,
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    ],
                                  ),
                                ),
                              ],
                              if (example['steps'] != null &&
                                  example['steps'] is List) ...[
                                const SizedBox(height: 16),
                                ...(example['steps'] as List).map<Widget>(
                                  (step) => Padding(
                                    padding: const EdgeInsets.only(bottom: 8),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Icon(
                                          Icons.arrow_right,
                                          color: colorScheme.primary,
                                          size: 20,
                                        ),
                                        const SizedBox(width: 8),
                                        Expanded(
                                          child: Text(
                                            step.toString(),
                                            style:
                                                textTheme.bodyMedium?.copyWith(
                                              color: colorScheme.onSurface
                                                  .withOpacity(0.8),
                                              height: 1.5,
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                              if (example['journal'] != null)
                                JournalComptableWidget(
                                  title: 'Écriture comptable',
                                  entries: _convertToJournalEntries(
                                      example['journal']),
                                ),
                              const SizedBox(height: 24),
                            ],
                          );
                        }),
                      ],
                    ),
                  ),
                ),
              ],
            ],
          ),
        );
      },
    );
  }
}
