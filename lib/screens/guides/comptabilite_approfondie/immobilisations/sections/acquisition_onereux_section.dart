import 'package:flutter/material.dart';
import '../widgets/section_base.dart';

class AcquisitionOnereuxSection extends StatelessWidget {
  const AcquisitionOnereuxSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).colorScheme.surfaceContainerHighest.withOpacity(0.5),
              Theme.of(context).colorScheme.surface,
            ],
          ),
        ),
        child: const SectionBase(
          jsonPath: 'assets/immobilisations/acquisition_onereux.json',
        ),
      ),
    );
  }
}
