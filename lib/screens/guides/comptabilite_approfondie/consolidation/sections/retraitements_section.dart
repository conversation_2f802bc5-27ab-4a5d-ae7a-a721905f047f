import 'package:flutter/material.dart';
import '../../../../../../widgets/journal_comptable_widget.dart';

class RetraitementsSection extends StatelessWidget {
  const RetraitementsSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Retraitements de Consolidation',
            style: textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 24),
          _buildHomogeneisation(context),
          const SizedBox(height: 24),
          _buildEliminations(context),
          const SizedBox(height: 24),
          _buildExempleRetraitement(context),
        ],
      ),
    );
  }

  Widget _buildHomogeneisation(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.sync_alt, color: colorScheme.primary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Homogénéisation des Méthodes',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'L\'homogénéisation consiste à appliquer des méthodes comptables uniformes pour des transactions et événements similaires dans des circonstances semblables.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.primary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colorScheme.primary.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Principaux retraitements:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(
                      context, 'Méthodes d\'évaluation des stocks'),
                  _buildBulletPoint(context, 'Méthodes d\'amortissement'),
                  _buildBulletPoint(context,
                      'Traitement des contrats de location-financement'),
                  _buildBulletPoint(
                      context, 'Conversion des états financiers en devises'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEliminations(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.secondary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.remove_circle_outline,
                      color: colorScheme.secondary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Éliminations des Opérations Intra-groupe',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Les opérations réalisées entre les sociétés du groupe doivent être éliminées pour ne pas affecter le résultat consolidé.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.secondary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.secondary.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Opérations à éliminer:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.secondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(context, 'Comptes réciproques'),
                  _buildBulletPoint(
                      context, 'Ventes internes et marges internes'),
                  _buildBulletPoint(context, 'Dividendes intra-groupe'),
                  _buildBulletPoint(
                      context, 'Provisions sur éléments intra-groupe'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExempleRetraitement(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.tertiary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.edit_note, color: colorScheme.tertiary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Exemple de Retraitement',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Cas: Vente intra-groupe',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'La société mère vend des marchandises à sa filiale pour 100 000 DH avec une marge de 20%. La filiale n\'a pas encore revendu ces marchandises à la clôture.',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.tertiary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.tertiary.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Écritures de retraitement:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.tertiary,
                    ),
                  ),
                  const SizedBox(height: 16),
                  JournalComptableWidget(
                    title: 'Élimination de la marge interne',
                    entries: [
                      JournalEntry(
                        date: '31/12/2024',
                        lines: [
                          JournalLine(
                            account: '31',
                            label: 'Stocks de marchandises',
                            credit: '20000',
                          ),
                          JournalLine(
                            account: '7111',
                            label: 'Ventes de marchandises',
                            debit: '100000',
                          ),
                          JournalLine(
                            account: '6111',
                            label: 'Achats de marchandises',
                            credit: '100000',
                          ),
                          JournalLine(
                            account: '1180',
                            label: 'Résultat consolidé',
                            debit: '20000',
                          ),
                        ],
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Explications:\n'
                    '1. Élimination des ventes et achats réciproques\n'
                    '2. Annulation de la marge interne dans les stocks\n'
                    '3. Impact sur le résultat consolidé',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBulletPoint(BuildContext context, String text) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(left: 8, bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              )),
          Expanded(
            child: Text(text,
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface,
                )),
          ),
        ],
      ),
    );
  }
}
