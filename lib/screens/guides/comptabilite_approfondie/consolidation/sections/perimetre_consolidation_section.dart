import 'package:flutter/material.dart';

class PerimetreConsolidationSection extends StatelessWidget {
  const PerimetreConsolidationSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Périmètre de Consolidation',
            style: textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 24),
          _buildDefinition(context),
          const SizedBox(height: 24),
          _buildCriteresInclusion(context),
          const SizedBox(height: 24),
          _buildCriteresExclusion(context),
          const SizedBox(height: 24),
          _buildVariationsPerimetre(context),
        ],
      ),
    );
  }

  Widget _buildDefinition(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.account_tree, color: colorScheme.primary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Définition et Importance',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Le périmètre de consolidation détermine l\'ensemble des entreprises dont les comptes sont retenus pour l\'établissement des comptes consolidés du groupe.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.primary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colorScheme.primary.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Éléments clés:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(
                      context, 'Identification des entités à consolider'),
                  _buildBulletPoint(
                      context, 'Détermination des liens de contrôle'),
                  _buildBulletPoint(
                      context, 'Choix des méthodes de consolidation'),
                  _buildBulletPoint(
                      context, 'Impact sur la présentation des comptes'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCriteresInclusion(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.secondary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.add_circle_outline,
                      color: colorScheme.secondary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Critères d\'Inclusion',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Les entreprises sont incluses dans le périmètre de consolidation en fonction de la nature et de l\'importance du contrôle exercé.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.secondary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.secondary.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Types de contrôle:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.secondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(
                      context, 'Contrôle exclusif (> 50% des droits de vote)'),
                  _buildBulletPoint(
                      context, 'Contrôle conjoint (accord contractuel)'),
                  _buildBulletPoint(
                      context, 'Influence notable (20-50% des droits de vote)'),
                  _buildBulletPoint(context,
                      'Contrôle de fait (pouvoir de direction effectif)'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCriteresExclusion(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.remove_circle_outline,
                      color: colorScheme.error),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Critères d\'Exclusion',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Certaines entreprises peuvent être exclues du périmètre de consolidation dans des cas spécifiques.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.error.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colorScheme.error.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Motifs d\'exclusion:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.error,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(
                      context, 'Restrictions durables et fortes du contrôle'),
                  _buildBulletPoint(context,
                      'Intérêt négligeable par rapport à l\'ensemble consolidé'),
                  _buildBulletPoint(
                      context, 'Impossibilité d\'obtenir des informations'),
                  _buildBulletPoint(context,
                      'Actions détenues en vue d\'une cession ultérieure'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildVariationsPerimetre(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.tertiary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.change_circle_outlined,
                      color: colorScheme.tertiary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Variations du Périmètre',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'Les variations du périmètre de consolidation peuvent avoir un impact significatif sur les comptes consolidés.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.tertiary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.tertiary.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Types de variations:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.tertiary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(context,
                      'Entrées dans le périmètre (acquisitions, créations)'),
                  _buildBulletPoint(
                      context, 'Sorties du périmètre (cessions, liquidations)'),
                  _buildBulletPoint(
                      context, 'Modifications du pourcentage d\'intérêt'),
                  _buildBulletPoint(
                      context, 'Changements de méthode de consolidation'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBulletPoint(BuildContext context, String text) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(left: 8, bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              )),
          Expanded(
            child: Text(text,
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface,
                )),
          ),
        ],
      ),
    );
  }
}
