import 'package:flutter/material.dart';

class MethodesConsolidationSection extends StatelessWidget {
  const MethodesConsolidationSection({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Méthodes de Consolidation',
            style: textTheme.headlineMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 24),
          _buildIntegrationGlobale(context),
          const SizedBox(height: 24),
          _buildMiseEnEquivalence(context),
          const SizedBox(height: 24),
          _buildIntegrationProportionnelle(context),
          const SizedBox(height: 24),
          _buildComparaisonMethodes(context),
        ],
      ),
    );
  }

  Widget _buildIntegrationGlobale(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.merge_type, color: colorScheme.primary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Intégration Globale',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'L\'intégration globale est utilisée lorsque la société mère exerce un contrôle exclusif sur la filiale. Elle consiste à intégrer dans les comptes de l\'entreprise consolidante les éléments des comptes de la filiale consolidée, après retraitements éventuels.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.primary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: colorScheme.primary.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Caractéristiques:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.primary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(context,
                      'Intégration à 100% des éléments du bilan et du compte de résultat'),
                  _buildBulletPoint(
                      context, 'Élimination des opérations intra-groupe'),
                  _buildBulletPoint(context,
                      'Calcul et présentation des intérêts minoritaires'),
                  _buildBulletPoint(context,
                      'Traitement des écarts de première consolidation'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMiseEnEquivalence(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.secondary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.link, color: colorScheme.secondary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Mise en Équivalence',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'La mise en équivalence est utilisée lorsque la société consolidante exerce une influence notable sur la société consolidée. Cette méthode consiste à substituer à la valeur comptable des titres détenus la quote-part des capitaux propres.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.secondary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.secondary.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Principes:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.secondary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(context,
                      'Remplacement de la valeur des titres par la quote-part des capitaux propres'),
                  _buildBulletPoint(context,
                      'Constatation de la quote-part dans le résultat'),
                  _buildBulletPoint(
                      context, 'Pas d\'intégration des comptes individuels'),
                  _buildBulletPoint(
                      context, 'Traitement des écarts d\'acquisition'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIntegrationProportionnelle(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.tertiary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.merge, color: colorScheme.tertiary),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Intégration Proportionnelle',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Text(
              'L\'intégration proportionnelle est utilisée pour les entreprises sous contrôle conjoint. Les éléments sont intégrés dans les comptes de l\'entreprise consolidante proportionnellement à sa participation.',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.tertiary.withOpacity(0.05),
                borderRadius: BorderRadius.circular(8),
                border:
                    Border.all(color: colorScheme.tertiary.withOpacity(0.2)),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Spécificités:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.tertiary,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildBulletPoint(context,
                      'Intégration proportionnelle au pourcentage d\'intérêt'),
                  _buildBulletPoint(
                      context, 'Pas de calcul d\'intérêts minoritaires'),
                  _buildBulletPoint(
                      context, 'Élimination des opérations réciproques'),
                  _buildBulletPoint(context,
                      'Traitement des écarts de première consolidation'),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildComparaisonMethodes(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: colorScheme.error.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(Icons.compare_arrows, color: colorScheme.error),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    'Comparaison des Méthodes',
                    style: textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Critères de choix:',
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Text(
                    '1. Intégration Globale:\n'
                    '   • Contrôle exclusif (> 50% des droits de vote)\n'
                    '   • Pouvoir de direction des politiques financières\n\n'
                    '2. Mise en Équivalence:\n'
                    '   • Influence notable (20-50% des droits de vote)\n'
                    '   • Participation aux décisions stratégiques\n\n'
                    '3. Intégration Proportionnelle:\n'
                    '   • Contrôle conjoint\n'
                    '   • Accord contractuel de partage du contrôle',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildBulletPoint(BuildContext context, String text) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.only(left: 8, bottom: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('• ',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              )),
          Expanded(
            child: Text(text,
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface,
                )),
          ),
        ],
      ),
    );
  }
}
