import 'package:flutter/material.dart';
import '../../../../../../widgets/journal_comptable_widget.dart';

class MethodesSection extends StatefulWidget {
  const MethodesSection({super.key});

  @override
  State<MethodesSection> createState() => _MethodesSectionState();
}

class _MethodesSectionState extends State<MethodesSection> {
  final Map<String, bool> _expandedSections = {};

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMethodeCard(
            'Intégration Globale (IG)',
            'Applicable en cas de contrôle exclusif (>50%)',
            [
              _buildExample(
                'Exemple: Société M détient 80% de F',
                '''Données:
• Capital F: 1 000 000 DH
• Réserves F: 200 000 DH
• Résultat F: 150 000 DH
• Coût d'acquisition des titres: 900 000 DH''',
                [
                  JournalEntry(
                    date: '2024-01-01',
                    lines: [
                      JournalLine(
                        account: '261',
                        label: 'Titres de participation',
                        debit: '900000',
                      ),
                      JournalLine(
                        account: '5141',
                        label: 'Banque',
                        credit: '900000',
                      ),
                    ],
                  ),
                  JournalEntry(
                    date: '2024-12-31',
                    lines: [
                      JournalLine(
                        account: '101',
                        label: 'Capital F',
                        debit: '1000000',
                      ),
                      JournalLine(
                        account: '106',
                        label: 'Réserves F',
                        debit: '200000',
                      ),
                      JournalLine(
                        account: '120',
                        label: 'Résultat F',
                        debit: '150000',
                      ),
                      JournalLine(
                        account: '261',
                        label: 'Titres de participation',
                        credit: '900000',
                      ),
                      JournalLine(
                        account: '1081',
                        label: 'Intérêts minoritaires',
                        credit: '270000',
                      ),
                      JournalLine(
                        account: '1061',
                        label: 'Écart de première consolidation',
                        credit: '180000',
                      ),
                    ],
                  ),
                ],
              ),
            ],
            Colors.blue,
          ),
          const SizedBox(height: 16),
          _buildMethodeCard(
            'Intégration Proportionnelle (IP)',
            'Applicable en cas de contrôle conjoint (50%)',
            [
              _buildExample(
                'Exemple: Société M détient 50% de F',
                '''Données:
• Capital F: 800 000 DH
• Réserves F: 100 000 DH
• Résultat F: 80 000 DH
• Coût d'acquisition des titres: 450 000 DH''',
                [
                  JournalEntry(
                    date: '2024-01-01',
                    lines: [
                      JournalLine(
                        account: '261',
                        label: 'Titres de participation',
                        debit: '450000',
                      ),
                      JournalLine(
                        account: '5141',
                        label: 'Banque',
                        credit: '450000',
                      ),
                    ],
                  ),
                  JournalEntry(
                    date: '2024-12-31',
                    lines: [
                      JournalLine(
                        account: '101',
                        label: 'Capital F (50%)',
                        debit: '400000',
                      ),
                      JournalLine(
                        account: '106',
                        label: 'Réserves F (50%)',
                        debit: '50000',
                      ),
                      JournalLine(
                        account: '120',
                        label: 'Résultat F (50%)',
                        debit: '40000',
                      ),
                      JournalLine(
                        account: '261',
                        label: 'Titres de participation',
                        credit: '450000',
                      ),
                      JournalLine(
                        account: '1061',
                        label: 'Écart de première consolidation',
                        credit: '40000',
                      ),
                    ],
                  ),
                ],
              ),
            ],
            Colors.green,
          ),
          const SizedBox(height: 16),
          _buildMethodeCard(
            'Mise en Équivalence (ME)',
            'Applicable en cas d\'influence notable (20-50%)',
            [
              _buildExample(
                'Exemple: Société M détient 30% de F',
                '''Données:
• Capitaux propres F: 1 500 000 DH
• Résultat F: 120 000 DH
• Coût d'acquisition des titres: 400 000 DH''',
                [
                  JournalEntry(
                    date: '2024-01-01',
                    lines: [
                      JournalLine(
                        account: '261',
                        label: 'Titres de participation',
                        debit: '400000',
                      ),
                      JournalLine(
                        account: '5141',
                        label: 'Banque',
                        credit: '400000',
                      ),
                    ],
                  ),
                  JournalEntry(
                    date: '2024-12-31',
                    lines: [
                      JournalLine(
                        account: '265',
                        label: 'Titres mis en équivalence',
                        debit: '450000',
                      ),
                      JournalLine(
                        account: '261',
                        label: 'Titres de participation',
                        credit: '400000',
                      ),
                      JournalLine(
                        account: '1061',
                        label: 'Réserves consolidées',
                        credit: '50000',
                      ),
                      JournalLine(
                        account: '7755',
                        label: 'Quote-part résultat MEE',
                        credit: '36000',
                      ),
                    ],
                  ),
                ],
              ),
            ],
            Colors.purple,
          ),
        ],
      ),
    );
  }

  Widget _buildMethodeCard(
      String title, String description, List<Widget> examples, Color color) {
    return Card(
      elevation: 4,
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.account_tree, color: color),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: color,
                      ),
                    ),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: examples,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExample(
      String title, String description, List<JournalEntry> entries) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.grey[100],
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            description,
            style: const TextStyle(fontSize: 14),
          ),
        ),
        const SizedBox(height: 16),
        const Text(
          'Écritures de consolidation:',
          style: TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        JournalComptableWidget(
          title: 'Journal des Opérations',
          entries: entries,
        ),
      ],
    );
  }
}
