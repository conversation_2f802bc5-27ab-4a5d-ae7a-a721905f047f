import 'package:flutter/material.dart';
import 'sections/perimetre_consolidation_section.dart';
import 'sections/methodes_consolidation_section.dart';
import 'sections/ecarts_consolidation_section.dart';
import 'sections/retraitements_section.dart';
import 'sections/exercices_section.dart';
import 'sections/exercices_avances_section.dart';

class ConsolidationScreen extends StatefulWidget {
  const ConsolidationScreen({super.key});

  @override
  State<ConsolidationScreen> createState() => _ConsolidationScreenState();
}

class _ConsolidationScreenState extends State<ConsolidationScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  final List<({IconData icon, String text, String tooltip})> _tabs = const [
    (
      icon: Icons.account_tree,
      text: 'Périmètre',
      tooltip: 'Périmètre de consolidation'
    ),
    (
      icon: Icons.merge_type,
      text: 'Méthodes',
      tooltip: 'Méthodes de consolidation'
    ),
    (icon: Icons.calculate, text: 'Écarts', tooltip: 'Écarts de consolidation'),
    (
      icon: Icons.sync_alt,
      text: 'Retraitements',
      tooltip: 'Retraitements de consolidation'
    ),
    (icon: Icons.school, text: 'Exercices', tooltip: 'Exercices de base'),
    (
      icon: Icons.psychology,
      text: 'Exercices Avancés',
      tooltip: 'Exercices avancés'
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Guide de Consolidation',
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: colorScheme.onSurface),
          onPressed: () => Navigator.pop(context),
        ),
        bottom: PreferredSize(
          preferredSize: const Size.fromHeight(kToolbarHeight),
          child: Container(
            decoration: BoxDecoration(
              border: Border(
                bottom: BorderSide(
                  color: colorScheme.outline.withOpacity(0.2),
                ),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              isScrollable: true,
              tabAlignment: TabAlignment.start,
              dividerColor: Colors.transparent,
              indicatorColor: colorScheme.primary,
              indicatorWeight: 3,
              labelColor: colorScheme.primary,
              unselectedLabelColor: colorScheme.onSurfaceVariant,
              labelStyle: textTheme.labelLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              unselectedLabelStyle: textTheme.labelLarge,
              indicator: UnderlineTabIndicator(
                borderSide: BorderSide(
                  width: 3,
                  color: colorScheme.primary,
                ),
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(3),
                ),
              ),
              tabs: _tabs.map((tab) {
                return Tab(
                  height: 64,
                  child: Tooltip(
                    message: tab.tooltip,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(tab.icon),
                        const SizedBox(height: 4),
                        Text(
                          tab.text,
                          style: TextStyle(
                            color: _tabController.index == _tabs.indexOf(tab)
                                ? colorScheme.primary
                                : colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
      ),
      body: Container(
        color: colorScheme.surface,
        child: TabBarView(
          controller: _tabController,
          children: const [
            PerimetreConsolidationSection(),
            MethodesConsolidationSection(),
            EcartsConsolidationSection(),
            RetraitementsSection(),
            ExercicesSection(),
            ExercicesAvancesSection(),
          ],
        ),
      ),
    );
  }
}
