import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class CalculatorSection extends StatefulWidget {
  const CalculatorSection({super.key});

  @override
  State<CalculatorSection> createState() => _CalculatorSectionState();
}

class _CalculatorSectionState extends State<CalculatorSection> {
  Map<String, dynamic>? _calculatorData;
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, double?> _results = {};

  @override
  void initState() {
    super.initState();
    _loadCalculatorData();
  }

  @override
  void dispose() {
    for (var controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  Future<void> _loadCalculatorData() async {
    try {
      final String response =
          await rootBundle.loadString('assets/provisions/calculator.json');
      final data = await json.decode(response);
      setState(() {
        _calculatorData = data;
        _initializeControllers();
      });
    } catch (e) {
      debugPrint('Error loading calculator data: $e');
    }
  }

  void _initializeControllers() {
    if (_calculatorData == null) return;

    for (var calculator in _calculatorData!['calculators']) {
      for (var field in calculator['fields']) {
        final key = '${calculator['title']}_${field['name']}';
        _controllers[key] = TextEditingController();
      }
    }
  }

  void _calculate(Map<String, dynamic> calculator) {
    final fields = calculator['fields'] as List;
    final Map<String, double> values = {};

    // Collect values
    for (var field in fields) {
      final key = '${calculator['title']}_${field['name']}';
      final value = double.tryParse(_controllers[key]?.text ?? '');
      if (value == null) {
        setState(() {
          _results[calculator['title']] = null;
        });
        return;
      }
      values[field['name']] = value;
    }

    // Calculate result based on formula
    double result;
    switch (calculator['formula']) {
      case 'valeur_comptable - valeur_actuelle':
        result = values['valeur_comptable']! - values['valeur_actuelle']!;
        break;
      case 'nouvelle_depreciation - provision_existante':
        result =
            values['nouvelle_depreciation']! - values['provision_existante']!;
        break;
      default:
        result = 0;
    }

    setState(() {
      _results[calculator['title']] = result;
    });
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (_calculatorData == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
            ),
            const SizedBox(height: 16),
            Text(
              'Chargement des calculateurs...',
              style: textTheme.bodyLarge?.copyWith(
                color: colorScheme.onSurface,
              ),
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Calculateurs de Provisions',
            style: textTheme.headlineMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          ..._buildCalculators(),
        ],
      ),
    );
  }

  List<Widget> _buildCalculators() {
    if (_calculatorData == null) return [];

    final calculators = _calculatorData!['calculators'] as List;
    return calculators.map<Widget>((calculator) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCalculatorCard(calculator),
          const SizedBox(height: 24),
        ],
      );
    }).toList();
  }

  Widget _buildCalculatorCard(Map<String, dynamic> calculator) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(12),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  calculator['title'],
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (calculator['description'] != null) ...[
                  const SizedBox(height: 8),
                  Text(
                    calculator['description'],
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onPrimaryContainer.withOpacity(0.8),
                    ),
                  ),
                ],
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ...(calculator['fields'] as List).map<Widget>((field) {
                  final key = '${calculator['title']}_${field['name']}';
                  return Padding(
                    padding: const EdgeInsets.only(bottom: 16),
                    child: TextField(
                      controller: _controllers[key],
                      style: TextStyle(color: colorScheme.onSurface),
                      decoration: InputDecoration(
                        labelText: field['label'],
                        labelStyle: TextStyle(color: colorScheme.primary),
                        hintText: '0.00',
                        hintStyle: TextStyle(
                          color: colorScheme.onSurfaceVariant.withOpacity(0.5),
                        ),
                        suffixText: 'DH',
                        suffixStyle: TextStyle(color: colorScheme.primary),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(color: colorScheme.outline),
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: colorScheme.outline.withOpacity(0.5),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: colorScheme.primary,
                            width: 2,
                          ),
                        ),
                        filled: true,
                        fillColor: colorScheme.surface,
                      ),
                      keyboardType:
                          const TextInputType.numberWithOptions(decimal: true),
                      onChanged: (_) => _calculate(calculator),
                    ),
                  );
                }),
                const SizedBox(height: 16),
                if (_results[calculator['title']] != null)
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colorScheme.outline.withOpacity(0.2),
                      ),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Résultat:',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          '${_results[calculator['title']]!.toStringAsFixed(2)} DH',
                          style: textTheme.titleMedium?.copyWith(
                            color: colorScheme.primary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
