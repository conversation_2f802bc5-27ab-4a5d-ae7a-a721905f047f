import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_highlight/flutter_highlight.dart';
import 'package:flutter_highlight/themes/monokai-sublime.dart';

class ExercicesSection extends StatefulWidget {
  const ExercicesSection({super.key});

  @override
  State<ExercicesSection> createState() => _ExercicesSectionState();
}

class _ExercicesSectionState extends State<ExercicesSection> {
  List<dynamic>? _originalExercices;
  List<dynamic>? exercices;
  bool isLoading = true;
  String? error;
  final TextEditingController _searchController = TextEditingController();
  String _selectedDifficulty = 'Tous';

  @override
  void initState() {
    super.initState();
    _loadExercicesData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  Future<void> _loadExercicesData() async {
    try {
      final String response =
          await rootBundle.loadString('assets/provisions/exercices.json');
      final data = json.decode(response);
      if (data != null && data['exercices'] != null) {
        setState(() {
          _originalExercices = data['exercices'] as List<dynamic>;
          exercices = _originalExercices;
          isLoading = false;
        });
      } else {
        setState(() {
          error = 'Format de données invalide';
          isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        error = e.toString();
        isLoading = false;
      });
    }
  }

  void _filterExercices() {
    final query = _searchController.text.toLowerCase();
    setState(() {
      exercices = _originalExercices?.where((exercice) {
        final matchesSearch = query.isEmpty ||
            (exercice['title']?.toString().toLowerCase().contains(query) ??
                false) ||
            (exercice['description']
                    ?.toString()
                    .toLowerCase()
                    .contains(query) ??
                false);

        final matchesDifficulty = _selectedDifficulty == 'Tous' ||
            (exercice['difficulty']?.toString() == _selectedDifficulty);

        return matchesSearch && matchesDifficulty;
      }).toList();
    });
  }

  Color _getDifficultyColor(String? difficulty, ColorScheme colorScheme) {
    switch (difficulty) {
      case 'Facile':
        return colorScheme.primaryContainer.withOpacity(0.3);
      case 'Moyen':
        return colorScheme.secondaryContainer.withOpacity(0.3);
      case 'Difficile':
        return colorScheme.tertiaryContainer.withOpacity(0.3);
      default:
        return colorScheme.surfaceContainerHighest.withOpacity(0.3);
    }
  }

  IconData _getDifficultyIcon(String? difficulty) {
    switch (difficulty) {
      case 'Facile':
        return Icons.sentiment_satisfied;
      case 'Moyen':
        return Icons.sentiment_neutral;
      case 'Difficile':
        return Icons.sentiment_very_dissatisfied;
      default:
        return Icons.help_outline;
    }
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (isLoading) {
      return Center(
        child: CircularProgressIndicator(
          color: colorScheme.primary,
        ),
      );
    }

    if (error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 48,
              color: colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'Erreur: $error',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.error,
              ),
            ),
            const SizedBox(height: 16),
            ElevatedButton.icon(
              onPressed: _loadExercicesData,
              icon: const Icon(Icons.refresh),
              label: const Text('Réessayer'),
              style: ElevatedButton.styleFrom(
                foregroundColor: colorScheme.onPrimary,
                backgroundColor: colorScheme.primary,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(16.0),
          child: Row(
            children: [
              Expanded(
                flex: 3,
                child: TextField(
                  controller: _searchController,
                  style: TextStyle(color: colorScheme.onSurface),
                  decoration: InputDecoration(
                    hintText: 'Rechercher un exercice...',
                    hintStyle: TextStyle(color: colorScheme.onSurfaceVariant),
                    prefixIcon: Icon(Icons.search, color: colorScheme.primary),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: colorScheme.outline),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: colorScheme.outline.withOpacity(0.5),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: colorScheme.primary,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: colorScheme.surface,
                  ),
                  onChanged: (_) => _filterExercices(),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(color: colorScheme.outline),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: colorScheme.outline.withOpacity(0.5),
                      ),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide(
                        color: colorScheme.primary,
                        width: 2,
                      ),
                    ),
                    filled: true,
                    fillColor: colorScheme.surface,
                  ),
                  value: _selectedDifficulty,
                  style: TextStyle(color: colorScheme.onSurface),
                  dropdownColor: colorScheme.surface,
                  items: ['Tous', 'Facile', 'Moyen', 'Difficile']
                      .map<DropdownMenuItem<String>>((difficulty) => DropdownMenuItem(
                            value: difficulty,
                            child: Text(difficulty),
                          ))
                      .toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedDifficulty = value!;
                      _filterExercices();
                    });
                  },
                ),
              ),
            ],
          ),
        ),
        Expanded(
          child: exercices == null || exercices!.isEmpty
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.search_off,
                        size: 48,
                        color: colorScheme.onSurfaceVariant,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Aucun exercice disponible',
                        style: textTheme.titleMedium?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                )
              : _buildExercices(),
        ),
      ],
    );
  }

  Widget _buildExercices() {
    return ListView.builder(
      padding: const EdgeInsets.all(16.0),
      itemCount: exercices?.length ?? 0,
      itemBuilder: (context, index) {
        final exercice = exercices?[index];
        if (exercice == null) return const SizedBox.shrink();
        return _buildExerciceCard(exercice as Map<String, dynamic>);
      },
    );
  }

  Widget _buildExerciceCard(Map<String, dynamic> exercice) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final difficultyColor = _getDifficultyColor(
      exercice['difficulty']?.toString(),
      colorScheme,
    );

    return Card(
      margin: const EdgeInsets.only(bottom: 16.0),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: colorScheme.outline.withOpacity(0.2),
        ),
      ),
      color: difficultyColor,
      child: ExpansionTile(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        collapsedShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
        title: Text(
          exercice['title']?.toString() ?? 'Sans titre',
          style: textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        subtitle: Row(
          children: [
            Text(
              exercice['difficulty']?.toString() ?? 'Niveau non spécifié',
              style: textTheme.bodyMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(width: 8),
            Icon(
              _getDifficultyIcon(exercice['difficulty']?.toString()),
              size: 16,
              color: colorScheme.primary,
            ),
          ],
        ),
        children: [
          Container(
            padding: const EdgeInsets.all(16.0),
            decoration: BoxDecoration(
              color: colorScheme.surface,
              borderRadius: const BorderRadius.vertical(
                bottom: Radius.circular(12),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Description:',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  exercice['description']?.toString() ?? '',
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'Question:',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  exercice['question']?.toString() ?? '',
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 16),
                if (exercice['solution'] != null)
                  _buildSolutionSection(
                    exercice['solution'] as Map<String, dynamic>,
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSolutionSection(Map<String, dynamic> solution) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return ExpansionTile(
      title: Text(
        'Solution',
        style: textTheme.titleMedium?.copyWith(
          color: colorScheme.primary,
          fontWeight: FontWeight.bold,
        ),
      ),
      children: [
        Container(
          padding: const EdgeInsets.all(16.0),
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: colorScheme.outline.withOpacity(0.2),
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (solution['calcul'] != null) ...[
                Text(
                  'Calcul:',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Container(
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: HighlightView(
                    solution['calcul'].toString(),
                    language: 'python',
                    theme: monokaiSublimeTheme,
                    padding: const EdgeInsets.all(12),
                  ),
                ),
                const SizedBox(height: 16),
              ],
              if (solution['journaux'] != null)
                _buildJournaux(solution['journaux'] as List<dynamic>),
              if (solution['tableau_suivi'] != null)
                _buildTableau(
                    'Tableau de suivi', solution['tableau_suivi'].toString()),
              if (solution['tableau_synthese'] != null)
                _buildTableau('Tableau de synthèse',
                    solution['tableau_synthese'].toString()),
              if (solution['analyse'] != null) ...[
                const SizedBox(height: 16),
                Text(
                  'Analyse:',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  solution['analyse'].toString(),
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
                const SizedBox(height: 16),
                ElevatedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(
                        content: const Text('Analyse détaillée à venir'),
                        behavior: SnackBarBehavior.floating,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(10),
                        ),
                        backgroundColor: colorScheme.primary,
                      ),
                    );
                  },
                  icon: const Icon(Icons.analytics_outlined),
                  label: const Text('Analyse détaillée'),
                  style: ElevatedButton.styleFrom(
                    foregroundColor: colorScheme.onPrimary,
                    backgroundColor: colorScheme.primary,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 12,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildJournaux(List<dynamic> journaux) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (journaux.isEmpty) return const SizedBox.shrink();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: journaux.map((journal) {
        if (journal == null) return const SizedBox.shrink();

        final Map<String, dynamic> j = journal as Map<String, dynamic>;
        if (j['ecritures'] == null) return const SizedBox.shrink();

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Journal ${j['nom'] ?? ''} (${j['code'] ?? ''}):',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...(j['ecritures'] as List<dynamic>).map<Widget>((ecriture) {
              if (ecriture == null) return const SizedBox.shrink();

              final Map<String, dynamic> e = ecriture as Map<String, dynamic>;
              if (e['lines'] == null) return const SizedBox.shrink();

              return Container(
                margin: const EdgeInsets.only(bottom: 8.0),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: colorScheme.outline.withOpacity(0.2),
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      '${e['date'] ?? ''} - ${e['description'] ?? ''}',
                      style: textTheme.titleSmall?.copyWith(
                        color: colorScheme.onSurface,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 12),
                    SingleChildScrollView(
                      scrollDirection: Axis.horizontal,
                      child: Table(
                        defaultVerticalAlignment:
                            TableCellVerticalAlignment.middle,
                        columnWidths: const {
                          0: FixedColumnWidth(100),
                          1: FixedColumnWidth(200),
                          2: FixedColumnWidth(100),
                          3: FixedColumnWidth(100),
                        },
                        children: [
                          TableRow(
                            decoration: BoxDecoration(
                              color:
                                  colorScheme.primaryContainer.withOpacity(0.3),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            children: [
                              _buildTableHeader('Compte', colorScheme),
                              _buildTableHeader('Libellé', colorScheme),
                              _buildTableHeader('Débit', colorScheme),
                              _buildTableHeader('Crédit', colorScheme),
                            ],
                          ),
                          ...(e['lines'] as List<dynamic>)
                              .map<TableRow>((line) {
                            if (line == null) {
                              return const TableRow(children: [
                                SizedBox(height: 40),
                                SizedBox(height: 40),
                                SizedBox(height: 40),
                                SizedBox(height: 40),
                              ]);
                            }
                            final Map<String, dynamic> l =
                                line as Map<String, dynamic>;
                            return TableRow(
                              children: [
                                _buildTableCell(l['account']?.toString() ?? '',
                                    colorScheme),
                                _buildTableCell(
                                    l['label']?.toString() ?? '', colorScheme),
                                _buildTableCell(
                                    l['debit']?.toString() ?? '', colorScheme),
                                _buildTableCell(
                                    l['credit']?.toString() ?? '', colorScheme),
                              ],
                            );
                          }),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildTableHeader(String text, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Text(
        text,
        style: TextStyle(
          fontWeight: FontWeight.bold,
          color: colorScheme.primary,
        ),
      ),
    );
  }

  Widget _buildTableCell(String text, ColorScheme colorScheme) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
      child: Text(
        text,
        style: TextStyle(
          color: colorScheme.onSurface,
        ),
      ),
    );
  }

  Widget _buildTableau(String title, String data) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const SizedBox(height: 16),
        Text(
          title,
          style: textTheme.titleMedium?.copyWith(
            color: colorScheme.primary,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: colorScheme.surface,
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: colorScheme.outline.withOpacity(0.2),
            ),
          ),
          padding: const EdgeInsets.all(12),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Text(
              data,
              style: TextStyle(
                fontFamily: 'monospace',
                color: colorScheme.onSurface,
              ),
            ),
          ),
        ),
      ],
    );
  }
}
