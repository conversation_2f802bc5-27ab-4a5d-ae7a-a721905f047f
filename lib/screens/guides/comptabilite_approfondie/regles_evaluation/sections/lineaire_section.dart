import 'package:flutter/material.dart';
import '../widgets/common_widgets.dart';

class LineaireSection extends StatefulWidget {
  const LineaireSection({super.key});

  @override
  State<LineaireSection> createState() => _LineaireSectionState();
}

class _LineaireSectionState extends State<LineaireSection> {
  final _valeurOrigineController = TextEditingController();
  final _tauxAmortissementController = TextEditingController();
  final _premiereAnnuiteController = TextEditingController();

  bool? _valeurOrigineCorrect;
  bool? _tauxAmortissementCorrect;
  bool? _premiereAnnuiteCorrect;

  @override
  void dispose() {
    _valeurOrigineController.dispose();
    _tauxAmortissementController.dispose();
    _premiereAnnuiteController.dispose();
    super.dispose();
  }

  void _verifierReponses() {
    setState(() {
      _valeurOrigineCorrect = _valeurOrigineController.text.trim() == '350000';
      _tauxAmortissementCorrect =
          _tauxAmortissementController.text.trim() == '20';
      _premiereAnnuiteCorrect =
          _premiereAnnuiteController.text.trim() == '52500';
    });

    showDialog(
      context: context,
      builder: (context) {
        final colorScheme = Theme.of(context).colorScheme;
        final textTheme = Theme.of(context).textTheme;

        return AlertDialog(
          title: Row(
            children: [
              Icon(
                _toutesLesReponsesCorrectes
                    ? Icons.check_circle
                    : Icons.warning,
                color: _toutesLesReponsesCorrectes
                    ? colorScheme.primary
                    : colorScheme.error,
              ),
              const SizedBox(width: 8),
              Text(
                _toutesLesReponsesCorrectes
                    ? 'Félicitations !'
                    : 'Continuez vos efforts',
                style: textTheme.titleLarge?.copyWith(
                  color: _toutesLesReponsesCorrectes
                      ? colorScheme.primary
                      : colorScheme.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _toutesLesReponsesCorrectes
                    ? 'Toutes vos réponses sont correctes !'
                    : 'Certaines réponses sont incorrectes. Voici les explications :',
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),
              if (!_toutesLesReponsesCorrectes) ...[
                if (_valeurOrigineCorrect == false)
                  _buildExplication(
                    'Valeur d\'origine',
                    '320 000 + 30 000 = 350 000 DH',
                  ),
                if (_tauxAmortissementCorrect == false)
                  _buildExplication(
                    'Taux d\'amortissement',
                    '100% / 5 ans = 20%',
                  ),
                if (_premiereAnnuiteCorrect == false)
                  _buildExplication(
                    'Première annuité',
                    '350 000 × 20% × 9/12 = 52 500 DH',
                  ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Fermer',
                style: textTheme.labelLarge?.copyWith(
                  color: colorScheme.primary,
                ),
              ),
            ),
          ],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          backgroundColor: colorScheme.surface,
          surfaceTintColor: colorScheme.surfaceTint,
        );
      },
    );
  }

  bool get _toutesLesReponsesCorrectes =>
      _valeurOrigineCorrect == true &&
      _tauxAmortissementCorrect == true &&
      _premiereAnnuiteCorrect == true;

  Widget _buildExplication(String titre, String detail) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            titre,
            style: textTheme.titleMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            detail,
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Amortissement Linéaire',
                  style: textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Comprendre le calcul de l\'amortissement linéaire',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onPrimary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          DefinitionCard(
            title: 'Caractéristiques de l\'amortissement linéaire :',
            points: [
              'Annuités constantes',
              'Taux linéaire = 100% / Durée',
              'Base de calcul = Valeur d\'origine',
              'Prorata temporis la première année',
            ],
          ),
          const SizedBox(height: 24),
          _buildExemple(),
          const SizedBox(height: 24),
          _buildExercice(),
        ],
      ),
    );
  }

  Widget _buildExemple() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final rows = [
      AmortissementRow(
        annee: 'N',
        base: '400 000',
        annuite: '20 000',
        cumul: '20 000',
        vnc: '380 000',
      ),
      AmortissementRow(
        annee: 'N+1',
        base: '400 000',
        annuite: '80 000',
        cumul: '100 000',
        vnc: '300 000',
      ),
      AmortissementRow(
        annee: 'N+2',
        base: '400 000',
        annuite: '80 000',
        cumul: '180 000',
        vnc: '220 000',
      ),
      AmortissementRow(
        annee: 'N+3',
        base: '400 000',
        annuite: '80 000',
        cumul: '260 000',
        vnc: '140 000',
      ),
      AmortissementRow(
        annee: 'N+4',
        base: '400 000',
        annuite: '80 000',
        cumul: '340 000',
        vnc: '60 000',
      ),
      AmortissementRow(
        annee: 'N+5',
        base: '400 000',
        annuite: '60 000',
        cumul: '400 000',
        vnc: '0',
      ),
    ];

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Exemple pratique',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Données :',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Une entreprise acquiert un équipement le 01/10/N :\n'
                    '• Valeur d\'origine : 400 000 DH\n'
                    '• Durée d\'utilisation : 5 ans\n'
                    '• Mode d\'amortissement : Linéaire',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            AmortissementTable(rows: rows),
          ],
        ),
      ),
    );
  }

  Widget _buildExercice() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Exercice pratique',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Énoncé :',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Une entreprise acquiert une machine le 01/04/N :\n'
                    '• Prix d\'achat : 320 000 DH\n'
                    '• Frais d\'installation : 30 000 DH\n'
                    '• Durée d\'utilisation : 5 ans\n'
                    '• Mode d\'amortissement : Linéaire',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Questions :',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            QuestionInput(
              label: '1. Calculez la valeur d\'origine :',
              controller: _valeurOrigineController,
              isCorrect: _valeurOrigineCorrect,
            ),
            QuestionInput(
              label: '2. Calculez le taux d\'amortissement :',
              controller: _tauxAmortissementController,
              isCorrect: _tauxAmortissementCorrect,
              suffix: '%',
            ),
            QuestionInput(
              label: '3. Calculez la première annuité (prorata temporis) :',
              controller: _premiereAnnuiteController,
              isCorrect: _premiereAnnuiteCorrect,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _verifierReponses,
                icon: const Icon(Icons.check_circle),
                label: Text(
                  'Vérifier mes réponses',
                  style: textTheme.labelLarge?.copyWith(
                    color: colorScheme.onPrimary,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
