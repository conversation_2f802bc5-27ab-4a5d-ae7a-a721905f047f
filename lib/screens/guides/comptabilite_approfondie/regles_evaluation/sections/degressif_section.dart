import 'package:flutter/material.dart';
import '../widgets/common_widgets.dart';

class DegressifSection extends StatefulWidget {
  const DegressifSection({super.key});

  @override
  State<DegressifSection> createState() => _DegressifSectionState();
}

class _DegressifSectionState extends State<DegressifSection> {
  final _valeurOrigineController = TextEditingController();
  final _tauxLineaireController = TextEditingController();
  final _tauxDegressifController = TextEditingController();
  final _premiereAnnuiteController = TextEditingController();

  bool? _valeurOrigineCorrect;
  bool? _tauxLineaireCorrect;
  bool? _tauxDegressifCorrect;
  bool? _premiereAnnuiteCorrect;

  @override
  void dispose() {
    _valeurOrigineController.dispose();
    _tauxLineaireController.dispose();
    _tauxDegressifController.dispose();
    _premiereAnnuiteController.dispose();
    super.dispose();
  }

  void _verifierReponses() {
    setState(() {
      _valeurOrigineCorrect = _valeurOrigineController.text.trim() == '450000';
      _tauxLineaireCorrect = _tauxLineaireController.text.trim() == '25';
      _tauxDegressifCorrect = _tauxDegressifController.text.trim() == '37.5';
      _premiereAnnuiteCorrect =
          _premiereAnnuiteController.text.trim() == '126562.5';
    });

    showDialog(
      context: context,
      builder: (context) {
        final colorScheme = Theme.of(context).colorScheme;
        final textTheme = Theme.of(context).textTheme;

        return AlertDialog(
          title: Row(
            children: [
              Icon(
                _toutesLesReponsesCorrectes
                    ? Icons.check_circle
                    : Icons.warning,
                color: _toutesLesReponsesCorrectes
                    ? colorScheme.primary
                    : colorScheme.error,
              ),
              const SizedBox(width: 8),
              Text(
                _toutesLesReponsesCorrectes
                    ? 'Félicitations !'
                    : 'Continuez vos efforts',
                style: textTheme.titleLarge?.copyWith(
                  color: _toutesLesReponsesCorrectes
                      ? colorScheme.primary
                      : colorScheme.error,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                _toutesLesReponsesCorrectes
                    ? 'Toutes vos réponses sont correctes !'
                    : 'Certaines réponses sont incorrectes. Voici les explications :',
                style: textTheme.bodyLarge?.copyWith(
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(height: 16),
              if (!_toutesLesReponsesCorrectes) ...[
                if (_valeurOrigineCorrect == false)
                  _buildExplication(
                    'Valeur d\'origine',
                    '420 000 + 30 000 = 450 000 DH',
                  ),
                if (_tauxLineaireCorrect == false)
                  _buildExplication(
                    'Taux linéaire',
                    '100% / 4 ans = 25%',
                  ),
                if (_tauxDegressifCorrect == false)
                  _buildExplication(
                    'Taux dégressif',
                    '25% × 1,5 = 37,5% (coefficient 1,5 pour 4 ans)',
                  ),
                if (_premiereAnnuiteCorrect == false)
                  _buildExplication(
                    'Première annuité',
                    '450 000 × 37,5% × 9/12 = 126 562,5 DH',
                  ),
              ],
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                'Fermer',
                style: textTheme.labelLarge?.copyWith(
                  color: colorScheme.primary,
                ),
              ),
            ),
          ],
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
          backgroundColor: colorScheme.surface,
          surfaceTintColor: colorScheme.surfaceTint,
        );
      },
    );
  }

  bool get _toutesLesReponsesCorrectes =>
      _valeurOrigineCorrect == true &&
      _tauxLineaireCorrect == true &&
      _tauxDegressifCorrect == true &&
      _premiereAnnuiteCorrect == true;

  Widget _buildExplication(String titre, String detail) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            titre,
            style: textTheme.titleMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            detail,
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: colorScheme.shadow.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Amortissement Dégressif',
                  style: textTheme.headlineMedium?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Comprendre le calcul de l\'amortissement dégressif',
                  style: textTheme.titleMedium?.copyWith(
                    color: colorScheme.onPrimary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 24),
          DefinitionCard(
            title: 'Caractéristiques de l\'amortissement dégressif :',
            points: [
              'Amortissement accéléré les premières années',
              'Taux dégressif = Taux linéaire × Coefficient',
              'Base de calcul = VNC en début d\'exercice',
              'Passage au linéaire quand plus avantageux',
            ],
          ),
          const SizedBox(height: 24),
          _buildCoefficients(),
          const SizedBox(height: 24),
          _buildExemple(),
          const SizedBox(height: 24),
          _buildExercice(),
        ],
      ),
    );
  }

  Widget _buildCoefficients() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Coefficients selon la durée',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Durée',
                        style: textTheme.titleMedium?.copyWith(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        'Coefficient',
                        style: textTheme.titleMedium?.copyWith(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  Divider(color: colorScheme.outlineVariant),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '3-4 ans',
                        style: textTheme.bodyLarge?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        '1,5',
                        style: textTheme.bodyLarge?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '5-6 ans',
                        style: textTheme.bodyLarge?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        '2',
                        style: textTheme.bodyLarge?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '> 6 ans',
                        style: textTheme.bodyLarge?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        '3',
                        style: textTheme.bodyLarge?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildExemple() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final rows = [
      AmortissementRow(
        annee: 'N (9 mois)',
        base: '600 000',
        annuite: '135 000',
        cumul: '135 000',
        vnc: '465 000',
      ),
      AmortissementRow(
        annee: 'N+1',
        base: '465 000',
        annuite: '186 000',
        cumul: '321 000',
        vnc: '279 000',
      ),
      AmortissementRow(
        annee: 'N+2',
        base: '279 000',
        annuite: '111 600',
        cumul: '432 600',
        vnc: '167 400',
      ),
      AmortissementRow(
        annee: 'N+3',
        base: '167 400',
        annuite: '83 700',
        cumul: '516 300',
        vnc: '83 700',
      ),
      AmortissementRow(
        annee: 'N+4 (3 mois)',
        base: '83 700',
        annuite: '83 700',
        cumul: '600 000',
        vnc: '0',
      ),
    ];

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Exemple pratique',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Données :',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Une entreprise acquiert un équipement le 01/04/N :\n'
                    '• Valeur d\'origine : 600 000 DH\n'
                    '• Durée d\'utilisation : 5 ans\n'
                    '• Mode d\'amortissement : Dégressif',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            AmortissementTable(rows: rows),
          ],
        ),
      ),
    );
  }

  Widget _buildExercice() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Exercice pratique',
              style: textTheme.titleLarge?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: colorScheme.outlineVariant,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Énoncé :',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Une entreprise acquiert une machine le 01/04/N :\n'
                    '• Prix d\'achat : 420 000 DH\n'
                    '• Frais de transport : 30 000 DH\n'
                    '• Durée d\'utilisation : 4 ans\n'
                    '• Mode d\'amortissement : Dégressif',
                    style: textTheme.bodyLarge?.copyWith(
                      color: colorScheme.onSurface,
                      height: 1.5,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Questions :',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            QuestionInput(
              label: '1. Calculez la valeur d\'origine :',
              controller: _valeurOrigineController,
              isCorrect: _valeurOrigineCorrect,
            ),
            QuestionInput(
              label: '2. Calculez le taux linéaire :',
              controller: _tauxLineaireController,
              isCorrect: _tauxLineaireCorrect,
              suffix: '%',
            ),
            QuestionInput(
              label: '3. Calculez le taux dégressif :',
              controller: _tauxDegressifController,
              isCorrect: _tauxDegressifCorrect,
              suffix: '%',
            ),
            QuestionInput(
              label: '4. Calculez la première annuité (prorata temporis) :',
              controller: _premiereAnnuiteController,
              isCorrect: _premiereAnnuiteCorrect,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _verifierReponses,
                icon: const Icon(Icons.check_circle),
                label: Text(
                  'Vérifier mes réponses',
                  style: textTheme.labelLarge?.copyWith(
                    color: colorScheme.onPrimary,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
