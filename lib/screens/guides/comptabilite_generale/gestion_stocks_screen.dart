import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../../../services/theme_service.dart';
import '../../../theme/app_theme.dart';
import '../../../widgets/inventory/evaluation_method_card.dart';
import '../../../widgets/inventory/inventory_system_card.dart';
import '../../../widgets/inventory/specific_case_card.dart';

class GestionStocksScreen extends StatefulWidget {
  const GestionStocksScreen({super.key});

  @override
  State<GestionStocksScreen> createState() => _GestionStocksScreenState();
}

class _GestionStocksScreenState extends State<GestionStocksScreen> {
  Map<String, dynamic>? _data;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    try {
      final String response = await rootBundle
          .loadString('assets/compta_generale/gestion_stocks.json');
      setState(() {
        _data = json.decode(response);
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement des données: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Gestion des Stocks',
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: colorScheme.surface,
        surfaceTintColor: Colors.transparent,
        elevation: isDark ? 0 : 2,
      ),
      body: _isLoading
          ? Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(
                    color: colorScheme.primary,
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'Chargement des données...',
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                  ),
                ],
              ),
            )
          : _data == null
              ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.error_outline,
                        size: 48,
                        color: colorScheme.error,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        'Aucune donnée disponible',
                        style: textTheme.titleMedium?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                    ],
                  ),
                )
              : Container(
                  decoration: AppTheme.getGradientDecoration(colorScheme),
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        _buildHeader(context),
                        _buildContent(context),
                      ],
                    ),
                  ),
                ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isDark
              ? [
                  colorScheme.primary.withOpacity(0.8),
                  colorScheme.primaryContainer.withOpacity(0.6),
                ]
              : [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(isDark ? 0.05 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _data!['title'],
            style: textTheme.headlineMedium?.copyWith(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _data!['description'],
            style: textTheme.bodyLarge?.copyWith(
              color: colorScheme.onPrimary.withOpacity(0.9),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(32),
          topRight: Radius.circular(32),
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(isDark ? 0.05 : 0.1),
            blurRadius: 10,
            offset: const Offset(0, -4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (_data!['methodes_evaluation'] != null) ...[
            _buildSectionTitle(
              context,
              'Méthodes d\'Évaluation',
              Icons.calculate_outlined,
            ),
            const SizedBox(height: 16),
            ..._data!['methodes_evaluation'].map<Widget>((methode) => Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: EvaluationMethodCard(
                    title: methode['title'],
                    data: methode,
                    icon: Icons.calculate_outlined,
                  ),
                )),
          ],
          if (_data!['systemes_inventaire'] != null) ...[
            const SizedBox(height: 24),
            _buildSectionTitle(
              context,
              'Systèmes d\'Inventaire',
              Icons.inventory_2_outlined,
            ),
            const SizedBox(height: 16),
            ..._data!['systemes_inventaire'].map<Widget>((systeme) => Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: InventorySystemCard(
                    title: systeme['title'],
                    data: systeme,
                    icon: Icons.inventory_2_outlined,
                  ),
                )),
          ],
          if (_data!['cas_specifiques'] != null) ...[
            const SizedBox(height: 24),
            _buildSectionTitle(
              context,
              'Cas Spécifiques',
              Icons.cases_outlined,
            ),
            const SizedBox(height: 16),
            ..._data!['cas_specifiques'].map<Widget>((cas) => Padding(
                  padding: const EdgeInsets.only(bottom: 16),
                  child: SpecificCaseCard(
                    title: cas['title'],
                    data: cas,
                    icon: Icons.cases_outlined,
                  ),
                )),
          ],
        ],
      ),
    );
  }

  Widget _buildSectionTitle(BuildContext context, String title, IconData icon) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final textTheme = theme.textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withOpacity(isDark ? 0.3 : 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            icon,
            size: 20,
            color: colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Text(
            title,
            style: textTheme.titleMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
