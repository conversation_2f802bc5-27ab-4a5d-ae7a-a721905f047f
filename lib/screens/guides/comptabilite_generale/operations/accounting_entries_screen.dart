import 'package:flutter/material.dart';
import '../../../../widgets/journal_comptable_widget.dart';

class AccountingEntriesScreen extends StatefulWidget {
  const AccountingEntriesScreen({super.key});

  @override
  State<AccountingEntriesScreen> createState() =>
      _AccountingEntriesScreenState();
}

class _AccountingEntriesScreenState extends State<AccountingEntriesScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _tabs = [
    'Achats',
    'Ventes',
    'TVA',
    'Transport',
    'Emballages',
    'Retours'
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        centerTitle: true,
        title: Text(
          'Écritures Comptables',
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.onPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                colorScheme.primary,
                colorScheme.primaryContainer,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          labelStyle: textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
          unselectedLabelStyle: textTheme.titleMedium,
          labelColor: colorScheme.onPrimary,
          unselectedLabelColor: colorScheme.onPrimary.withOpacity(0.7),
          indicatorColor: colorScheme.onPrimary,
          indicatorWeight: 3,
          tabs: _tabs.map((tab) => Tab(text: tab)).toList(),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              colorScheme.surface,
              colorScheme.surfaceContainerHighest.withOpacity(0.5),
            ],
          ),
        ),
        child: TabBarView(
          controller: _tabController,
          children: [
            _buildAchatsSection(context),
            _buildVentesSection(context),
            _buildTVASection(context),
            _buildTransportSection(context),
            _buildEmballagesSection(context),
            _buildRetoursSection(context),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionCard({
    required BuildContext context,
    required String title,
    required String description,
    required IconData icon,
    required List<JournalEntry> entries,
    String? note,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      margin: const EdgeInsets.only(bottom: 16),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  colorScheme.primary,
                  colorScheme.primaryContainer,
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: colorScheme.onPrimary.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    icon,
                    color: colorScheme.onPrimary,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    title,
                    style: textTheme.titleMedium?.copyWith(
                      color: colorScheme.onPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  description,
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface,
                    height: 1.5,
                  ),
                ),
                if (note != null) ...[
                  const SizedBox(height: 16),
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: colorScheme.secondaryContainer.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colorScheme.secondary.withOpacity(0.2),
                      ),
                    ),
                    child: Row(
                      children: [
                        Icon(
                          Icons.info_outline,
                          color: colorScheme.secondary,
                          size: 20,
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Text(
                            note,
                            style: textTheme.bodySmall?.copyWith(
                              color: colorScheme.onSecondaryContainer,
                              height: 1.5,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
                const SizedBox(height: 16),
                JournalComptableWidget(
                  title: '',
                  entries: entries,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAchatsSection(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionCard(
            context: context,
            title: 'Achat de marchandises simple',
            description: 'Achat de marchandises pour 120.000 DH HT, TVA 20%',
            icon: Icons.shopping_cart,
            entries: [
              JournalEntry(
                date: '01/01/2024',
                lines: [
                  JournalLine(
                    account: '6111',
                    label: 'Achats de marchandises',
                    debit: '120.000,00',
                  ),
                  JournalLine(
                    account: '34551',
                    label: 'État, TVA récupérable sur charges',
                    debit: '24.000,00',
                  ),
                  JournalLine(
                    account: '4411',
                    label: 'Fournisseurs',
                    credit: '144.000,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Achat avec remise commerciale',
            description: 'Achat de 100.000 DH HT avec remise 5%, TVA 20%',
            icon: Icons.discount,
            note: 'La remise commerciale est déduite directement du montant HT',
            entries: [
              JournalEntry(
                date: '02/01/2024',
                lines: [
                  JournalLine(
                    account: '6111',
                    label: 'Achats de marchandises',
                    debit: '95.000,00',
                  ),
                  JournalLine(
                    account: '34551',
                    label: 'État, TVA récupérable sur charges',
                    debit: '19.000,00',
                  ),
                  JournalLine(
                    account: '4411',
                    label: 'Fournisseurs',
                    credit: '114.000,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Achat avec remise et escompte',
            description:
                'Achat de 200.000 DH HT, remise 10%, escompte 2%, TVA 20%',
            icon: Icons.discount,
            note: '''
Calcul :
- Prix brut HT : 200.000 DH
- Remise 10% : -20.000 DH
- Net commercial : 180.000 DH
- Escompte 2% : -3.600 DH
- Net financier : 176.400 DH
- TVA 20% : 35.280 DH
- Net à payer : 211.680 DH''',
            entries: [
              JournalEntry(
                date: '03/01/2024',
                lines: [
                  JournalLine(
                    account: '6111',
                    label: 'Achats de marchandises',
                    debit: '180.000,00',
                  ),
                  JournalLine(
                    account: '7386',
                    label: 'Escomptes obtenus',
                    credit: '3.600,00',
                  ),
                  JournalLine(
                    account: '34551',
                    label: 'État, TVA récupérable sur charges',
                    debit: '35.280,00',
                  ),
                  JournalLine(
                    account: '4411',
                    label: 'Fournisseurs',
                    credit: '211.680,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Achat avec frais accessoires',
            description: '''Achat de marchandises :
- Montant brut : 150.000 DH HT
- Transport : 3.000 DH HT
- Assurance : 1.000 DH HT
- Emballages : 2.000 DH HT
TVA 20% sur tous les éléments''',
            icon: Icons.local_shipping,
            note: 'Les frais accessoires sont incorporés au coût d\'achat',
            entries: [
              JournalEntry(
                date: '04/01/2024',
                lines: [
                  JournalLine(
                    account: '6111',
                    label: 'Achats de marchandises',
                    debit: '156.000,00',
                  ),
                  JournalLine(
                    account: '34551',
                    label: 'État, TVA récupérable sur charges',
                    debit: '31.200,00',
                  ),
                  JournalLine(
                    account: '4411',
                    label: 'Fournisseurs',
                    credit: '187.200,00',
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildVentesSection(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionCard(
            context: context,
            title: 'Vente de marchandises simple',
            description: 'Vente de marchandises pour 180.000 DH HT, TVA 20%',
            icon: Icons.point_of_sale,
            entries: [
              JournalEntry(
                date: '03/01/2024',
                lines: [
                  JournalLine(
                    account: '3421',
                    label: 'Clients',
                    debit: '216.000,00',
                  ),
                  JournalLine(
                    account: '7111',
                    label: 'Ventes de marchandises',
                    credit: '180.000,00',
                  ),
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    credit: '36.000,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Vente avec remise et escompte',
            description: '''Vente de marchandises :
- Prix brut HT : 200.000 DH
- Remise commerciale : 10%
- Escompte : 2%
TVA 20%''',
            icon: Icons.discount,
            note: '''
Calcul :
- Prix brut HT : 200.000 DH
- Remise 10% : -20.000 DH
- Net commercial : 180.000 DH
- Escompte 2% : -3.600 DH
- Net financier : 176.400 DH
- TVA 20% : 35.280 DH
- Net à payer : 211.680 DH''',
            entries: [
              JournalEntry(
                date: '04/01/2024',
                lines: [
                  JournalLine(
                    account: '3421',
                    label: 'Clients',
                    debit: '211.680,00',
                  ),
                  JournalLine(
                    account: '7111',
                    label: 'Ventes de marchandises',
                    credit: '180.000,00',
                  ),
                  JournalLine(
                    account: '6386',
                    label: 'Escomptes accordés',
                    debit: '3.600,00',
                  ),
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    credit: '35.280,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Vente avec frais accessoires',
            description: '''Vente de marchandises :
- Montant brut : 250.000 DH HT
- Transport : 5.000 DH HT
- Emballages consignés : 3.000 DH (non soumis à TVA)
TVA 20% sur marchandises et transport''',
            icon: Icons.local_shipping,
            entries: [
              JournalEntry(
                date: '05/01/2024',
                lines: [
                  JournalLine(
                    account: '3421',
                    label: 'Clients',
                    debit: '309.000,00',
                  ),
                  JournalLine(
                    account: '7111',
                    label: 'Ventes de marchandises',
                    credit: '255.000,00',
                  ),
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    credit: '51.000,00',
                  ),
                  JournalLine(
                    account: '4417',
                    label: 'Clients - emballages consignés',
                    credit: '3.000,00',
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTransportSection(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionCard(
            context: context,
            title: 'Transport sur achat non facturé',
            description:
                'Transport sur achat payé directement au transporteur : 2.000 DH HT, TVA 20%',
            icon: Icons.local_shipping,
            note:
                'Le transport est incorporé au coût d\'achat mais payé séparément',
            entries: [
              JournalEntry(
                date: '05/01/2024',
                lines: [
                  JournalLine(
                    account: '6111',
                    label: 'Achats de marchandises',
                    debit: '2.000,00',
                  ),
                  JournalLine(
                    account: '34552',
                    label: 'État, TVA récupérable sur charges',
                    debit: '400,00',
                  ),
                  JournalLine(
                    account: '4411',
                    label: 'Fournisseurs',
                    credit: '2.400,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Transport sur vente facturé',
            description: '''Transport sur vente :
- Transport facturé au client : 3.000 DH HT
- Coût réel du transport : 2.500 DH HT
TVA 20%''',
            icon: Icons.local_shipping,
            note:
                'La différence entre le transport facturé et le coût réel constitue une marge',
            entries: [
              JournalEntry(
                date: '06/01/2024',
                lines: [
                  JournalLine(
                    account: '3421',
                    label: 'Clients',
                    debit: '3.600,00',
                  ),
                  JournalLine(
                    account: '7111',
                    label: 'Ventes de marchandises',
                    credit: '3.000,00',
                  ),
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    credit: '600,00',
                  ),
                ],
              ),
              JournalEntry(
                date: '06/01/2024',
                lines: [
                  JournalLine(
                    account: '6241',
                    label: 'Transport sur ventes',
                    debit: '2.500,00',
                  ),
                  JournalLine(
                    account: '34552',
                    label: 'État, TVA récupérable sur charges',
                    debit: '500,00',
                  ),
                  JournalLine(
                    account: '4411',
                    label: 'Fournisseurs',
                    credit: '3.000,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Transport sur vente pour compte client',
            description: '''Transport avancé pour le compte du client :
- Montant du transport : 4.000 DH HT
TVA 20%''',
            icon: Icons.local_shipping,
            note:
                'Le transport est avancé pour le compte du client et lui est refacturé à l\'identique',
            entries: [
              JournalEntry(
                date: '07/01/2024',
                lines: [
                  JournalLine(
                    account: '3421',
                    label: 'Clients',
                    debit: '4.800,00',
                  ),
                  JournalLine(
                    account: '4411',
                    label: 'Fournisseurs',
                    credit: '4.800,00',
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTVASection(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionCard(
            context: context,
            title: 'Liquidation mensuelle de la TVA (Cas créditeur)',
            description: 'Liquidation de la TVA pour le mois de janvier 2024',
            icon: Icons.calculate,
            note: '''
TVA facturée : 80.000 DH
TVA récupérable sur immobilisations : 15.000 DH
TVA récupérable sur charges : 25.000 DH
Crédit de TVA du mois précédent : 8.000 DH''',
            entries: [
              JournalEntry(
                date: '31/01/2024',
                lines: [
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    debit: '80.000,00',
                  ),
                  JournalLine(
                    account: '34551',
                    label: 'État, TVA récupérable sur immobilisations',
                    credit: '15.000,00',
                  ),
                  JournalLine(
                    account: '34552',
                    label: 'État, TVA récupérable sur charges',
                    credit: '25.000,00',
                  ),
                  JournalLine(
                    account: '3456',
                    label: 'État, Crédit de TVA',
                    credit: '8.000,00',
                  ),
                  JournalLine(
                    account: '4456',
                    label: 'État, TVA due',
                    credit: '32.000,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Liquidation mensuelle de la TVA (Cas débiteur)',
            description: 'Liquidation de la TVA pour le mois de février 2024',
            icon: Icons.calculate,
            note: '''
TVA facturée : 30.000 DH
TVA récupérable sur immobilisations : 25.000 DH
TVA récupérable sur charges : 20.000 DH
Pas de crédit de TVA antérieur''',
            entries: [
              JournalEntry(
                date: '28/02/2024',
                lines: [
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    debit: '30.000,00',
                  ),
                  JournalLine(
                    account: '34551',
                    label: 'État, TVA récupérable sur immobilisations',
                    credit: '25.000,00',
                  ),
                  JournalLine(
                    account: '34552',
                    label: 'État, TVA récupérable sur charges',
                    credit: '20.000,00',
                  ),
                  JournalLine(
                    account: '3456',
                    label: 'État, Crédit de TVA',
                    debit: '15.000,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Régularisation de TVA',
            description: 'Régularisation suite à une facture d\'avoir',
            icon: Icons.published_with_changes,
            note: '''
Avoir reçu pour une remise de 5.000 DH HT
TVA 20%''',
            entries: [
              JournalEntry(
                date: '10/02/2024',
                lines: [
                  JournalLine(
                    account: '4411',
                    label: 'Fournisseurs',
                    debit: '6.000,00',
                  ),
                  JournalLine(
                    account: '6111',
                    label: 'Achats de marchandises',
                    credit: '5.000,00',
                  ),
                  JournalLine(
                    account: '34552',
                    label: 'État, TVA récupérable sur charges',
                    credit: '1.000,00',
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildEmballagesSection(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionCard(
            context: context,
            title: 'Emballages consignés à la vente',
            description: '''Vente avec emballages consignés :
- Marchandises : 50.000 DH HT
- Emballages consignés : 2.000 DH
TVA 20% sur marchandises uniquement''',
            icon: Icons.inventory_2,
            note: 'Les emballages consignés ne sont pas soumis à la TVA',
            entries: [
              JournalEntry(
                date: '07/01/2024',
                lines: [
                  JournalLine(
                    account: '3421',
                    label: 'Clients',
                    debit: '62.000,00',
                  ),
                  JournalLine(
                    account: '7111',
                    label: 'Ventes de marchandises',
                    credit: '50.000,00',
                  ),
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    credit: '10.000,00',
                  ),
                  JournalLine(
                    account: '4417',
                    label: 'Clients - emballages consignés',
                    credit: '2.000,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Retour d\'emballages consignés en bon état',
            description:
                'Retour des emballages consignés par le client en bon état',
            icon: Icons.assignment_return,
            note: 'Remboursement intégral de la consignation',
            entries: [
              JournalEntry(
                date: '15/01/2024',
                lines: [
                  JournalLine(
                    account: '4417',
                    label: 'Clients - emballages consignés',
                    debit: '2.000,00',
                  ),
                  JournalLine(
                    account: '5141',
                    label: 'Banques',
                    credit: '2.000,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Retour d\'emballages consignés détériorés',
            description: '''Retour d'emballages détériorés :
- Valeur de consignation : 3.000 DH
- Retenue pour détérioration : 500 DH HT
TVA 20% sur la retenue''',
            icon: Icons.warning_amber,
            note: 'La retenue pour détérioration est soumise à la TVA',
            entries: [
              JournalEntry(
                date: '20/01/2024',
                lines: [
                  JournalLine(
                    account: '4417',
                    label: 'Clients - emballages consignés',
                    debit: '3.000,00',
                  ),
                  JournalLine(
                    account: '7119',
                    label: 'Retenues sur emballages',
                    credit: '500,00',
                  ),
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    credit: '100,00',
                  ),
                  JournalLine(
                    account: '5141',
                    label: 'Banques',
                    credit: '2.400,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Emballages perdus ou non restitués',
            description: '''Facturation d'emballages non restitués :
- Valeur de consignation : 4.000 DH
- Majoration : 20% HT
TVA 20% sur le total''',
            icon: Icons.error_outline,
            note:
                'La facturation définitive est soumise à la TVA sur la totalité',
            entries: [
              JournalEntry(
                date: '31/01/2024',
                lines: [
                  JournalLine(
                    account: '3421',
                    label: 'Clients',
                    debit: '5.760,00',
                  ),
                  JournalLine(
                    account: '4417',
                    label: 'Clients - emballages consignés',
                    credit: '4.000,00',
                  ),
                  JournalLine(
                    account: '7119',
                    label: 'Majoration sur emballages',
                    credit: '800,00',
                  ),
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    credit: '960,00',
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildRetoursSection(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildSectionCard(
            context: context,
            title: 'Retour de marchandises vendues simple',
            description:
                'Retour de marchandises vendues pour 20.000 DH HT, TVA 20%',
            icon: Icons.assignment_return,
            note: 'Le retour annule partiellement la vente initiale',
            entries: [
              JournalEntry(
                date: '10/01/2024',
                lines: [
                  JournalLine(
                    account: '7111',
                    label: 'Ventes de marchandises',
                    debit: '20.000,00',
                  ),
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    debit: '4.000,00',
                  ),
                  JournalLine(
                    account: '3421',
                    label: 'Clients',
                    credit: '24.000,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Retour sur achat avec frais',
            description: '''Retour de marchandises achetées :
- Valeur marchandises : 15.000 DH HT
- Frais de retour : 500 DH HT
TVA 20% sur le total''',
            icon: Icons.assignment_return,
            note: 'Les frais de retour diminuent la valeur du retour',
            entries: [
              JournalEntry(
                date: '12/01/2024',
                lines: [
                  JournalLine(
                    account: '4411',
                    label: 'Fournisseurs',
                    debit: '17.400,00',
                  ),
                  JournalLine(
                    account: '6111',
                    label: 'Achats de marchandises',
                    credit: '14.500,00',
                  ),
                  JournalLine(
                    account: '34552',
                    label: 'État, TVA récupérable sur charges',
                    credit: '2.900,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Retour partiel avec décote',
            description: '''Retour de marchandises avec décote :
- Valeur initiale : 30.000 DH HT
- Décote accordée : 20%
TVA 20%''',
            icon: Icons.trending_down,
            note: 'La décote réduit la valeur du retour',
            entries: [
              JournalEntry(
                date: '15/01/2024',
                lines: [
                  JournalLine(
                    account: '7111',
                    label: 'Ventes de marchandises',
                    debit: '24.000,00',
                  ),
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    debit: '4.800,00',
                  ),
                  JournalLine(
                    account: '3421',
                    label: 'Clients',
                    credit: '28.800,00',
                  ),
                ],
              ),
            ],
          ),
          _buildSectionCard(
            context: context,
            title: 'Retour avec remplacement',
            description: '''Retour et remplacement de marchandises :
1. Retour : 25.000 DH HT
2. Remplacement : 28.000 DH HT
Différence facturée, TVA 20%''',
            icon: Icons.swap_horiz,
            note: 'Seule la différence fait l\'objet d\'une facturation',
            entries: [
              JournalEntry(
                date: '20/01/2024',
                lines: [
                  JournalLine(
                    account: '7111',
                    label: 'Ventes de marchandises',
                    debit: '25.000,00',
                  ),
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    debit: '5.000,00',
                  ),
                  JournalLine(
                    account: '3421',
                    label: 'Clients',
                    credit: '30.000,00',
                  ),
                ],
              ),
              JournalEntry(
                date: '20/01/2024',
                lines: [
                  JournalLine(
                    account: '3421',
                    label: 'Clients',
                    debit: '3.600,00',
                  ),
                  JournalLine(
                    account: '7111',
                    label: 'Ventes de marchandises',
                    credit: '3.000,00',
                  ),
                  JournalLine(
                    account: '4455',
                    label: 'État, TVA facturée',
                    credit: '600,00',
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }
}
