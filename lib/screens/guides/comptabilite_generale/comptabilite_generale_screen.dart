import 'package:flutter/material.dart';
import '../../../theme/app_theme.dart';
import 'introduction_screen.dart';
import 'documents_comptables_screen.dart';
import 'plan_comptable_screen.dart';
import 'operations_courantes_screen.dart';
import 'gestion_stocks_screen.dart';

class ComptabiliteGeneraleScreen extends StatefulWidget {
  const ComptabiliteGeneraleScreen({super.key});

  @override
  State<ComptabiliteGeneraleScreen> createState() =>
      _ComptabiliteGeneraleScreenState();
}

class _ComptabiliteGeneraleScreenState
    extends State<ComptabiliteGeneraleScreen> {
  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Comptabilité Générale',
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        child: Column(
          children: [
            _buildHeader(context),
            _buildContent(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      margin: const EdgeInsets.all(16),
      decoration: AppTheme.getGradientDecoration(colorScheme),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Principes Fondamentaux',
            style: textTheme.headlineMedium?.copyWith(
              color: colorScheme.onPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Découvrez les concepts essentiels de la comptabilité générale marocaine',
            style: textTheme.bodyLarge?.copyWith(
              color: colorScheme.onPrimary.withOpacity(0.8),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final moduleColors = AppTheme.getModuleColors(colorScheme);

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Sections détaillées',
            style: textTheme.titleLarge?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildModuleCard(
            context,
            title: 'Introduction',
            description: 'Principes et concepts de base',
            icon: Icons.school,
            color: moduleColors[0],
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => const IntroductionScreen()),
            ),
          ),
          _buildModuleCard(
            context,
            title: 'Documents Comptables',
            description: 'Bilan, CPC, Balance et Journal',
            icon: Icons.description,
            color: moduleColors[1],
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => const DocumentsComptablesScreen()),
            ),
          ),
          _buildModuleCard(
            context,
            title: 'Plan Comptable',
            description: 'Structure et organisation des comptes',
            icon: Icons.account_tree,
            color: moduleColors[2],
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => const PlanComptableScreen()),
            ),
          ),
          _buildModuleCard(
            context,
            title: 'Opérations Courantes',
            description: 'Factures, réductions, TVA et plus',
            icon: Icons.receipt_long,
            color: moduleColors[3],
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => const OperationsCourantesScreen()),
            ),
          ),
          _buildModuleCard(
            context,
            title: 'Gestion des Stocks',
            description: 'Méthodes d\'évaluation et systèmes d\'inventaire',
            icon: Icons.inventory_2,
            color: moduleColors[4],
            onTap: () => Navigator.push(
              context,
              MaterialPageRoute(
                  builder: (context) => const GestionStocksScreen()),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModuleCard(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: AppTheme.getModuleIconDecoration(colorScheme, color),
                child: Icon(icon, color: color, size: 28),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: color,
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }
}
