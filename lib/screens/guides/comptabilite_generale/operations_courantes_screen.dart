import 'package:flutter/material.dart';
import 'operations/transport_screen.dart';
import 'operations/effets_commerce_screen.dart';
import 'operations/accounting_entries_screen.dart';

class OperationsCourantesScreen extends StatefulWidget {
  const OperationsCourantesScreen({super.key});

  @override
  State<OperationsCourantesScreen> createState() =>
      _OperationsCourantesScreenState();
}

class _OperationsCourantesScreenState extends State<OperationsCourantesScreen> {
  final TextEditingController _searchController = TextEditingController();
  String _searchQuery = '';
  late ScrollController _scrollController;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
  }

  @override
  void dispose() {
    _searchController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  final List<Map<String, dynamic>> _allItems = [
    {
      'type': 'info',
      'title': 'La Facturation',
      'subtitle': 'Justificatif d\'opération commerciale',
      'icon': Icons.receipt_long,
      'keywords': ['facture', 'justificatif', 'commercial', 'document'],
      'content': [
        {
          'type': 'definition',
          'title': 'Définition',
          'text':
              'La facture est un document commercial qui détaille les biens ou services vendus, leurs prix et les conditions de vente',
          'icon': Icons.info_outline,
        },
        {
          'type': 'list',
          'title': 'Objectifs',
          'items': [
            'Prouver la réalisation d\'une transaction commerciale',
            'Servir de base pour la comptabilisation',
            'Permettre le suivi des opérations commerciales',
            'Faciliter le contrôle fiscal et la gestion de la TVA',
          ],
          'icon': Icons.stars,
        },
      ],
    },
    {
      'type': 'navigation',
      'title': 'Écritures Comptables',
      'subtitle': 'Guide complet des écritures avec exemples détaillés',
      'icon': Icons.account_balance_wallet,
      'screen': const AccountingEntriesScreen(),
      'keywords': [
        'écriture',
        'comptable',
        'exemple',
        'journal',
        'tva',
        'achat',
        'vente'
      ],
    },
    {
      'type': 'navigation',
      'title': 'Transport',
      'subtitle': 'Traitement comptable des frais de transport',
      'icon': Icons.local_shipping,
      'screen': const TransportScreen(),
      'keywords': ['transport', 'frais', 'livraison', 'port'],
    },
    {
      'type': 'navigation',
      'title': 'Effets de Commerce',
      'subtitle': 'Gestion des effets de commerce et écritures associées',
      'icon': Icons.receipt_long,
      'screen': const EffetsCommerceScreen(),
      'keywords': ['effet', 'lettre de change', 'billet à ordre', 'traite'],
    },
    {
      'type': 'info',
      'title': 'Éléments de la Facture',
      'subtitle': 'Structure et composants d\'une facture',
      'icon': Icons.description,
      'keywords': ['facture', 'élément', 'composant', 'structure'],
      'content': [
        {
          'type': 'table',
          'columns': ['Élément', 'Description'],
          'rows': [
            ['En-tête', 'Informations sur le vendeur et l\'acheteur'],
            ['Références', 'N° de facture, date, conditions de paiement'],
            ['Corps', 'Détail des articles ou services'],
            ['Montants HT', 'Prix unitaires et totaux hors taxes'],
            ['Réductions', 'Remises, rabais, ristournes'],
            ['TVA', 'Taux et montants de TVA par taux'],
            ['Pied', 'Totaux, net à payer, mentions légales'],
          ],
        },
      ],
    },
    {
      'type': 'info',
      'title': 'Réductions Commerciales',
      'subtitle': 'Types et traitement des réductions',
      'icon': Icons.discount,
      'keywords': ['réduction', 'remise', 'rabais', 'ristourne', 'escompte'],
      'content': [
        {
          'type': 'definition',
          'title': 'Types de Réductions',
          'text':
              'Les réductions peuvent être commerciales ou financières, chacune ayant un traitement comptable spécifique.',
          'icon': Icons.category,
        },
        {
          'type': 'list',
          'title': 'Réductions Commerciales',
          'items': [
            'Rabais : réduction pour défaut de qualité ou non-conformité',
            'Remise : réduction basée sur le volume ou la qualité du client',
            'Ristourne : réduction périodique sur le chiffre d\'affaires',
          ],
          'icon': Icons.store,
        },
        {
          'type': 'list',
          'title': 'Réduction Financière',
          'items': [
            'Escompte : réduction pour paiement anticipé',
            'Calculé sur le net commercial',
            'Soumis à la TVA',
            'Impact sur le résultat financier',
          ],
          'icon': Icons.payments,
        },
      ],
    },
  ];

  List<Map<String, dynamic>> get _filteredItems {
    if (_searchQuery.isEmpty) return _allItems;
    return _allItems.where((item) {
      final keywords =
          (item['keywords'] as List<String>).join(' ').toLowerCase();
      final title = item['title'].toString().toLowerCase();
      final subtitle = item['subtitle'].toString().toLowerCase();
      final searchLower = _searchQuery.toLowerCase();
      return keywords.contains(searchLower) ||
          title.contains(searchLower) ||
          subtitle.contains(searchLower);
    }).toList();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        centerTitle: true,
        title: Text(
          'Opérations Courantes',
          style: textTheme.titleLarge?.copyWith(
            color: colorScheme.onPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                colorScheme.primary,
                colorScheme.primaryContainer,
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
        ),
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              colorScheme.surface,
              colorScheme.surfaceContainerHighest.withOpacity(0.5),
            ],
          ),
        ),
        child: Column(
          children: [
            Container(
              padding: const EdgeInsets.all(16.0),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                boxShadow: [
                  BoxShadow(
                    color: colorScheme.shadow.withOpacity(0.1),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TextField(
                controller: _searchController,
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface,
                ),
                decoration: InputDecoration(
                  hintText: 'Rechercher une opération...',
                  hintStyle: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface.withOpacity(0.5),
                  ),
                  prefixIcon: Icon(
                    Icons.search,
                    color: colorScheme.primary,
                    size: 24,
                  ),
                  suffixIcon: _searchQuery.isNotEmpty
                      ? IconButton(
                          icon: Icon(
                            Icons.clear,
                            color: colorScheme.primary,
                            size: 20,
                          ),
                          onPressed: () {
                            setState(() {
                              _searchController.clear();
                              _searchQuery = '';
                            });
                          },
                        )
                      : null,
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: colorScheme.outline.withOpacity(0.3),
                    ),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: colorScheme.outline.withOpacity(0.2),
                    ),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(12),
                    borderSide: BorderSide(
                      color: colorScheme.primary,
                      width: 2,
                    ),
                  ),
                  filled: true,
                  fillColor: colorScheme.surface,
                  contentPadding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                ),
                onChanged: (value) {
                  setState(() {
                    _searchQuery = value;
                  });
                },
              ),
            ),
            Expanded(
              child: Scrollbar(
                controller: _scrollController,
                child: ListView.builder(
                  controller: _scrollController,
                  padding: const EdgeInsets.all(16.0),
                  itemCount: _filteredItems.length,
                  itemBuilder: (context, index) {
                    final item = _filteredItems[index];
                    if (item['type'] == 'navigation') {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: _buildNavigationCard(
                          context,
                          title: item['title'],
                          subtitle: item['subtitle'],
                          icon: item['icon'],
                          color: _getColorForIndex(index, colorScheme),
                          onTap: () => Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => item['screen'],
                            ),
                          ),
                        ),
                      );
                    } else {
                      return Padding(
                        padding: const EdgeInsets.only(bottom: 16),
                        child: _buildInfoCard(
                          context,
                          title: item['title'],
                          subtitle: item['subtitle'],
                          icon: item['icon'],
                          color: _getColorForIndex(index, colorScheme),
                          content: [
                            for (var contentItem in item['content'])
                              if (contentItem['type'] == 'definition')
                                _buildDefinitionTile(
                                  contentItem['title'],
                                  contentItem['text'],
                                  icon: contentItem['icon'],
                                )
                              else if (contentItem['type'] == 'list')
                                _buildListTile(
                                  contentItem['title'],
                                  List<String>.from(contentItem['items']),
                                  icon: contentItem['icon'],
                                )
                              else if (contentItem['type'] == 'table')
                                _buildTableTile(
                                  context,
                                  columns:
                                      List<String>.from(contentItem['columns']),
                                  rows: List<List<String>>.from(
                                    contentItem['rows'].map(
                                      (row) => List<String>.from(row),
                                    ),
                                  ),
                                ),
                          ],
                        ),
                      );
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNavigationCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 28),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              Icon(
                Icons.chevron_right,
                color: color,
                size: 24,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoCard(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required List<Widget> content,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Theme(
        data: Theme.of(context).copyWith(dividerColor: Colors.transparent),
        child: ExpansionTile(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          backgroundColor: colorScheme.surface,
          collapsedBackgroundColor: colorScheme.surface,
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: color, size: 28),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: textTheme.bodyMedium?.copyWith(
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(16),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: content,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDefinitionTile(String title, String definition,
      {IconData? icon}) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(icon, size: 20, color: colorScheme.primary),
                const SizedBox(width: 8),
              ],
              Text(
                title,
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            definition,
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
              height: 1.5,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListTile(String title, List<String> items, {IconData? icon}) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.secondaryContainer.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.secondary.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Icon(icon, size: 20, color: colorScheme.secondary),
                const SizedBox(width: 8),
              ],
              Text(
                title,
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.secondary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...items.map((item) => Padding(
                padding: const EdgeInsets.only(left: 8, bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Icon(
                      Icons.arrow_right,
                      size: 20,
                      color: colorScheme.secondary,
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Text(
                        item,
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurface,
                          height: 1.5,
                        ),
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ),
    );
  }

  Widget _buildTableTile(
    BuildContext context, {
    required List<String> columns,
    required List<List<String>> rows,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.2),
        ),
        color: colorScheme.surface,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: Table(
          columnWidths: const {
            0: FlexColumnWidth(2),
            1: FlexColumnWidth(3),
          },
          children: [
            TableRow(
              decoration: BoxDecoration(
                color: colorScheme.primaryContainer.withOpacity(0.3),
              ),
              children: columns
                  .map((col) => Container(
                        padding: const EdgeInsets.all(12),
                        child: Text(
                          col,
                          style: textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.primary,
                          ),
                        ),
                      ))
                  .toList(),
            ),
            ...rows.map(
              (row) => TableRow(
                decoration: BoxDecoration(
                  color: colorScheme.surface,
                ),
                children: row
                    .map((cell) => Container(
                          padding: const EdgeInsets.all(12),
                          child: Text(
                            cell,
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorScheme.onSurface,
                              height: 1.5,
                            ),
                          ),
                        ))
                    .toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getColorForIndex(int index, ColorScheme colorScheme) {
    final colors = [
      colorScheme.primary,
      colorScheme.secondary,
      colorScheme.tertiary,
      colorScheme.primary,
      colorScheme.secondary,
    ];
    return colors[index % colors.length];
  }
}
