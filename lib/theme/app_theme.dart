import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

class AppTheme {
  // Design system constants
  static const double _borderRadius = 16.0;
  static const double _smallBorderRadius = 12.0;
  static const double _cardElevation = 2.0;
  static const double _headerElevation = 3.0;
  static const double _spacing = 16.0;
  static const double _smallSpacing = 8.0;

  static ThemeData getThemeData(ColorScheme colorScheme) {
    final baseTextTheme = GoogleFonts.poppinsTextTheme();
    
    return ThemeData(
      useMaterial3: true,
      colorScheme: colorScheme,
      textTheme: baseTextTheme.copyWith(
        headlineLarge: baseTextTheme.headlineLarge?.copyWith(
          fontWeight: FontWeight.w700,
          letterSpacing: -0.5,
        ),
        headlineMedium: baseTextTheme.headlineMedium?.copyWith(
          fontWeight: FontWeight.w600,
          letterSpacing: -0.3,
        ),
        titleLarge: baseTextTheme.titleLarge?.copyWith(
          fontWeight: FontWeight.w600,
          letterSpacing: 0.1,
        ),
      ),
      appBarTheme: AppBarTheme(
        centerTitle: true,
        elevation: _headerElevation,
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        surfaceTintColor: Colors.transparent,
        shadowColor: colorScheme.shadow.withOpacity(0.1),
      ),
      navigationRailTheme: NavigationRailThemeData(
        backgroundColor: colorScheme.surface,
        elevation: _cardElevation,
        selectedIconTheme: IconThemeData(
          color: colorScheme.primary,
          size: 28,
        ),
        unselectedIconTheme: IconThemeData(
          color: colorScheme.onSurfaceVariant,
          size: 24,
        ),
        selectedLabelTextStyle: TextStyle(
          color: colorScheme.primary,
          fontWeight: FontWeight.w600,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
        unselectedLabelTextStyle: TextStyle(
          color: colorScheme.onSurfaceVariant,
          fontWeight: FontWeight.w500,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
        useIndicator: true,
        indicatorColor: colorScheme.primaryContainer,
        indicatorShape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
        ),
        minWidth: 72,
        minExtendedWidth: 220,
      ),
      navigationBarTheme: NavigationBarThemeData(
        backgroundColor: colorScheme.surface,
        elevation: _headerElevation,
        shadowColor: colorScheme.shadow.withOpacity(0.1),
        surfaceTintColor: Colors.transparent,
        indicatorColor: colorScheme.primaryContainer,
        labelTextStyle: WidgetStateProperty.resolveWith((states) {
          if (states.contains(WidgetState.selected)) {
            return TextStyle(
              color: colorScheme.primary,
              fontSize: 12,
              fontWeight: FontWeight.w600,
              letterSpacing: 0.1,
            );
          }
          return TextStyle(
            color: colorScheme.onSurfaceVariant,
            fontSize: 12,
            letterSpacing: 0.1,
          );
        }),
      ),
      cardTheme: CardThemeData(
        elevation: _cardElevation,
        shadowColor: colorScheme.shadow.withOpacity(0.1),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_borderRadius),
        ),
        clipBehavior: Clip.antiAlias,
        color: colorScheme.surface,
      ),
      snackBarTheme: SnackBarThemeData(
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
        ),
        elevation: _cardElevation,
        backgroundColor: colorScheme.inverseSurface,
        contentTextStyle: TextStyle(
          color: colorScheme.onInverseSurface,
          fontSize: 14,
          letterSpacing: 0.1,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          padding: EdgeInsets.symmetric(
            horizontal: _spacing * 1.5,
            vertical: _spacing * 0.75,
          ),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(_smallBorderRadius),
          ),
          elevation: _cardElevation,
          shadowColor: colorScheme.shadow.withOpacity(0.2),
          textStyle: const TextStyle(
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.5),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
          borderSide: BorderSide.none,
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(_smallBorderRadius),
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: _spacing,
          vertical: _spacing,
        ),
        hintStyle: TextStyle(
          color: colorScheme.onSurfaceVariant.withOpacity(0.7),
          fontWeight: FontWeight.w400,
        ),
      ),
    );
  }

  static BoxDecoration getGradientDecoration(ColorScheme colorScheme) {
    return BoxDecoration(
      gradient: LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          colorScheme.primary,
          colorScheme.primary.withOpacity(0.8),
          colorScheme.primaryContainer,
        ],
        stops: const [0.0, 0.5, 1.0],
      ),
      borderRadius: BorderRadius.circular(_borderRadius),
      boxShadow: [
        BoxShadow(
          color: colorScheme.shadow.withOpacity(0.15),
          blurRadius: 12,
          offset: const Offset(0, 4),
          spreadRadius: 2,
        ),
      ],
    );
  }

  static BoxDecoration getModuleIconDecoration(ColorScheme colorScheme, Color baseColor) {
    return BoxDecoration(
      color: baseColor.withOpacity(0.12),
      borderRadius: BorderRadius.circular(_smallBorderRadius),
      border: Border.all(
        color: baseColor.withOpacity(0.05),
        width: 1,
      ),
    );
  }

  static List<Color> getModuleColors(ColorScheme colorScheme) {
    return [
      colorScheme.primary,
      colorScheme.secondary,
      colorScheme.tertiary,
      Color.lerp(colorScheme.primary, colorScheme.secondary, 0.5)!,
      Color.lerp(colorScheme.secondary, colorScheme.tertiary, 0.5)!,
    ];
  }

  static BoxDecoration getCardDecoration(ColorScheme colorScheme) {
    return BoxDecoration(
      color: colorScheme.surface,
      borderRadius: BorderRadius.circular(_borderRadius),
      boxShadow: [
        BoxShadow(
          color: colorScheme.shadow.withOpacity(0.08),
          blurRadius: 8,
          offset: const Offset(0, 3),
          spreadRadius: 1,
        ),
      ],
    );
  }

  static ColorScheme getDarkColorScheme() {
    return ColorScheme.dark(
      primary: const Color(0xFF60A5FA),      // Bright blue
      secondary: const Color(0xFF818CF8),     // Indigo
      tertiary: const Color(0xFFF472B6),     // Pink
      
      primaryContainer: const Color(0xFF1E40AF).withOpacity(0.15),  // Darker blue
      secondaryContainer: const Color(0xFF4338CA).withOpacity(0.15), // Darker indigo
      tertiaryContainer: const Color(0xFFBE185D).withOpacity(0.15),  // Darker pink
      
      surface: const Color(0xFF1A1B26),       // Dark background
      surfaceContainerHighest: const Color(0xFF16161E),     // Darkest background
      
      error: const Color(0xFFF87171),         // Soft red
      onError: Colors.white,
      
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onTertiary: Colors.white,
      onSurface: const Color(0xFFE5E7EB),  // Light gray
      onSurfaceVariant: const Color(0xFFD1D5DB), // Lighter gray for variants
      
      onPrimaryContainer: const Color(0xFFBFDBFE), // Light blue
      onSecondaryContainer: const Color(0xFFC7D2FE), // Light indigo
      onTertiaryContainer: const Color(0xFFFCE7F3), // Light pink
      
      outline: const Color(0xFF374151).withOpacity(0.5), // Semi-transparent border
      shadow: const Color(0xFF000000),       // Pure black for shadows
      inverseSurface: const Color(0xFFFFFFFF), // White
      onInverseSurface: const Color(0xFF1F2937), // Dark gray
      inversePrimary: const Color(0xFF2563EB), // Original blue
    );
  }

  static ColorScheme getLightColorScheme() {
    return ColorScheme.light(
      primary: const Color(0xFF2563EB),      // Blue
      secondary: const Color(0xFF4F46E5),     // Indigo
      tertiary: const Color(0xFFDB2777),     // Pink
      
      primaryContainer: const Color(0xFFDBEAFE),   // Light blue
      secondaryContainer: const Color(0xFFE0E7FF),  // Light indigo
      tertiaryContainer: const Color(0xFFFCE7F3),  // Light pink
      
      surface: Colors.white,
      surfaceContainerHighest: const Color(0xFFF8FAFC),      // Light blue gray
      
      error: const Color(0xFFEF4444),         // Red
      onError: Colors.white,
      
      onPrimary: Colors.white,
      onSecondary: Colors.white,
      onTertiary: Colors.white,
      onSurface: const Color(0xFF1F2937),  // Dark gray
      
      onPrimaryContainer: const Color(0xFF1E40AF),   // Darker blue
      onSecondaryContainer: const Color(0xFF3730A3), // Darker indigo
      onTertiaryContainer: const Color(0xFF9D174D),  // Darker pink
      
      outline: const Color(0xFFE2E8F0),       // Border color
    );
  }
}
