import 'package:hive_flutter/hive_flutter.dart';
import 'package:uuid/uuid.dart'; // For generating default username

// Core Models
import '../models/hive/user_profile.dart';
import '../models/hive/user_progress.dart';

// Quiz Models
import '../models/hive/quiz_attempt.dart';
import '../models/hive/active_quiz_state.dart';

// Exam Models (New)
import '../models/exam/exam_attempt.dart';
import '../models/exam/active_exam_state.dart';


class UserProgressService {
  // Box Names
  static const String _userProgressBoxName = 'userProgressBox';
  static const String _quizAttemptsBoxName = 'quizAttemptsBox';
  static const String _activeQuizBoxName = 'activeQuizBox';
  static const String _examAttemptsBoxName = 'examAttemptsBox'; // New
  static const String _activeExamBoxName = 'activeExamBox';     // New

  // Keys
  static const String _userProgressKey = 'currentUserProgress';

  // Box Instances
  late Box<UserProgress> _userProgressBox;
  late Box<QuizAttempt> _quizAttemptsBox;
  late Box<ActiveQuizState> _activeQuizBox;
  late Box<ExamAttempt> _examAttemptsBox;     // New
  late Box<ActiveExamState> _activeExamBox;     // New

  Future<void> init() async {
    // Open all required Hive boxes
    _userProgressBox = await Hive.openBox<UserProgress>(_userProgressBoxName);
    _quizAttemptsBox = await Hive.openBox<QuizAttempt>(_quizAttemptsBoxName);
    _activeQuizBox = await Hive.openBox<ActiveQuizState>(_activeQuizBoxName);
    _examAttemptsBox = await Hive.openBox<ExamAttempt>(_examAttemptsBoxName); // New
    _activeExamBox = await Hive.openBox<ActiveExamState>(_activeExamBoxName); // New

    // Check if user progress already exists, if not, create it
    if (_userProgressBox.get(_userProgressKey) == null) {
      await _createInitialUserProgress();
    }
  }

  Future<void> _createInitialUserProgress() async {
    final defaultUsername = 'User-${const Uuid().v4().substring(0, 6)}';
    final initialProfile = UserProfile(username: defaultUsername);
    final initialProgress = UserProgress(userProfile: initialProfile);

    // Initialize HiveLists using the already opened boxes
    initialProgress.quizAttempts = HiveList(_quizAttemptsBox);
    initialProgress.examAttempts = HiveList(_examAttemptsBox); // New

    await _userProgressBox.put(_userProgressKey, initialProgress);
  }

  UserProgress? getCurrentUserProgress() {
    return _userProgressBox.get(_userProgressKey);
  }

  Future<void> updateUserProfile(UserProfile updatedProfile) async {
    final progress = getCurrentUserProgress();
    if (progress != null) {
      progress.userProfile = updatedProfile;
      await progress.save();
    }
  }

  // --- Quiz Attempt Methods ---

  Future<void> addQuizAttempt(QuizAttempt attempt) async {
    final progress = getCurrentUserProgress();
    if (progress != null) {
      progress.quizAttempts ??= HiveList(_quizAttemptsBox);
      await _quizAttemptsBox.add(attempt);
      progress.quizAttempts!.add(attempt);
      await progress.save();
      print("Quiz attempt added and progress saved.");
    } else {
       print("Error: Could not find user progress to add quiz attempt.");
    }
  }

  List<QuizAttempt> getAllQuizAttempts() {
    final progress = getCurrentUserProgress();
    return progress?.quizAttempts?.toList() ?? [];
  }

  // --- Exam Attempt Methods (New) ---

  Future<void> addExamAttempt(ExamAttempt attempt) async {
    final progress = getCurrentUserProgress();
    if (progress != null) {
      progress.examAttempts ??= HiveList(_examAttemptsBox);
      await _examAttemptsBox.add(attempt); // Add to its own box
      progress.examAttempts!.add(attempt); // Link to UserProgress
      
      // Update the user profile with latest exam data
      final userProfile = progress.userProfile;
      final percentage = attempt.score / attempt.totalQuestions * 100;
      
      // Check if this is a better score
      final currentBest = userProfile.bestExamPercentage ?? 0.0;
      final isBetterScore = percentage > currentBest;
      
      // Update profile with latest exam data
      userProfile.lastExamScore = attempt.score;
      userProfile.lastExamTotal = attempt.totalQuestions;
      userProfile.lastExamId = attempt.examId;
      userProfile.lastExamDate = attempt.timestamp;
      
      // Update best percentage if this attempt is better
      if (isBetterScore) {
        userProfile.bestExamPercentage = percentage;
      }
      
      await progress.save();
      print("Exam attempt added and user profile updated with new exam data.");
    } else {
       print("Error: Could not find user progress to add exam attempt.");
    }
  }

  List<ExamAttempt> getAllExamAttempts() {
    final progress = getCurrentUserProgress();
    return progress?.examAttempts?.toList() ?? [];
  }


  // --- Active Quiz State Methods ---

  Future<void> saveActiveQuizState(ActiveQuizState activeState) async {
    await _activeQuizBox.put(activeState.storageKey, activeState);
    print("Active quiz state saved for key: ${activeState.storageKey}");
  }

  ActiveQuizState? getActiveQuizState(String categoryName, String levelName) {
    final key = '$categoryName-$levelName';
    print("Attempting to retrieve active quiz state for key: $key");
    final state = _activeQuizBox.get(key);
    print("Retrieved quiz state: ${state != null ? 'Found' : 'Not Found'}");
    return state;
  }

  Future<void> deleteActiveQuizState(String categoryName, String levelName) async {
     final key = '$categoryName-$levelName';
    if (_activeQuizBox.containsKey(key)) {
      await _activeQuizBox.delete(key);
      print("Active quiz state deleted for key: $key");
    } else {
       print("No active quiz state found to delete for key: $key");
    }
  }

  List<ActiveQuizState> getAllActiveQuizzes() {
    return _activeQuizBox.values.toList();
  }

  // --- Active Exam State Methods (New) ---

  Future<void> saveActiveExamState(ActiveExamState activeState) async {
    // Use examId as the key for active exams
    await _activeExamBox.put(activeState.examId, activeState);
    print("Active exam state saved for key: ${activeState.examId}");
  }

  ActiveExamState? getActiveExamState(String examId) {
    print("Attempting to retrieve active exam state for key: $examId");
    final state = _activeExamBox.get(examId);
    print("Retrieved exam state: ${state != null ? 'Found' : 'Not Found'}");
    return state;
  }

  Future<void> deleteActiveExamState(String examId) async {
    if (_activeExamBox.containsKey(examId)) {
      await _activeExamBox.delete(examId);
      print("Active exam state deleted for key: $examId");
    } else {
       print("No active exam state found to delete for key: $examId");
    }
  }

  List<ActiveExamState> getAllActiveExams() {
    return _activeExamBox.values.toList();
  }

  // --- Dispose Method ---

  Future<void> dispose() async {
    // Compact and close all boxes
    await _userProgressBox.compact();
    await _userProgressBox.close();
    await _quizAttemptsBox.compact();
    await _quizAttemptsBox.close();
    await _activeQuizBox.compact();
    await _activeQuizBox.close();
    await _examAttemptsBox.compact(); // New
    await _examAttemptsBox.close();    // New
    await _activeExamBox.compact();   // New
    await _activeExamBox.close();      // New
  }
}
