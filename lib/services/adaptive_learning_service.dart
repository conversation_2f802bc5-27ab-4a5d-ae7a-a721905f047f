import 'dart:math';
import 'package:hive/hive.dart';
import '../models/adaptive_learning_models.dart';

class AdaptiveLearningService {
  static const String _performanceBoxName = 'user_performance';
  static const String _learningPathBoxName = 'learning_paths';
  static const String _weaknessBoxName = 'weakness_areas';
  static const String _achievementBoxName = 'achievements';
  static const String _sessionBoxName = 'study_sessions';

  late Box<UserPerformance> _performanceBox;
  late Box<LearningPath> _learningPathBox;
  late Box<WeaknessArea> _weaknessBox;
  late Box<Achievement> _achievementBox;
  late Box<StudySession> _sessionBox;

  static final AdaptiveLearningService _instance = AdaptiveLearningService._internal();
  factory AdaptiveLearningService() => _instance;
  AdaptiveLearningService._internal();

  Future<void> initialize() async {
    _performanceBox = await Hive.openBox<UserPerformance>(_performanceBoxName);
    _learningPathBox = await Hive.openBox<LearningPath>(_learningPathBoxName);
    _weaknessBox = await Hive.openBox<WeaknessArea>(_weaknessBoxName);
    _achievementBox = await Hive.openBox<Achievement>(_achievementBoxName);
    _sessionBox = await Hive.openBox<StudySession>(_sessionBoxName);
    
    await _initializeDefaultAchievements();
  }

  // Performance tracking
  Future<void> recordQuestionAttempt({
    required String categoryName,
    required String levelName,
    required String topic,
    required bool isCorrect,
    required Duration timeSpent,
  }) async {
    final key = '${categoryName}_$levelName';
    final existing = _performanceBox.get(key);
    
    final topicAccuracy = Map<String, int>.from(existing?.topicAccuracy ?? {});
    topicAccuracy[topic] = (topicAccuracy[topic] ?? 0) + (isCorrect ? 1 : 0);
    
    final updated = existing?.copyWith(
      questionsAnswered: (existing.questionsAnswered) + 1,
      correctAnswers: (existing.correctAnswers) + (isCorrect ? 1 : 0),
      averageTimePerQuestion: _calculateNewAverage(
        existing.averageTimePerQuestion,
        existing.questionsAnswered,
        timeSpent.inSeconds.toDouble(),
      ),
      lastUpdated: DateTime.now(),
      streakCount: isCorrect ? (existing.streakCount + 1) : 0,
      topicAccuracy: topicAccuracy,
    ) ?? UserPerformance(
      categoryName: categoryName,
      levelName: levelName,
      questionsAnswered: 1,
      correctAnswers: isCorrect ? 1 : 0,
      averageTimePerQuestion: timeSpent.inSeconds.toDouble(),
      weakTopics: [],
      strongTopics: [],
      lastUpdated: DateTime.now(),
      streakCount: isCorrect ? 1 : 0,
      topicAccuracy: topicAccuracy,
    );
    
    await _performanceBox.put(key, updated);
    await _updateWeaknessAreas(updated);
    await _checkAchievements(updated);
  }

  double _calculateNewAverage(double currentAverage, int count, double newValue) {
    return ((currentAverage * count) + newValue) / (count + 1);
  }

  Future<void> _updateWeaknessAreas(UserPerformance performance) async {
    final weakTopics = <String>[];
    final strongTopics = <String>[];
    
    for (final entry in performance.topicAccuracy.entries) {
      final topic = entry.key;
      final correct = entry.value;
      final total = _getTotalAttemptsForTopic(performance.categoryName, topic);
      final accuracy = total > 0 ? correct / total : 0.0;
      
      if (accuracy < 0.7 && total >= 3) {
        weakTopics.add(topic);
        await _recordWeaknessArea(performance.categoryName, topic, accuracy, total);
      } else if (accuracy >= 0.8) {
        strongTopics.add(topic);
      }
    }
    
    final updated = performance.copyWith(
      weakTopics: weakTopics,
      strongTopics: strongTopics,
    );
    
    await _performanceBox.put('${performance.categoryName}_${performance.levelName}', updated);
  }

  int _getTotalAttemptsForTopic(String categoryName, String topic) {
    // This would need to be tracked separately or calculated from session history
    // For now, return a mock value
    return 5; // This should be properly implemented
  }

  Future<void> _recordWeaknessArea(String categoryName, String topic, double accuracy, int attempts) async {
    final key = '${categoryName}_$topic';
    final existing = _weaknessBox.get(key);
    
    final weakness = existing?.copyWith(
      accuracyRate: accuracy,
      totalAttempts: attempts,
      needsReview: accuracy < 0.6,
      priority: _calculatePriority(accuracy, attempts),
    ) ?? WeaknessArea(
      topicName: topic,
      categoryName: categoryName,
      accuracyRate: accuracy,
      totalAttempts: attempts,
      commonMistakes: [],
      identifiedAt: DateTime.now(),
      priority: _calculatePriority(accuracy, attempts),
    );
    
    await _weaknessBox.put(key, weakness);
  }

  int _calculatePriority(double accuracy, int attempts) {
    if (accuracy < 0.3) return 5; // Critical
    if (accuracy < 0.5) return 4; // High
    if (accuracy < 0.7) return 3; // Medium
    return 2; // Low
  }

  // Learning path management
  Future<LearningPath> generateLearningPath(String categoryName) async {
    final performance = getUserPerformance(categoryName);
    final weaknesses = getWeaknessAreas(categoryName);
    
    final recommendedTopics = <String>[];
    
    // Add weak areas first (highest priority)
    for (final weakness in weaknesses.where((w) => w.needsReview)) {
      recommendedTopics.add(weakness.topicName);
    }
    
    // Add topics based on difficulty progression
    final currentLevel = _determineDifficultyLevel(performance);
    recommendedTopics.addAll(_getTopicsForLevel(categoryName, currentLevel));
    
    final path = LearningPath(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      categoryName: categoryName,
      recommendedTopics: recommendedTopics,
      completedTopics: [],
      currentDifficulty: currentLevel,
      createdAt: DateTime.now(),
      lastAccessed: DateTime.now(),
    );
    
    await _learningPathBox.put(path.id, path);
    return path;
  }

  String _determineDifficultyLevel(List<UserPerformance> performances) {
    if (performances.isEmpty) return 'Débutant';
    
    final avgAccuracy = performances
        .map((p) => p.accuracy)
        .reduce((a, b) => a + b) / performances.length;
    
    if (avgAccuracy >= 0.8) return 'Expert';
    if (avgAccuracy >= 0.6) return 'Intermédiaire';
    return 'Débutant';
  }

  List<String> _getTopicsForLevel(String categoryName, String level) {
    // This would be based on curriculum or predefined learning paths
    // For now, return sample topics
    switch (level.toLowerCase()) {
      case 'débutant':
        return ['Bases fiscales', 'Concepts fondamentaux'];
      case 'intermédiaire':
        return ['Applications pratiques', 'Cas complexes'];
      case 'expert':
        return ['Optimisation fiscale', 'Jurisprudence récente'];
      default:
        return ['Révision générale'];
    }
  }

  // Achievement system
  Future<void> _initializeDefaultAchievements() async {
    if (_achievementBox.isNotEmpty) return;
    
    final achievements = [
      Achievement(
        id: 'first_quiz',
        title: 'Premier Quiz',
        description: 'Terminez votre premier quiz',
        iconName: 'school',
        categoryName: 'general',
        unlockedAt: DateTime(2024),
        points: 10,
        criteria: {'quizzes_completed': 1},
      ),
      Achievement(
        id: 'perfect_score',
        title: 'Score Parfait',
        description: 'Obtenez 100% à un quiz',
        iconName: 'star',
        categoryName: 'general',
        unlockedAt: DateTime(2024),
        points: 50,
        rarity: 'rare',
        criteria: {'perfect_scores': 1},
      ),
      Achievement(
        id: 'streak_10',
        title: 'En Série',
        description: 'Répondez correctement à 10 questions consécutives',
        iconName: 'whatshot',
        categoryName: 'general',
        unlockedAt: DateTime(2024),
        points: 25,
        criteria: {'max_streak': 10},
      ),
      Achievement(
        id: 'agriculture_expert',
        title: 'Expert Agricole',
        description: 'Maîtrisez tous les niveaux en fiscalité agricole',
        iconName: 'agriculture',
        categoryName: 'Agriculture et Fiscalité 2025',
        unlockedAt: DateTime(2024),
        points: 100,
        rarity: 'epic',
        criteria: {'category_mastery': 'Agriculture et Fiscalité 2025'},
      ),
    ];
    
    for (final achievement in achievements) {
      await _achievementBox.put(achievement.id, achievement);
    }
  }

  Future<void> _checkAchievements(UserPerformance performance) async {
    final achievements = _achievementBox.values.toList();
    
    for (final achievement in achievements.where((a) => !a.isUnlocked)) {
      bool shouldUnlock = false;
      
      switch (achievement.id) {
        case 'first_quiz':
          shouldUnlock = performance.questionsAnswered >= 5; // Basic quiz completion
          break;
        case 'perfect_score':
          shouldUnlock = performance.accuracy == 1.0 && performance.questionsAnswered >= 5;
          break;
        case 'streak_10':
          shouldUnlock = performance.streakCount >= 10;
          break;
        case 'agriculture_expert':
          shouldUnlock = achievement.categoryName == performance.categoryName &&
              performance.accuracy >= 0.9 && performance.questionsAnswered >= 20;
          break;
      }
      
      if (shouldUnlock) {
        final unlockedAchievement = achievement.copyWith(
          isUnlocked: true,
          unlockedAt: DateTime.now(),
        );
        await _achievementBox.put(achievement.id, unlockedAchievement);
      }
    }
  }

  // Adaptive difficulty
  String adaptQuestionDifficulty(UserPerformance performance, String currentLevel) {
    final accuracy = performance.accuracy;
    final recentQuestions = performance.questionsAnswered >= 5 ? 
        performance.questionsAnswered : 0;
    
    if (recentQuestions < 5) return currentLevel; // Not enough data
    
    // If doing very well (>85%), increase difficulty
    if (accuracy >= 0.85) {
      switch (currentLevel.toLowerCase()) {
        case 'débutant':
          return 'Intermédiaire';
        case 'intermédiaire':
          return 'Expert';
        default:
          return currentLevel;
      }
    }
    
    // If struggling (<60%), decrease difficulty
    if (accuracy < 0.60) {
      switch (currentLevel.toLowerCase()) {
        case 'expert':
          return 'Intermédiaire';
        case 'intermédiaire':
          return 'Débutant';
        default:
          return currentLevel;
      }
    }
    
    return currentLevel; // Stay at current level
  }

  // Data retrieval methods
  List<UserPerformance> getUserPerformance(String categoryName) {
    return _performanceBox.values
        .where((p) => p.categoryName == categoryName)
        .toList();
  }

  List<WeaknessArea> getWeaknessAreas(String categoryName) {
    return _weaknessBox.values
        .where((w) => w.categoryName == categoryName)
        .toList()
      ..sort((a, b) => b.priority.compareTo(a.priority));
  }

  List<Achievement> getAchievements({String? categoryName}) {
    final achievements = _achievementBox.values.toList();
    if (categoryName != null) {
      return achievements
          .where((a) => a.categoryName == categoryName || a.categoryName == 'general')
          .toList();
    }
    return achievements;
  }

  List<Achievement> getUnlockedAchievements({String? categoryName}) {
    return getAchievements(categoryName: categoryName)
        .where((a) => a.isUnlocked)
        .toList();
  }

  // Study session management
  Future<String> startStudySession(String categoryName, String levelName) async {
    final sessionId = DateTime.now().millisecondsSinceEpoch.toString();
    final session = StudySession(
      id: sessionId,
      categoryName: categoryName,
      levelName: levelName,
      startTime: DateTime.now(),
    );
    
    await _sessionBox.put(sessionId, session);
    return sessionId;
  }

  Future<void> endStudySession(String sessionId, {
    int questionsAttempted = 0,
    int correctAnswers = 0,
    List<String> topicsStudied = const [],
  }) async {
    final session = _sessionBox.get(sessionId);
    if (session == null) return;
    
    final endTime = DateTime.now();
    final totalTime = endTime.difference(session.startTime);
    
    final updatedSession = session.copyWith(
      endTime: endTime,
      questionsAttempted: questionsAttempted,
      correctAnswers: correctAnswers,
      totalTime: totalTime,
      topicsStudied: topicsStudied,
      isCompleted: true,
    );
    
    await _sessionBox.put(sessionId, updatedSession);
  }

  // Analytics and insights
  Map<String, dynamic> getLearningAnalytics(String categoryName) {
    final performances = getUserPerformance(categoryName);
    final weaknesses = getWeaknessAreas(categoryName);
    final sessions = _sessionBox.values
        .where((s) => s.categoryName == categoryName && s.isCompleted)
        .toList();
    
    final totalTime = sessions.fold<Duration>(
      Duration.zero,
      (total, session) => total + session.totalTime,
    );
    
    final avgAccuracy = performances.isNotEmpty
        ? performances.map((p) => p.accuracy).reduce((a, b) => a + b) / performances.length
        : 0.0;
    
    return {
      'totalStudyTime': totalTime.inMinutes,
      'averageAccuracy': avgAccuracy,
      'totalQuestions': performances.fold(0, (sum, p) => sum + p.questionsAnswered),
      'weaknessCount': weaknesses.length,
      'sessionsCompleted': sessions.length,
      'improvementRate': _calculateImprovementRate(performances),
    };
  }

  double _calculateImprovementRate(List<UserPerformance> performances) {
    if (performances.length < 2) return 0.0;
    
    // Sort by last updated to get progression
    performances.sort((a, b) => a.lastUpdated.compareTo(b.lastUpdated));
    
    final first = performances.first.accuracy;
    final last = performances.last.accuracy;
    
    return last - first; // Positive means improvement
  }

  // Recommendation engine
  List<String> getPersonalizedRecommendations(String categoryName) {
    final weaknesses = getWeaknessAreas(categoryName);
    final performance = getUserPerformance(categoryName);
    
    final recommendations = <String>[];
    
    // High priority weaknesses
    final criticalWeaknesses = weaknesses.where((w) => w.isCritical).toList();
    if (criticalWeaknesses.isNotEmpty) {
      recommendations.add(
        'Concentrez-vous sur ${criticalWeaknesses.first.topicName} (${(criticalWeaknesses.first.accuracyRate * 100).round()}% de réussite)'
      );
    }
    
    // Time-based recommendations
    final recentPerformance = performance
        .where((p) => p.lastUpdated.isAfter(DateTime.now().subtract(const Duration(days: 7))))
        .toList();
    
    if (recentPerformance.isEmpty) {
      recommendations.add('Il est temps de réviser ! Vous n\'avez pas pratiqué récemment.');
    }
    
    // Streak encouragement
    final maxStreak = performance.isNotEmpty 
        ? performance.map((p) => p.streakCount).reduce(max)
        : 0;
    
    if (maxStreak >= 5) {
      recommendations.add('Excellent ! Votre meilleure série est de $maxStreak bonnes réponses.');
    }
    
    return recommendations;
  }
}