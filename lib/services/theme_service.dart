import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../theme/app_theme.dart';

enum ThemeType {
  light,
  dark,
  sepia,
  tokyoNight,
  solarizedDark,
  monokaiDimmed,
}

class ThemeService extends ChangeNotifier {
  static const String _themeKey = 'theme_type';
  late final SharedPreferences _prefs;
  ThemeType _currentTheme = ThemeType.light;

  Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
    _currentTheme = ThemeType.values[_prefs.getInt(_themeKey) ?? 0];
    notifyListeners();
  }

  ThemeType get currentTheme => _currentTheme;
  bool get isDarkMode =>
      _currentTheme == ThemeType.dark ||
      _currentTheme == ThemeType.tokyoNight ||
      _currentTheme == ThemeType.solarizedDark ||
      _currentTheme == ThemeType.monokaiDimmed;

  void setTheme(ThemeType theme) {
    _currentTheme = theme;
    _prefs.setInt(_themeKey, theme.index);
    notifyListeners();
  }

  void cycleTheme() {
    final values = ThemeType.values;
    final nextIndex = (_currentTheme.index + 1) % values.length;
    setTheme(values[nextIndex]);
  }

  String getThemeName(ThemeType type) {
    switch (type) {
      case ThemeType.light:
        return 'Thème Clair';
      case ThemeType.dark:
        return 'Thème Sombre';
      case ThemeType.sepia:
        return 'Thème Sépia';
      case ThemeType.tokyoNight:
        return 'Tokyo Night';
      case ThemeType.solarizedDark:
        return 'Solarized Dark';
      case ThemeType.monokaiDimmed:
        return 'Monokai Dimmed';
    }
  }

  ThemeData get theme {
    switch (_currentTheme) {
      case ThemeType.light:
        return AppTheme.getThemeData(AppTheme.getLightColorScheme());
      case ThemeType.dark:
        return AppTheme.getThemeData(AppTheme.getDarkColorScheme());
      case ThemeType.sepia:
        return AppTheme.getThemeData(
          ColorScheme.fromSeed(
            seedColor: const Color(0xFF8B7355),
            brightness: Brightness.light,
            primary: const Color(0xFF8B7355),
            surface: const Color(0xFFF5E6D3),
            background: const Color(0xFFF5E6D3),
            onSurface: const Color(0xFF2C1810),
            surfaceVariant: const Color(0xFFE6D5C1),
            onSurfaceVariant: const Color(0xFF4A3524),
          ),
        );
      case ThemeType.tokyoNight:
        return AppTheme.getThemeData(AppTheme.getDarkColorScheme().copyWith(
          primary: const Color(0xFF7AA2F7),
          surface: const Color(0xFF1A1B26),
          surfaceContainerHighest: const Color(0xFF16161E),
        ));
      case ThemeType.solarizedDark:
        return AppTheme.getThemeData(AppTheme.getDarkColorScheme().copyWith(
          primary: const Color(0xFF859900),
          surface: const Color(0xFF002B36),
          surfaceContainerHighest: const Color(0xFF073642),
          onSurface: const Color(0xFF93A1A1),
        ));
      case ThemeType.monokaiDimmed:
        return AppTheme.getThemeData(AppTheme.getDarkColorScheme().copyWith(
          primary: const Color(0xFFFD971F),
          surface: const Color(0xFF1E1F1C),
          surfaceContainerHighest: const Color(0xFF252621),
          onSurface: const Color(0xFFA6E22E),
        ));
    }
  }
}
