import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../models/quiz_model.dart';

class QuizOptionButton extends StatelessWidget {
  final QuizQuestion question;
  final int index;
  final bool isSelected;
  final bool showResult;
  final VoidCallback onPressed;
  final Color color;

  const QuizOptionButton({
    super.key,
    required this.question,
    required this.index,
    required this.isSelected,
    required this.showResult,
    required this.onPressed,
    required this.color,
  });

  @override
  Widget build(BuildContext context) {
    final option = question.options[index];
    final isCorrect = index == question.correct;
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    // Determine button color based on state
    Color backgroundColor;
    Color borderColor;
    Color textColor;

    if (showResult) {
      if (isCorrect) {
        backgroundColor = Colors.green.shade100;
        borderColor = Colors.green;
        textColor = Colors.green.shade900;
      } else if (isSelected && !isCorrect) {
        backgroundColor = Colors.red.shade100;
        borderColor = Colors.red;
        textColor = Colors.red.shade900;
      } else {
        backgroundColor = colorScheme.surfaceContainerHighest;
        borderColor = colorScheme.outline;
        textColor = colorScheme.onSurfaceVariant;
      }
    } else {
      if (isSelected) {
        backgroundColor = color.withOpacity(0.2);
        borderColor = color;
        textColor = color.withOpacity(0.9);
      } else {
        backgroundColor = colorScheme.surfaceContainerHighest;
        borderColor = colorScheme.outline;
        textColor = colorScheme.onSurfaceVariant;
      }
    }

    return Material(
      color: backgroundColor,
      borderRadius: BorderRadius.circular(16),
      elevation: isSelected ? 2 : 0,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: borderColor,
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              Container(
                width: 36,
                height: 36,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected ? color.withOpacity(0.2) : Colors.white,
                  border: Border.all(
                    color: isSelected ? color : colorScheme.outline,
                    width: 1.5,
                  ),
                ),
                child: Center(
                  child: Text(
                    String.fromCharCode(65 + index), // A, B, C, D...
                    style: textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: isSelected ? color : colorScheme.onSurfaceVariant,
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Text(
                  option,
                  style: textTheme.bodyLarge?.copyWith(
                    color: textColor,
                    fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  ),
                  softWrap: true,
                  overflow: TextOverflow.visible,
                ),
              ),
              if (showResult) ...[
                const SizedBox(width: 8),
                Icon(
                  isCorrect ? Icons.check_circle : Icons.cancel,
                  color: isCorrect ? Colors.green : Colors.red,
                  size: 24,
                ),
              ],
            ],
          ),
        ),
      ),
    ).animate()
        .fadeIn()
        .scale()
        .move(delay: 100.ms * index);
  }
}
