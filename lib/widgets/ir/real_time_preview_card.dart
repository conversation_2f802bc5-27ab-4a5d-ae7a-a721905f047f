import 'package:flutter/material.dart';
import '../../models/salary/salary_result.dart';

class RealTimePreviewCard extends StatelessWidget {
  final ValueNotifier<SalaryResult?> resultNotifier;
  final ValueNotifier<bool> isCalculatingNotifier;
  final ValueNotifier<String?> errorNotifier;
  final bool showDetailedBreakdown;
  final bool isCompactView;

  const RealTimePreviewCard({
    super.key,
    required this.resultNotifier,
    required this.isCalculatingNotifier,
    required this.errorNotifier,
    this.showDetailedBreakdown = false,
    this.isCompactView = false,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Colors.blue.shade600,
              Colors.blue.shade800,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Icon(
                    Icons.calculate,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Résultat en temps réel',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  ValueListenableBuilder<bool>(
                    valueListenable: isCalculatingNotifier,
                    builder: (context, isCalculating, child) {
                      if (isCalculating) {
                        return SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        );
                      }
                      return const SizedBox.shrink();
                    },
                  ),
                ],
              ),
              const SizedBox(height: 16),
              ValueListenableBuilder<String?>(
                valueListenable: errorNotifier,
                builder: (context, error, child) {
                  if (error != null) {
                    return _buildErrorState(context, error);
                  }
                  
                  return ValueListenableBuilder<SalaryResult?>(
                    valueListenable: resultNotifier,
                    builder: (context, result, child) {
                      if (result == null) {
                        return _buildEmptyState(context);
                      }
                      
                      return _buildResultDisplay(context, result);
                    },
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.pending_actions,
            size: 48,
            color: Colors.white70,
          ),
          const SizedBox(height: 12),
          Text(
            'En attente de calcul',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Saisissez les informations pour voir le résultat',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, String error) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade100,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.red.shade300),
      ),
      child: Column(
        children: [
          Icon(
            Icons.error_outline,
            size: 32,
            color: Colors.red.shade600,
          ),
          const SizedBox(height: 8),
          Text(
            'Erreur de calcul',
            style: TextStyle(
              color: Colors.red.shade800,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            error,
            style: TextStyle(
              color: Colors.red.shade700,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildResultDisplay(BuildContext context, SalaryResult result) {
    if (isCompactView) {
      return _buildCompactLayout(context, result);
    } else {
      return _buildDetailedLayout(context, result);
    }
  }

  Widget _buildCompactLayout(BuildContext context, SalaryResult result) {
    return Column(
      children: [
        _buildMainAmountCard(
          'Salaire Net',
          result.netSalary,
          Icons.account_balance_wallet,
          Colors.green.shade100,
          Colors.green.shade800,
          isLarge: true,
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildSmallAmountCard(
                'Brut',
                result.grossSalary,
                Icons.trending_up,
                Colors.blue.shade100,
                Colors.blue.shade800,
              ),
            ),
            const SizedBox(width: 8),
            Expanded(
              child: _buildSmallAmountCard(
                'IR',
                result.irResult.finalTax,
                Icons.receipt,
                Colors.orange.shade100,
                Colors.orange.shade800,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDetailedLayout(BuildContext context, SalaryResult result) {
    return Column(
      children: [
        _buildMainAmountCard(
          'Salaire Net à Payer',
          result.netSalary,
          Icons.account_balance_wallet,
          Colors.green.shade100,
          Colors.green.shade800,
          isLarge: true,
        ),
        const SizedBox(height: 16),
        LayoutBuilder(
          builder: (context, constraints) {
            final isWide = constraints.maxWidth > 400;
            
            if (isWide) {
              return _buildTwoColumnGrid(result);
            } else {
              return _buildSingleColumnList(result);
            }
          },
        ),
        if (showDetailedBreakdown) ...[
          const SizedBox(height: 16),
          _buildDetailedBreakdown(context, result),
        ],
      ],
    );
  }

  Widget _buildTwoColumnGrid(SalaryResult result) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildAmountCard(
                'Salaire Brut',
                result.grossSalary,
                Icons.trending_up,
                Colors.blue.shade100,
                Colors.blue.shade800,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildAmountCard(
                'Cotisations Sociales',
                result.socialContributions.total,
                Icons.security,
                Colors.purple.shade100,
                Colors.purple.shade800,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildAmountCard(
                'Revenu Imposable',
                result.irResult.taxableIncome,
                Icons.account_balance,
                Colors.indigo.shade100,
                Colors.indigo.shade800,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildAmountCard(
                'Impôt sur le Revenu',
                result.irResult.finalTax,
                Icons.receipt_long,
                Colors.orange.shade100,
                Colors.orange.shade800,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSingleColumnList(SalaryResult result) {
    return Column(
      children: [
        _buildAmountCard(
          'Salaire Brut',
          result.grossSalary,
          Icons.trending_up,
          Colors.blue.shade100,
          Colors.blue.shade800,
        ),
        const SizedBox(height: 8),
        _buildAmountCard(
          'Cotisations Sociales',
          result.socialContributions.total,
          Icons.security,
          Colors.purple.shade100,
          Colors.purple.shade800,
        ),
        const SizedBox(height: 8),
        _buildAmountCard(
          'Revenu Imposable',
          result.irResult.taxableIncome,
          Icons.account_balance,
          Colors.indigo.shade100,
          Colors.indigo.shade800,
        ),
        const SizedBox(height: 8),
        _buildAmountCard(
          'Impôt sur le Revenu',
          result.irResult.finalTax,
          Icons.receipt_long,
          Colors.orange.shade100,
          Colors.orange.shade800,
        ),
      ],
    );
  }

  Widget _buildMainAmountCard(
    String label,
    double amount,
    IconData icon,
    Color bgColor,
    Color textColor, {
    bool isLarge = false,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: textColor.withOpacity(0.3), width: 2),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: textColor,
            size: isLarge ? 28 : 24,
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.w600,
              fontSize: isLarge ? 16 : 14,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            '${_formatAmount(amount)} DH',
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.bold,
              fontSize: isLarge ? 24 : 20,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAmountCard(
    String label,
    double amount,
    IconData icon,
    Color bgColor,
    Color textColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: textColor.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: textColor,
            size: 20,
          ),
          const SizedBox(height: 6),
          Text(
            label,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.w600,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            '${_formatAmount(amount)} DH',
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.bold,
              fontSize: 16,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSmallAmountCard(
    String label,
    double amount,
    IconData icon,
    Color bgColor,
    Color textColor,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: bgColor,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: textColor.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: textColor,
            size: 16,
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.w600,
              fontSize: 10,
            ),
          ),
          Text(
            '${_formatAmount(amount)} DH',
            style: TextStyle(
              color: textColor,
              fontWeight: FontWeight.bold,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedBreakdown(BuildContext context, SalaryResult result) {
    return ExpansionTile(
      title: Text(
        'Détail du calcul',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w600,
        ),
      ),
      iconColor: Colors.white,
      collapsedIconColor: Colors.white70,
      children: [
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            result.generateDetails(),
            style: TextStyle(
              fontSize: 12,
              fontFamily: 'monospace',
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }

  String _formatAmount(double amount) {
    return amount.toStringAsFixed(2).replaceAll(RegExp(r'\.00$'), '');
  }
}