import 'package:flutter/material.dart';
import '../../utils/input_validators.dart';
import '../../models/salary/salary_data.dart';

class BonusesCard extends StatelessWidget {
  final Map<String, (TextEditingController, bool, bool)> bonuses;
  final Function(String, bool) onBonusToggled;
  final VoidCallback? onFieldChanged;
  final bool isVisible;
  final Function(String) onRemoveBonus;
  final VoidCallback onAddBonus;

  const BonusesCard({
    super.key,
    required this.bonuses,
    required this.onBonusToggled,
    this.onFieldChanged,
    this.isVisible = true,
    required this.onRemoveBonus,
    required this.onAddBonus,
  });

  @override
  Widget build(BuildContext context) {
    if (!isVisible) {
      return const SizedBox.shrink();
    }

    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.purple.shade600,
                    Colors.purple.shade400,
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.stars,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Primes et bonus',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    height: 32,
                    width: 32,
                    child: IconButton(
                      onPressed: onAddBonus,
                      icon: Icon(
                        Icons.add,
                        color: Colors.white,
                        size: 16,
                      ),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(
                        minHeight: 32,
                        minWidth: 32,
                      ),
                      tooltip: 'Ajouter une prime',
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            if (bonuses.isEmpty)
              _buildEmptyState(context)
            else
              LayoutBuilder(
                builder: (context, constraints) {
                  final isWide = constraints.maxWidth > 600;
                  
                  if (isWide) {
                    return _buildGridLayout();
                  } else {
                    return _buildListLayout();
                  }
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Icon(
            Icons.add_circle_outline,
            size: 48,
            color: Colors.grey.shade400,
          ),
          const SizedBox(height: 12),
          Text(
            'Aucune prime configurée',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Appuyez sur + pour ajouter une prime',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: Colors.grey.shade500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridLayout() {
    return Wrap(
      spacing: 16,
      runSpacing: 16,
      children: bonuses.entries.map((entry) {
        return SizedBox(
          width: 280,
          child: _buildBonusCard(entry.key, entry.value),
        );
      }).toList(),
    );
  }

  Widget _buildListLayout() {
    return Column(
      children: bonuses.entries.map((entry) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 12),
          child: _buildBonusCard(entry.key, entry.value),
        );
      }).toList(),
    );
  }

  Widget _buildBonusCard(String bonusName, (TextEditingController, bool, bool) bonusData) {
    final (controller, isEnabled, isAnnual) = bonusData;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(
          color: isEnabled ? Colors.purple.shade300 : Colors.grey.shade300,
          width: isEnabled ? 2 : 1,
        ),
        borderRadius: BorderRadius.circular(8),
        color: isEnabled ? Colors.purple.shade50 : Colors.grey.shade50,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                height: 24,
                width: 24,
                child: Checkbox(
                  value: isEnabled,
                  onChanged: (value) {
                    onBonusToggled(bonusName, value ?? false);
                    onFieldChanged?.call();
                  },
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  bonusName,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                    color: isEnabled ? Colors.purple.shade700 : Colors.grey.shade600,
                  ),
                ),
              ),
              if (_isCustomBonus(bonusName))
                Container(
                  height: 32,
                  width: 32,
                  child: IconButton(
                    onPressed: () => onRemoveBonus(bonusName),
                    icon: Icon(
                      Icons.close,
                      size: 16,
                      color: Colors.grey.shade600,
                    ),
                    padding: EdgeInsets.zero,
                    constraints: BoxConstraints(
                      minHeight: 32,
                      minWidth: 32,
                    ),
                    tooltip: 'Supprimer',
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Container(
            height: 48, // Minimum touch target
            child: TextFormField(
              controller: controller,
              enabled: isEnabled,
              keyboardType: TextInputType.number,
              inputFormatters: InputValidators.getMonetaryInputFormatters(),
              decoration: InputDecoration(
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(6),
                ),
                hintText: '0.00',
                suffixText: 'DH',
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 8,
                ),
                filled: true,
                fillColor: isEnabled ? Colors.white : Colors.grey.shade100,
              ),
              onChanged: (_) => onFieldChanged?.call(),
              validator: (value) {
                if (isEnabled && value != null && value.isNotEmpty) {
                  final amount = double.tryParse(value.replaceAll(' ', ''));
                  if (amount == null || amount < 0) {
                    return 'Invalide';
                  }
                }
                return null;
              },
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(
                isAnnual ? Icons.calendar_today : Icons.today,
                size: 16,
                color: isEnabled ? Colors.purple.shade600 : Colors.grey.shade500,
              ),
              const SizedBox(width: 4),
              Text(
                isAnnual ? 'Annuel' : 'Mensuel',
                style: TextStyle(
                  fontSize: 12,
                  color: isEnabled ? Colors.purple.shade600 : Colors.grey.shade500,
                  fontWeight: FontWeight.w500,
                ),
              ),
              if (isAnnual && isEnabled && controller.text.isNotEmpty) ...[
                const SizedBox(width: 8),
                Text(
                  '(${_getMonthlyEquivalent(controller.text)} DH/mois)',
                  style: TextStyle(
                    fontSize: 11,
                    color: Colors.grey.shade600,
                    fontStyle: FontStyle.italic,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  bool _isCustomBonus(String bonusName) {
    const predefinedBonuses = [
      'Prime de rendement',
      'Prime d\'ancienneté',
      'Prime de risque',
      'Prime de responsabilité',
      '13ème mois',
      'Prime de fin d\'année',
    ];
    return !predefinedBonuses.contains(bonusName);
  }

  String _getMonthlyEquivalent(String annualAmount) {
    final amount = double.tryParse(annualAmount.replaceAll(' ', ''));
    if (amount == null) return '0.00';
    return (amount / 12).toStringAsFixed(2);
  }
}