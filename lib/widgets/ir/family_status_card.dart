import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/salary/salary_data.dart';

class FamilyStatusCard extends StatelessWidget {
  final bool isMarried;
  final Function(bool) onMarriedChanged;
  final List<(TextEditingController, bool)> dependents;
  final VoidCallback onAddDependent;
  final Function(int) onRemoveDependent;
  final Function(int, bool) onDependentDisabilityChanged;
  final VoidCallback? onFieldChanged;

  const FamilyStatusCard({
    super.key,
    required this.isMarried,
    required this.onMarriedChanged,
    required this.dependents,
    required this.onAddDependent,
    required this.onRemoveDependent,
    required this.onDependentDisabilityChanged,
    this.onFieldChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    Colors.teal.shade600,
                    Colors.teal.shade400,
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.family_restroom,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Situation familiale',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    height: 32,
                    width: 32,
                    child: IconButton(
                      onPressed: onAddDependent,
                      icon: Icon(
                        Icons.person_add,
                        color: Colors.white,
                        size: 16,
                      ),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(
                        minHeight: 32,
                        minWidth: 32,
                      ),
                      tooltip: 'Ajouter une personne à charge',
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
            _buildMaritalStatusToggle(context),
            const SizedBox(height: 16),
            if (dependents.isEmpty)
              _buildNoDependentsState(context)
            else
              _buildDependentsList(context),
            const SizedBox(height: 12),
            _buildEligibilityInfo(context),
          ],
        ),
      ),
    );
  }

  Widget _buildMaritalStatusToggle(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.teal.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.teal.shade50,
      ),
      child: Row(
        children: [
          Icon(
            isMarried ? Icons.favorite : Icons.favorite_border,
            color: Colors.teal.shade600,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              'Situation matrimoniale',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                fontSize: 15,
                color: Colors.teal.shade700,
              ),
            ),
          ),
          Container(
            height: 48, // Minimum touch target
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isMarried ? 'Marié(e)' : 'Célibataire',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    color: Colors.teal.shade700,
                  ),
                ),
                const SizedBox(width: 8),
                Transform.scale(
                  scale: 1.2, // Make switch larger for better touch target
                  child: Switch(
                    value: isMarried,
                    onChanged: (value) {
                      onMarriedChanged(value);
                      onFieldChanged?.call();
                    },
                    activeColor: Colors.teal.shade600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNoDependentsState(BuildContext context) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey.shade300),
        borderRadius: BorderRadius.circular(8),
        color: Colors.grey.shade50,
      ),
      child: Column(
        children: [
          Icon(
            Icons.person_add_outlined,
            size: 32,
            color: Colors.grey.shade500,
          ),
          const SizedBox(height: 8),
          Text(
            'Aucune personne à charge',
            style: TextStyle(
              color: Colors.grey.shade600,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            'Appuyez sur + pour ajouter',
            style: TextStyle(
              color: Colors.grey.shade500,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDependentsList(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Personnes à charge (${dependents.length})',
          style: TextStyle(
            fontWeight: FontWeight.w600,
            fontSize: 14,
            color: Colors.teal.shade700,
          ),
        ),
        const SizedBox(height: 8),
        LayoutBuilder(
          builder: (context, constraints) {
            final isWide = constraints.maxWidth > 600;
            
            if (isWide) {
              return _buildDependentsGrid();
            } else {
              return _buildDependentsList();
            }
          },
        ),
      ],
    );
  }

  Widget _buildDependentsGrid() {
    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children: dependents.asMap().entries.map((entry) {
        final index = entry.key;
        final (controller, isDisabled) = entry.value;
        return SizedBox(
          width: 250,
          child: _buildDependentCard(index, controller, isDisabled),
        );
      }).toList(),
    );
  }

  Widget _buildDependentsList() {
    return Column(
      children: dependents.asMap().entries.map((entry) {
        final index = entry.key;
        final (controller, isDisabled) = entry.value;
        return Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: _buildDependentCard(index, controller, isDisabled),
        );
      }).toList(),
    );
  }

  Widget _buildDependentCard(int index, TextEditingController controller, bool isDisabled) {
    final age = int.tryParse(controller.text) ?? 0;
    final isEligible = isDisabled || age < 27;
    
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        border: Border.all(
          color: isEligible ? Colors.teal.shade300 : Colors.orange.shade300,
          width: 1.5,
        ),
        borderRadius: BorderRadius.circular(8),
        color: isEligible ? Colors.teal.shade50 : Colors.orange.shade50,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.person,
                size: 16,
                color: isEligible ? Colors.teal.shade600 : Colors.orange.shade600,
              ),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  'Personne ${index + 1}',
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 13,
                    color: isEligible ? Colors.teal.shade700 : Colors.orange.shade700,
                  ),
                ),
              ),
              Container(
                height: 32,
                width: 32,
                child: IconButton(
                  onPressed: () => onRemoveDependent(index),
                  icon: Icon(
                    Icons.close,
                    size: 14,
                    color: Colors.grey.shade600,
                  ),
                  padding: EdgeInsets.zero,
                  constraints: BoxConstraints(
                    minHeight: 32,
                    minWidth: 32,
                  ),
                  tooltip: 'Supprimer',
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: Container(
                  height: 40,
                  child: TextFormField(
                    controller: controller,
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      FilteringTextInputFormatter.digitsOnly,
                      LengthLimitingTextInputFormatter(2),
                    ],
                    decoration: InputDecoration(
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(6),
                      ),
                      hintText: 'Âge',
                      contentPadding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 8,
                      ),
                      isDense: true,
                    ),
                    onChanged: (_) => onFieldChanged?.call(),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Requis';
                      }
                      final age = int.tryParse(value);
                      if (age == null || age < 0 || age > 99) {
                        return 'Invalide';
                      }
                      return null;
                    },
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                height: 40,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Container(
                      height: 20,
                      width: 20,
                      child: Checkbox(
                        value: isDisabled,
                        onChanged: (value) {
                          onDependentDisabilityChanged(index, value ?? false);
                          onFieldChanged?.call();
                        },
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(3),
                        ),
                      ),
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Handicap',
                      style: TextStyle(
                        fontSize: 11,
                        color: Colors.grey.shade700,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: isEligible ? Colors.teal.shade100 : Colors.orange.shade100,
              borderRadius: BorderRadius.circular(10),
            ),
            child: Text(
              isEligible ? 'Éligible' : 'Non éligible',
              style: TextStyle(
                fontSize: 10,
                color: isEligible ? Colors.teal.shade700 : Colors.orange.shade700,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEligibilityInfo(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(6),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: 16,
            color: Colors.blue.shade600,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              'Éligible: âge < 27 ans ou personne handicapée',
              style: TextStyle(
                fontSize: 12,
                color: Colors.blue.shade700,
              ),
            ),
          ),
        ],
      ),
    );
  }
}