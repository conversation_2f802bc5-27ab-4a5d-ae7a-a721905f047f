import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/theme_service.dart';
import 'package:intl/intl.dart';

class CPCWidget extends StatelessWidget {
  final List<Map<String, dynamic>> produits;
  final List<Map<String, dynamic>> charges;
  final String title;
  final DateTime date;

  const CPCWidget({
    super.key,
    required this.produits,
    required this.charges,
    required this.title,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return SingleChildScrollView(
      child: Container(
        decoration: BoxDecoration(
          color: isDark ? colorScheme.surface.withOpacity(0.8) : colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: colorScheme.outline.withOpacity(isDark ? 0.1 : 0.2),
          ),
        ),
        child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: colorScheme.primary.withOpacity(0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(11),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.analytics,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                      ),
                      Text(
                        'Au ${DateFormat('dd/MM/yyyy').format(date)}',
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.primary.withOpacity(0.8),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: IntrinsicWidth(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSection(context, 'PRODUITS', produits),
                    const SizedBox(height: 24),
                    _buildSection(context, 'CHARGES', charges),
                    const SizedBox(height: 24),
                    _buildResultatNet(context),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    )
    )
    ;
  }

  Widget _buildSection(
    BuildContext context,
    String title,
    List<Map<String, dynamic>> items,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.2),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
            decoration: BoxDecoration(
              color: title == 'PRODUITS' 
                ? colorScheme.primary.withOpacity(0.1)
                : colorScheme.error.withOpacity(0.1),
              borderRadius: const BorderRadius.vertical(top: Radius.circular(15)),
            ),
            child: Row(
              children: [
                Icon(
                  title == 'PRODUITS' 
                    ? Icons.trending_up_rounded
                    : Icons.trending_down_rounded,
                  color: title == 'PRODUITS' 
                    ? colorScheme.primary
                    : colorScheme.error,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: title == 'PRODUITS' 
                      ? colorScheme.primary
                      : colorScheme.error,
                    letterSpacing: 0.5,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: (title == 'PRODUITS' 
                      ? colorScheme.primary
                      : colorScheme.error).withOpacity(0.1),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: (title == 'PRODUITS' 
                        ? colorScheme.primary
                        : colorScheme.error).withOpacity(0.2),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    '${NumberFormat('#,##0.00', 'fr_FR').format(_calculateTotal(items))} DH',
                    style: textTheme.labelMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: title == 'PRODUITS' 
                        ? colorScheme.primary
                        : colorScheme.error,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Table(
            defaultVerticalAlignment: TableCellVerticalAlignment.middle,
            columnWidths: const {
              0: FixedColumnWidth(240),
              1: FixedColumnWidth(160),
            },
            children: [
              TableRow(
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest.withOpacity(isDark ? 0.2 : 0.3),
                  border: Border(
                    bottom: BorderSide(
                      color: colorScheme.outline.withOpacity(0.1),
                      width: 1,
                    ),
                  ),
                ),
                children: [
                  _buildHeaderCell(context, 'Rubrique'),
                  _buildHeaderCell(context, 'Montant', isNumeric: true),
                ],
              ),
              ...items.map((item) {
                final isPositive = item['montant'] >= 0;
                return TableRow(
                  decoration: BoxDecoration(
                    border: Border(
                      bottom: BorderSide(
                        color: colorScheme.outline.withOpacity(0.05),
                        width: 1,
                      ),
                    ),
                  ),
                  children: [
                    _buildCell(context, item['rubrique']),
                    _buildCell(
                      context,
                      '${NumberFormat('#,##0.00', 'fr_FR').format(item['montant'].abs())} DH',
                      isNumeric: true,
                      isPositive: isPositive,
                    ),
                  ],
                );
              }),
              TableRow(
                decoration: BoxDecoration(
                  color: colorScheme.surfaceContainerHighest.withOpacity(isDark ? 0.1 : 0.2),
                ),
                children: [
                  _buildCell(
                    context,
                    'Total $title',
                    isBold: true,
                  ),
                  _buildCell(
                    context,
                    '${NumberFormat('#,##0.00', 'fr_FR').format(_calculateTotal(items))} DH',
                    isNumeric: true,
                    isBold: true,
                    highlight: true,
                    isPositive: title == 'PRODUITS',
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildResultatNet(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    final totalProduits = _calculateTotal(produits);
    final totalCharges = _calculateTotal(charges);
    final resultatNet = totalProduits - totalCharges;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      decoration: BoxDecoration(
        color: colorScheme.primary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'Résultat Net',
            style: textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
            ),
          ),
          Text(
            '${resultatNet.toStringAsFixed(2)} DH',
            style: textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: resultatNet >= 0 ? colorScheme.primary : colorScheme.error,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(BuildContext context, String text,
      {bool isNumeric = false}) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
              letterSpacing: 0.5,
            ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  Widget _buildCell(BuildContext context, String text,
      {bool isNumeric = false, bool isBold = false, bool highlight = false, bool isPositive = true}) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    Color textColor = colorScheme.onSurface;
    if (highlight) {
      textColor = isPositive ? colorScheme.primary : colorScheme.error;
    } else if (isNumeric) {
      textColor = isPositive ? colorScheme.onSurface : colorScheme.error;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Text(
        text,
        style: textTheme.bodyMedium?.copyWith(
          color: textColor,
          fontWeight: isBold || highlight ? FontWeight.bold : null,
          letterSpacing: highlight ? 0.5 : null,
        ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  double _calculateTotal(List<Map<String, dynamic>> items) {
    return items.fold(0.0, (sum, item) => sum + (item['montant'] as double));
  }
}
