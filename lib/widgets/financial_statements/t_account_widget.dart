import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/theme_service.dart';

class TAccountEntry {
  final String date;
  final String description;
  final double amount;

  const TAccountEntry({
    required this.date,
    required this.description,
    required this.amount,
  });
}

class TAccountWidget extends StatelessWidget {
  final String accountNumber;
  final String accountName;
  final List<TAccountEntry> debitEntries;
  final List<TAccountEntry> creditEntries;

  const TAccountWidget({
    super.key,
    required this.accountNumber,
    required this.accountName,
    required this.debitEntries,
    required this.creditEntries,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Container(
      decoration: BoxDecoration(
        color:
            isDark ? colorScheme.surface.withOpacity(0.8) : colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withOpacity(isDark ? 0.1 : 0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest.withOpacity(isDark ? 0.3 : 0.5),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(11),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.account_balance_wallet,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '$accountNumber $accountName',
                        style: textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: IntrinsicWidth(
                child: Column(
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: BoxDecoration(
                              border: Border(
                                right: BorderSide(
                                  color: colorScheme.outline
                                      .withOpacity(isDark ? 0.1 : 0.2),
                                ),
                              ),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.stretch,
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      vertical: 8, horizontal: 12),
                                  decoration: BoxDecoration(
                                    color: colorScheme.surfaceContainerHighest
                                        .withOpacity(isDark ? 0.2 : 0.4),
                                    borderRadius: const BorderRadius.only(
                                      topLeft: Radius.circular(8),
                                    ),
                                  ),
                                  child: Text(
                                    'DÉBIT',
                                    style: textTheme.titleSmall?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: colorScheme.primary,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                                Table(
                                  defaultVerticalAlignment:
                                      TableCellVerticalAlignment.middle,
                                  columnWidths: const {
                                    0: FixedColumnWidth(100), // Date
                                    1: FixedColumnWidth(200), // Description
                                    2: FixedColumnWidth(120), // Montant
                                  },
                                  children: [
                                    TableRow(
                                      decoration: BoxDecoration(
                                        color: colorScheme.surfaceContainerHighest
                                            .withOpacity(isDark ? 0.1 : 0.3),
                                      ),
                                      children: [
                                        _buildHeaderCell(context, 'Date'),
                                        _buildHeaderCell(
                                            context, 'Description'),
                                        _buildHeaderCell(context, 'Montant',
                                            isNumeric: true),
                                      ],
                                    ),
                                    ...debitEntries.map((entry) {
                                      return TableRow(
                                        children: [
                                          _buildCell(context, entry.date),
                                          _buildCell(
                                              context, entry.description),
                                          _buildCell(
                                            context,
                                            '${entry.amount.toStringAsFixed(2)} DH',
                                            isNumeric: true,
                                          ),
                                        ],
                                      );
                                    }),
                                    TableRow(
                                      decoration: BoxDecoration(
                                        color: colorScheme.surfaceContainerHighest
                                            .withOpacity(isDark ? 0.1 : 0.2),
                                      ),
                                      children: [
                                        _buildCell(context, '', isBold: true),
                                        _buildCell(context, 'Total Débit',
                                            isBold: true),
                                        _buildCell(
                                          context,
                                          '${_calculateTotalDebit().toStringAsFixed(2)} DH',
                                          isNumeric: true,
                                          isBold: true,
                                        ),
                                      ],
                                    ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                        ),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.stretch,
                            children: [
                              Container(
                                padding: const EdgeInsets.symmetric(
                                    vertical: 8, horizontal: 12),
                                decoration: BoxDecoration(
                                  color: colorScheme.surfaceContainerHighest
                                      .withOpacity(isDark ? 0.2 : 0.4),
                                  borderRadius: const BorderRadius.only(
                                    topRight: Radius.circular(8),
                                  ),
                                ),
                                child: Text(
                                  'CRÉDIT',
                                  style: textTheme.titleSmall?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: colorScheme.primary,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                              Table(
                                defaultVerticalAlignment:
                                    TableCellVerticalAlignment.middle,
                                columnWidths: const {
                                  0: FixedColumnWidth(100), // Date
                                  1: FixedColumnWidth(200), // Description
                                  2: FixedColumnWidth(120), // Montant
                                },
                                children: [
                                  TableRow(
                                    decoration: BoxDecoration(
                                      color: colorScheme.surfaceContainerHighest
                                          .withOpacity(isDark ? 0.1 : 0.3),
                                    ),
                                    children: [
                                      _buildHeaderCell(context, 'Date'),
                                      _buildHeaderCell(context, 'Description'),
                                      _buildHeaderCell(context, 'Montant',
                                          isNumeric: true),
                                    ],
                                  ),
                                  ...creditEntries.map((entry) {
                                    return TableRow(
                                      children: [
                                        _buildCell(context, entry.date),
                                        _buildCell(context, entry.description),
                                        _buildCell(
                                          context,
                                          '${entry.amount.toStringAsFixed(2)} DH',
                                          isNumeric: true,
                                        ),
                                      ],
                                    );
                                  }),
                                  TableRow(
                                    decoration: BoxDecoration(
                                      color: colorScheme.surfaceContainerHighest
                                          .withOpacity(isDark ? 0.1 : 0.2),
                                    ),
                                    children: [
                                      _buildCell(context, '', isBold: true),
                                      _buildCell(context, 'Total Crédit',
                                          isBold: true),
                                      _buildCell(
                                        context,
                                        '${_calculateTotalCredit().toStringAsFixed(2)} DH',
                                        isNumeric: true,
                                        isBold: true,
                                      ),
                                    ],
                                  ),
                                ],
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                          vertical: 12, horizontal: 16),
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withOpacity(0.1),
                        borderRadius: const BorderRadius.vertical(
                          bottom: Radius.circular(11),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Solde',
                            style: textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.primary,
                            ),
                          ),
                          Text(
                            '${_calculateSolde().abs().toStringAsFixed(2)} DH ${_calculateSolde() >= 0 ? 'Débiteur' : 'Créditeur'}',
                            style: textTheme.titleSmall?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: _calculateSolde() >= 0
                                  ? colorScheme.primary
                                  : colorScheme.error,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHeaderCell(BuildContext context, String text,
      {bool isNumeric = false}) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.primary,
            ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  Widget _buildCell(
    BuildContext context,
    String text, {
    bool isNumeric = false,
    bool isBold = false,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: isBold ? FontWeight.bold : null,
            ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  double _calculateTotalDebit() {
    return debitEntries.fold(0.0, (sum, entry) => sum + entry.amount);
  }

  double _calculateTotalCredit() {
    return creditEntries.fold(0.0, (sum, entry) => sum + entry.amount);
  }

  double _calculateSolde() {
    return _calculateTotalDebit() - _calculateTotalCredit();
  }
}
