import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/theme_service.dart';
import 'package:intl/intl.dart';

class BalanceWidget extends StatelessWidget {
  final List<Map<String, dynamic>> comptes;
  final String title;
  final DateTime date;

  const BalanceWidget({
    super.key,
    required this.comptes,
    required this.title,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    final totalMvtDebit = _calculateTotalMouvementsDebit();
    final totalMvtCredit = _calculateTotalMouvementsCredit();
    final totalSoldeDebit = _calculateTotalSoldeDebit();
    final totalSoldeCredit = _calculateTotalSoldeCredit();

    return Container(
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: colorScheme.outline.withOpacity(isDark ? 0.1 : 0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.primary.withOpacity(0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(15),
              ),
              border: Border(
                bottom: BorderSide(
                  color: colorScheme.outline.withOpacity(0.1),
                ),
              ),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.balance,
                        color: colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            title,
                            style: textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.primary,
                              letterSpacing: 0.5,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Au ${DateFormat('dd/MM/yyyy').format(date)}',
                            style: textTheme.bodySmall?.copyWith(
                              color: colorScheme.primary.withOpacity(0.8),
                              letterSpacing: 0.3,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    _buildSummaryCard(
                      context,
                      'Mouvements',
                      totalMvtDebit,
                      totalMvtCredit,
                      Icons.swap_horiz_rounded,
                    ),
                    const SizedBox(width: 16),
                    _buildSummaryCard(
                      context,
                      'Soldes',
                      totalSoldeDebit,
                      totalSoldeCredit,
                      Icons.account_balance_wallet_rounded,
                    ),
                  ],
                ),
              ],
            ),
          ),
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: colorScheme.outline.withOpacity(0.1),
                      ),
                    ),
                    child: Table(
                      defaultVerticalAlignment: TableCellVerticalAlignment.middle,
                      columnWidths: const {
                        0: FixedColumnWidth(100), // N° Compte
                        1: FixedColumnWidth(240), // Intitulé
                        2: FixedColumnWidth(160), // Mvt Débit
                        3: FixedColumnWidth(160), // Mvt Crédit
                        4: FixedColumnWidth(160), // Solde Débit
                        5: FixedColumnWidth(160), // Solde Crédit
                      },
                      children: [
                        TableRow(
                          decoration: BoxDecoration(
                            color: colorScheme.surfaceContainerHighest.withOpacity(isDark ? 0.2 : 0.3),
                            border: Border(
                              bottom: BorderSide(
                                color: colorScheme.outline.withOpacity(0.1),
                                width: 1,
                              ),
                            ),
                          ),
                          children: [
                            _buildHeaderCell(context, 'N° Compte'),
                            _buildHeaderCell(context, 'Intitulé'),
                            _buildHeaderCell(context, 'Mvt Débit', isNumeric: true),
                            _buildHeaderCell(context, 'Mvt Crédit', isNumeric: true),
                            _buildHeaderCell(context, 'Solde Débit', isNumeric: true),
                            _buildHeaderCell(context, 'Solde Crédit', isNumeric: true),
                          ],
                        ),
                        ...comptes.map((compte) {
                          return TableRow(
                            decoration: BoxDecoration(
                              border: Border(
                                bottom: BorderSide(
                                  color: colorScheme.outline.withOpacity(0.05),
                                  width: 1,
                                ),
                              ),
                            ),
                            children: [
                              _buildCell(
                                context, 
                                compte['numero'],
                                isAccountNumber: true,
                              ),
                              _buildCell(context, compte['intitule']),
                              _buildCell(
                                context,
                                '${NumberFormat('#,##0.00', 'fr_FR').format(compte['mouvements_debit'])} DH',
                                isNumeric: true,
                                isDebit: true,
                              ),
                              _buildCell(
                                context,
                                '${NumberFormat('#,##0.00', 'fr_FR').format(compte['mouvements_credit'])} DH',
                                isNumeric: true,
                                isDebit: false,
                              ),
                              _buildCell(
                                context,
                                '${NumberFormat('#,##0.00', 'fr_FR').format(compte['solde_debit'])} DH',
                                isNumeric: true,
                                isDebit: true,
                              ),
                              _buildCell(
                                context,
                                '${NumberFormat('#,##0.00', 'fr_FR').format(compte['solde_credit'])} DH',
                                isNumeric: true,
                                isDebit: false,
                              ),
                            ],
                          );
                        }),
                        TableRow(
                          decoration: BoxDecoration(
                            color: colorScheme.surfaceContainerHighest.withOpacity(isDark ? 0.1 : 0.2),
                          ),
                          children: [
                            _buildCell(context, '', isBold: true),
                            _buildCell(context, 'Totaux', isBold: true),
                            _buildCell(
                              context,
                              '${NumberFormat('#,##0.00', 'fr_FR').format(totalMvtDebit)} DH',
                              isNumeric: true,
                              isBold: true,
                              highlight: true,
                              isDebit: true,
                            ),
                            _buildCell(
                              context,
                              '${NumberFormat('#,##0.00', 'fr_FR').format(totalMvtCredit)} DH',
                              isNumeric: true,
                              isBold: true,
                              highlight: true,
                              isDebit: false,
                            ),
                            _buildCell(
                              context,
                              '${NumberFormat('#,##0.00', 'fr_FR').format(totalSoldeDebit)} DH',
                              isNumeric: true,
                              isBold: true,
                              highlight: true,
                              isDebit: true,
                            ),
                            _buildCell(
                              context,
                              '${NumberFormat('#,##0.00', 'fr_FR').format(totalSoldeCredit)} DH',
                              isNumeric: true,
                              isBold: true,
                              highlight: true,
                              isDebit: false,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(
    BuildContext context,
    String title,
    double debitAmount,
    double creditAmount,
    IconData icon,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Expanded(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: colorScheme.surface,
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: colorScheme.outline.withOpacity(0.1),
          ),
          boxShadow: [
            BoxShadow(
              color: colorScheme.shadow.withOpacity(0.05),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  icon,
                  color: colorScheme.primary,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  title,
                  style: textTheme.titleSmall?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Débit',
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${NumberFormat('#,##0.00', 'fr_FR').format(debitAmount)} DH',
                        style: textTheme.titleSmall?.copyWith(
                          color: colorScheme.primary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  height: 24,
                  width: 1,
                  color: colorScheme.outline.withOpacity(0.1),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        'Crédit',
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        '${NumberFormat('#,##0.00', 'fr_FR').format(creditAmount)} DH',
                        style: textTheme.titleSmall?.copyWith(
                          color: colorScheme.error,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeaderCell(BuildContext context, String text,
      {bool isNumeric = false}) {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Text(
        text,
        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
              letterSpacing: 0.5,
            ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  Widget _buildCell(BuildContext context, String text,
      {bool isNumeric = false, bool isBold = false, bool highlight = false, bool isDebit = true, bool isAccountNumber = false}) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    Color textColor = colorScheme.onSurface;
    if (highlight) {
      textColor = isDebit ? colorScheme.primary : colorScheme.error;
    } else if (isNumeric && text.trim().isNotEmpty) {
      textColor = isDebit ? colorScheme.primary.withOpacity(0.8) : colorScheme.error.withOpacity(0.8);
    } else if (isAccountNumber) {
      textColor = colorScheme.primary;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
      child: Text(
        text,
        style: (isAccountNumber ? textTheme.titleSmall : textTheme.bodyMedium)?.copyWith(
          color: textColor,
          fontWeight: isBold || highlight || isAccountNumber ? FontWeight.bold : null,
          letterSpacing: highlight || isAccountNumber ? 0.5 : null,
        ),
        textAlign: isNumeric ? TextAlign.right : TextAlign.left,
      ),
    );
  }

  double _calculateTotalMouvementsDebit() {
    return comptes.fold(
        0.0, (sum, compte) => sum + (compte['mouvements_debit'] as double));
  }

  double _calculateTotalMouvementsCredit() {
    return comptes.fold(
        0.0, (sum, compte) => sum + (compte['mouvements_credit'] as double));
  }

  double _calculateTotalSoldeDebit() {
    return comptes.fold(
        0.0, (sum, compte) => sum + (compte['solde_debit'] as double));
  }

  double _calculateTotalSoldeCredit() {
    return comptes.fold(
        0.0, (sum, compte) => sum + (compte['solde_credit'] as double));
  }
}
