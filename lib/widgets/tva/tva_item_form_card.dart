import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/tva/tva_line_item.dart';
import '../../services/tva_service.dart';
import '../../utils/input_validators.dart';
import '../../utils/calculation_utils.dart';
import 'tva_exemption_badge.dart';

class TvaItemFormCard extends StatefulWidget {
  final TvaLineItem? initialItem;
  final ValueChanged<TvaLineItem> onChanged;
  final VoidCallback? onDelete;
  final bool showDeleteButton;
  final bool autoFocus;

  const TvaItemFormCard({
    super.key,
    this.initialItem,
    required this.onChanged,
    this.onDelete,
    this.showDeleteButton = false,
    this.autoFocus = false,
  });

  @override
  State<TvaItemFormCard> createState() => _TvaItemFormCardState();
}

class _TvaItemFormCardState extends State<TvaItemFormCard> {
  late final TextEditingController _descriptionController;
  late final TextEditingController _quantityController;
  late final TextEditingController _unitPriceController;
  late final TextEditingController _vatRateController;

  final FocusNode _descriptionFocusNode = FocusNode();
  final FocusNode _quantityFocusNode = FocusNode();
  final FocusNode _unitPriceFocusNode = FocusNode();
  final FocusNode _vatRateFocusNode = FocusNode();

  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();

  List<String> _productNames = [];
  bool _isLoadingProducts = false;
  bool _isExpanded = false;
  bool _manualVatRate = false;
  bool _isExempt = false;
  String? _exemptionReason;

  @override
  void initState() {
    super.initState();
    
    // Initialize controllers with initial values
    _descriptionController = TextEditingController(
      text: widget.initialItem?.description ?? '',
    );
    _quantityController = TextEditingController(
      text: widget.initialItem?.quantity.toString() ?? '1',
    );
    _unitPriceController = TextEditingController(
      text: widget.initialItem?.unitPrice.toString() ?? '',
    );
    _vatRateController = TextEditingController(
      text: widget.initialItem?.vatRate.toString() ?? '20',
    );

    _isExempt = widget.initialItem?.isExempt ?? false;
    _exemptionReason = widget.initialItem?.exemptionReason;

    // Load product names
    _loadProductNames();

    // Add listeners to controllers
    _descriptionController.addListener(_onDescriptionChanged);
    _quantityController.addListener(_onFieldChanged);
    _unitPriceController.addListener(_onFieldChanged);
    _vatRateController.addListener(_onFieldChanged);

    // Auto focus if requested
    if (widget.autoFocus) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _descriptionFocusNode.requestFocus();
      });
    }
  }

  @override
  void dispose() {
    _descriptionController.dispose();
    _quantityController.dispose();
    _unitPriceController.dispose();
    _vatRateController.dispose();
    _descriptionFocusNode.dispose();
    _quantityFocusNode.dispose();
    _unitPriceFocusNode.dispose();
    _vatRateFocusNode.dispose();
    super.dispose();
  }

  Future<void> _loadProductNames() async {
    setState(() {
      _isLoadingProducts = true;
    });

    try {
      final tvaService = TvaService();
      final productNames = await tvaService.getAllProductNames();
      setState(() {
        _productNames = productNames;
        _isLoadingProducts = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingProducts = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur lors du chargement des produits: $e'),
            backgroundColor: Theme.of(context).colorScheme.error,
          ),
        );
      }
    }
  }

  Future<void> _onDescriptionChanged() async {
    final description = _descriptionController.text.trim();
    if (description.isEmpty || _manualVatRate) return;

    try {
      final tvaService = TvaService();
      final vatRate = await tvaService.findVatRate(description);
      final isExempt = await tvaService.isProductExempt(description);
      final exemptionReason = await tvaService.getExemptionReason(description);

      setState(() {
        _vatRateController.text = vatRate.toString();
        _isExempt = isExempt;
        _exemptionReason = exemptionReason;
      });

      _onFieldChanged();
    } catch (e) {
      // Silently handle errors in product lookup
    }
  }

  void _onFieldChanged() {
    if (!_formKey.currentState!.validate()) return;

    final description = _descriptionController.text.trim();
    final quantity = InputValidators.parseQuantityInput(_quantityController.text);
    final unitPrice = InputValidators.parseMonetaryInput(_unitPriceController.text);
    final vatRate = InputValidators.parseMonetaryInput(_vatRateController.text);

    final lineItem = TvaLineItem(
      description: description,
      quantity: quantity,
      unitPrice: unitPrice,
      vatRate: vatRate,
      isExempt: _isExempt,
      exemptionReason: _exemptionReason,
    );

    widget.onChanged(lineItem);
  }

  Widget _buildTotalsDisplay() {
    final quantity = InputValidators.parseQuantityInput(_quantityController.text);
    final unitPrice = InputValidators.parseMonetaryInput(_unitPriceController.text);
    final vatRate = InputValidators.parseMonetaryInput(_vatRateController.text);

    if (quantity <= 0 || unitPrice < 0) {
      return const SizedBox.shrink();
    }

    final totals = CalculationUtils.calculateLineItemTotals(quantity, unitPrice, _isExempt ? 0.0 : vatRate);
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.only(top: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(
                Icons.calculate,
                size: 20,
                color: colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                'Totaux calculés',
                style: textTheme.titleSmall?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total HT:',
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface,
                ),
              ),
              Text(
                CalculationUtils.formatMonetary(totals.totalHT),
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'TVA:',
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface,
                ),
              ),
              Text(
                CalculationUtils.formatMonetary(totals.totalTVA),
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const Divider(height: 16),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total TTC:',
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                CalculationUtils.formatMonetary(totals.totalTTC),
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Card(
      elevation: 2,
      surfaceTintColor: colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              colorScheme.surface,
              colorScheme.surfaceContainerHighest.withOpacity(0.3),
            ],
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Padding(
          padding: const EdgeInsets.all(20.0),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header with exemption badge and delete button
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: colorScheme.primary.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        Icons.inventory_2,
                        color: colorScheme.primary,
                        size: 24,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Article de facture',
                            style: textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: colorScheme.primary,
                            ),
                          ),
                          if (_isExempt || InputValidators.parseMonetaryInput(_vatRateController.text) > 0)
                            TvaExemptionBadge(
                              isExempt: _isExempt,
                              exemptionReason: _exemptionReason,
                              vatRate: _isExempt ? null : InputValidators.parseMonetaryInput(_vatRateController.text),
                            ),
                        ],
                      ),
                    ),
                    if (widget.showDeleteButton && widget.onDelete != null)
                      IconButton(
                        onPressed: widget.onDelete,
                        icon: Icon(
                          Icons.delete_outline,
                          color: colorScheme.error,
                        ),
                        tooltip: 'Supprimer cet article',
                      ),
                  ],
                ),
                const SizedBox(height: 24),

                // Product description with autocomplete
                Autocomplete<String>(
                  optionsBuilder: (TextEditingValue textEditingValue) {
                    if (textEditingValue.text.isEmpty) {
                      return const Iterable<String>.empty();
                    }
                    return _productNames.where((String option) {
                      return option.toLowerCase().contains(textEditingValue.text.toLowerCase());
                    });
                  },
                  onSelected: (String selection) {
                    _descriptionController.text = selection;
                    _onDescriptionChanged();
                  },
                  fieldViewBuilder: (context, controller, focusNode, onFieldSubmitted) {
                    // Sync with our controller
                    controller.text = _descriptionController.text;
                    controller.selection = _descriptionController.selection;
                    
                    return TextFormField(
                      controller: controller,
                      focusNode: _descriptionFocusNode,
                      decoration: InputDecoration(
                        labelText: 'Description du produit/service',
                        hintText: 'Tapez pour rechercher un produit...',
                        filled: true,
                        fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                        enabledBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: colorScheme.outline.withOpacity(0.3),
                          ),
                        ),
                        focusedBorder: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide(
                            color: colorScheme.primary,
                            width: 2,
                          ),
                        ),
                        prefixIcon: _isLoadingProducts
                            ? Padding(
                                padding: const EdgeInsets.all(12),
                                child: SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    color: colorScheme.primary,
                                  ),
                                ),
                              )
                            : Icon(Icons.search, color: colorScheme.primary),
                        suffixIcon: _descriptionController.text.isNotEmpty
                            ? IconButton(
                                onPressed: () {
                                  _descriptionController.clear();
                                  _onFieldChanged();
                                },
                                icon: Icon(
                                  Icons.clear,
                                  color: colorScheme.onSurfaceVariant,
                                ),
                              )
                            : null,
                      ),
                      validator: (value) => InputValidators.validateDescription(value),
                      onChanged: (value) {
                        _descriptionController.text = value;
                        _descriptionController.selection = TextSelection.fromPosition(
                          TextPosition(offset: value.length),
                        );
                        _onDescriptionChanged();
                      },
                      onFieldSubmitted: (_) {
                        _quantityFocusNode.requestFocus();
                      },
                    );
                  },
                ),
                const SizedBox(height: 16),

                // Quantity and Unit Price row
                Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: TextFormField(
                        controller: _quantityController,
                        focusNode: _quantityFocusNode,
                        decoration: InputDecoration(
                          labelText: 'Quantité',
                          filled: true,
                          fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: colorScheme.outline.withOpacity(0.3),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: colorScheme.primary,
                              width: 2,
                            ),
                          ),
                          prefixIcon: Icon(Icons.numbers, color: colorScheme.primary),
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: InputValidators.getQuantityInputFormatters(),
                        validator: (value) => InputValidators.validateQuantity(value),
                        onFieldSubmitted: (_) {
                          _unitPriceFocusNode.requestFocus();
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      flex: 2,
                      child: TextFormField(
                        controller: _unitPriceController,
                        focusNode: _unitPriceFocusNode,
                        decoration: InputDecoration(
                          labelText: 'Prix unitaire HT',
                          filled: true,
                          fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: colorScheme.outline.withOpacity(0.3),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: colorScheme.primary,
                              width: 2,
                            ),
                          ),
                          prefixIcon: Icon(Icons.attach_money, color: colorScheme.primary),
                          suffixText: ' DH',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: InputValidators.getMonetaryInputFormatters(),
                        validator: (value) => InputValidators.validateUnitPrice(value),
                        onFieldSubmitted: (_) {
                          if (!_manualVatRate) {
                            _isExpanded = true;
                            setState(() {});
                          } else {
                            _vatRateFocusNode.requestFocus();
                          }
                        },
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // VAT Rate section
                Row(
                  children: [
                    Expanded(
                      child: TextFormField(
                        controller: _vatRateController,
                        focusNode: _vatRateFocusNode,
                        decoration: InputDecoration(
                          labelText: 'Taux TVA',
                          filled: true,
                          fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide.none,
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: colorScheme.outline.withOpacity(0.3),
                            ),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(12),
                            borderSide: BorderSide(
                              color: colorScheme.primary,
                              width: 2,
                            ),
                          ),
                          prefixIcon: Icon(Icons.percent, color: colorScheme.primary),
                          suffixText: ' %',
                        ),
                        keyboardType: const TextInputType.numberWithOptions(decimal: true),
                        inputFormatters: InputValidators.getPercentageInputFormatters(),
                        validator: (value) => InputValidators.validateTaxRate(value),
                        readOnly: !_manualVatRate,
                        enabled: _manualVatRate,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Tooltip(
                      message: _manualVatRate 
                          ? 'Retour au taux automatique'
                          : 'Saisie manuelle du taux',
                      child: IconButton(
                        onPressed: () {
                          setState(() {
                            _manualVatRate = !_manualVatRate;
                            if (!_manualVatRate) {
                              _onDescriptionChanged();
                            }
                          });
                        },
                        icon: Icon(
                          _manualVatRate ? Icons.auto_mode : Icons.edit,
                          color: colorScheme.primary,
                        ),
                      ),
                    ),
                  ],
                ),

                // Advanced options toggle
                const SizedBox(height: 16),
                TextButton.icon(
                  onPressed: () {
                    setState(() {
                      _isExpanded = !_isExpanded;
                    });
                  },
                  icon: Icon(
                    _isExpanded ? Icons.expand_less : Icons.expand_more,
                    color: colorScheme.primary,
                  ),
                  label: Text(
                    _isExpanded ? 'Masquer les options' : 'Options avancées',
                    style: textTheme.labelLarge?.copyWith(
                      color: colorScheme.primary,
                    ),
                  ),
                ),

                // Expanded options
                if (_isExpanded) ...[
                  const SizedBox(height: 16),
                  Card(
                    elevation: 0,
                    color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: SwitchListTile(
                      title: Text(
                        'Article exonéré de TVA',
                        style: textTheme.titleSmall?.copyWith(
                          color: colorScheme.onSurface,
                        ),
                      ),
                      subtitle: Text(
                        'Forcer l\'exonération pour cet article',
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                      value: _isExempt,
                      onChanged: (bool value) {
                        setState(() {
                          _isExempt = value;
                          if (value) {
                            _vatRateController.text = '0';
                          } else if (!_manualVatRate) {
                            _onDescriptionChanged();
                          }
                        });
                        _onFieldChanged();
                      },
                      activeColor: colorScheme.primary,
                    ),
                  ),
                ],

                // Totals display
                _buildTotalsDisplay(),
              ],
            ),
          ),
        ),
      ),
    );
  }
}