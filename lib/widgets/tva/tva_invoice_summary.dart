import 'package:flutter/material.dart';
import '../../models/tva/tva_invoice_summary.dart';
import '../../utils/calculation_utils.dart';

/// Comprehensive widget that displays invoice totals and VAT breakdown
class TvaInvoiceSummaryWidget extends StatelessWidget {
  /// Invoice summary data
  final TvaInvoiceSummary summary;
  
  /// Control VAT breakdown visibility
  final bool showBreakdown;
  
  /// Control export button visibility
  final bool showExportActions;
  
  /// Callback for export action
  final VoidCallback? onExport;
  
  /// Custom padding
  final EdgeInsets? padding;

  const TvaInvoiceSummaryWidget({
    super.key,
    required this.summary,
    this.showBreakdown = true,
    this.showExportActions = false,
    this.onExport,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (!summary.hasItems) {
      return _buildEmptyState(context);
    }

    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Card(
        elevation: 4,
        surfaceTintColor: colorScheme.surfaceTint,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header Section
            _buildHeader(context),
            
            // Main Totals Section
            _buildMainTotals(context),
            
            // VAT Breakdown Section
            if (showBreakdown) _buildVatBreakdown(context),
            
            // Exempt Items Section
            if (summary.hasExemptItems) _buildExemptItemsSection(context),
            
            // Actions Section
            if (showExportActions) _buildActionsSection(context),
          ],
        ),
      ),
    );
  }

  /// Build empty state when no items
  Widget _buildEmptyState(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: padding ?? EdgeInsets.zero,
      child: Card(
        elevation: 2,
        surfaceTintColor: colorScheme.surfaceTint,
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              Icon(
                Icons.receipt_long_outlined,
                size: 48,
                color: colorScheme.outline,
              ),
              const SizedBox(height: 16),
              Text(
                'Aucun article',
                style: textTheme.titleMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                'Ajoutez des articles pour voir le résumé de la facture',
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build header with title and item count
  Widget _buildHeader(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.primary,
            colorScheme.primaryContainer,
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.receipt_long,
            color: colorScheme.onPrimary,
            size: 28,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Résumé de la facture',
                  style: textTheme.titleLarge?.copyWith(
                    color: colorScheme.onPrimary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${summary.items.length} article${summary.items.length > 1 ? 's' : ''}',
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onPrimary.withOpacity(0.8),
                  ),
                ),
              ],
            ),
          ),
          if (summary.hasExemptItems)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: colorScheme.onPrimary.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${summary.exemptItemsCount} exonéré${summary.exemptItemsCount > 1 ? 's' : ''}',
                style: textTheme.labelSmall?.copyWith(
                  color: colorScheme.onPrimary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build main totals display
  Widget _buildMainTotals(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Totaux',
            style: textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 16),
          _buildTotalRow(
            context,
            'Total HT:',
            summary.totalHT,
            colorScheme.primary,
            isLarge: false,
          ),
          Divider(color: colorScheme.outlineVariant),
          _buildTotalRow(
            context,
            'Total TVA:',
            summary.totalTVA,
            colorScheme.secondary,
            isLarge: false,
          ),
          Divider(color: colorScheme.outlineVariant),
          _buildTotalRow(
            context,
            'Total TTC:',
            summary.totalTTC,
            colorScheme.tertiary,
            isLarge: true,
          ),
        ],
      ),
    );
  }

  /// Build VAT breakdown section
  Widget _buildVatBreakdown(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final vatBreakdown = summary.vatBreakdown;

    if (vatBreakdown.isEmpty) return const SizedBox.shrink();

    return ExpansionTile(
      title: Text(
        'Détail TVA par taux',
        style: textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: colorScheme.onSurface,
        ),
      ),
      leading: Icon(
        Icons.pie_chart_outline,
        color: colorScheme.primary,
      ),
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
          child: Column(
            children: vatBreakdown.entries.map((entry) {
              final rate = entry.key;
              final amount = entry.value;
              final color = _getVatRateColor(context, rate);
              
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 4),
                child: Row(
                  children: [
                    Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        color: color,
                        shape: BoxShape.circle,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'TVA ${rate.toStringAsFixed(rate == rate.toInt() ? 0 : 1)}%',
                        style: textTheme.bodyMedium,
                      ),
                    ),
                    Text(
                      CalculationUtils.formatMonetary(amount),
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: color,
                      ),
                    ),
                  ],
                ),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }

  /// Build exempt items section
  Widget _buildExemptItemsSection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withOpacity(0.3),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: colorScheme.primaryContainer,
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.check_circle_outline,
            color: colorScheme.primary,
            size: 20,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              '${summary.exemptItemsCount} article${summary.exemptItemsCount > 1 ? 's' : ''} exonéré${summary.exemptItemsCount > 1 ? 's' : ''} de TVA',
              style: textTheme.bodySmall?.copyWith(
                color: colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build actions section
  Widget _buildActionsSection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Row(
        children: [
          Expanded(
            child: OutlinedButton.icon(
              onPressed: onExport,
              icon: const Icon(Icons.share),
              label: const Text('Partager'),
              style: OutlinedButton.styleFrom(
                foregroundColor: colorScheme.primary,
                side: BorderSide(color: colorScheme.outline),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: FilledButton.icon(
              onPressed: onExport,
              icon: const Icon(Icons.download),
              label: const Text('Exporter'),
            ),
          ),
        ],
      ),
    );
  }

  /// Build a total row with proper styling
  Widget _buildTotalRow(
    BuildContext context,
    String label,
    double amount,
    Color color, {
    bool isLarge = false,
  }) {
    final textTheme = Theme.of(context).textTheme;

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: (isLarge ? textTheme.titleMedium : textTheme.bodyLarge)?.copyWith(
              color: Theme.of(context).colorScheme.onSurface,
              fontWeight: isLarge ? FontWeight.bold : FontWeight.normal,
            ),
          ),
          Text(
            CalculationUtils.formatMonetary(amount),
            style: (isLarge ? textTheme.titleLarge : textTheme.titleMedium)?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// Get color for VAT rate
  Color _getVatRateColor(BuildContext context, double rate) {
    final colorScheme = Theme.of(context).colorScheme;
    
    if (rate == 0.0) {
      return colorScheme.primary;
    } else if (rate == 10.0) {
      return colorScheme.secondary;
    } else if (rate == 20.0) {
      return colorScheme.tertiary;
    } else {
      return colorScheme.outline;
    }
  }
}