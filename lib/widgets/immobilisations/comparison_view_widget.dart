import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import '../../../models/immobilisations/amortization_row.dart';

enum ComparisonMode {
  methodComparison,
  taxVsAccounting,
  scenarioComparison,
}

class ComparisonViewWidget extends StatefulWidget {
  final List<AmortizationRow> primaryData;
  final List<AmortizationRow> comparisonData;
  final String primaryTitle;
  final String comparisonTitle;
  final ComparisonMode mode;
  final bool showDifferences;
  final VoidCallback? onExport;

  const ComparisonViewWidget({
    super.key,
    required this.primaryData,
    required this.comparisonData,
    required this.primaryTitle,
    required this.comparisonTitle,
    required this.mode,
    this.showDifferences = false,
    this.onExport,
  });

  @override
  State<ComparisonViewWidget> createState() => _ComparisonViewWidgetState();
}

class _ComparisonViewWidgetState extends State<ComparisonViewWidget>
    with TickerProviderStateMixin {
  late ScrollController _primaryScrollController;
  late ScrollController _comparisonScrollController;
  late TabController _tabController;
  
  bool _isPrimaryScrolling = false;
  bool _isComparisonScrolling = false;
  int? _hoveredRowIndex;
  bool _showDifferences = false;

  @override
  void initState() {
    super.initState();
    _primaryScrollController = ScrollController();
    _comparisonScrollController = ScrollController();
    _tabController = TabController(length: 2, vsync: this);
    _showDifferences = widget.showDifferences;
    
    _setupScrollSynchronization();
  }

  void _setupScrollSynchronization() {
    _primaryScrollController.addListener(() {
      if (!_isComparisonScrolling && _primaryScrollController.hasClients) {
        _isPrimaryScrolling = true;
        if (_comparisonScrollController.hasClients) {
          _comparisonScrollController.jumpTo(_primaryScrollController.offset);
        }
        _isPrimaryScrolling = false;
      }
    });

    _comparisonScrollController.addListener(() {
      if (!_isPrimaryScrolling && _comparisonScrollController.hasClients) {
        _isComparisonScrolling = true;
        if (_primaryScrollController.hasClients) {
          _primaryScrollController.jumpTo(_comparisonScrollController.offset);
        }
        _isComparisonScrolling = false;
      }
    });
  }

  @override
  void dispose() {
    _primaryScrollController.dispose();
    _comparisonScrollController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  bool get _isTabletOrDesktop {
    return MediaQuery.of(context).size.width >= 768;
  }

  Widget _buildSummaryCards() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    final primaryTotal = widget.primaryData.fold<double>(
      0.0,
      (sum, row) => sum + row.annuity,
    );
    final comparisonTotal = widget.comparisonData.fold<double>(
      0.0,
      (sum, row) => sum + row.annuity,
    );
    final difference = comparisonTotal - primaryTotal;
    final percentageDifference = primaryTotal != 0 
        ? (difference / primaryTotal) * 100 
        : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: Card(
              elevation: 2,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.primaryContainer,
                      colorScheme.primary.withOpacity(0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total ${widget.primaryTitle}',
                      style: textTheme.titleSmall?.copyWith(
                        color: colorScheme.primary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${NumberFormat('#,##0.00', 'fr_FR').format(primaryTotal)} DH',
                      style: textTheme.titleMedium?.copyWith(
                        color: colorScheme.onPrimaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Card(
              elevation: 2,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      colorScheme.secondaryContainer,
                      colorScheme.secondary.withOpacity(0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Total ${widget.comparisonTitle}',
                      style: textTheme.titleSmall?.copyWith(
                        color: colorScheme.secondary,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${NumberFormat('#,##0.00', 'fr_FR').format(comparisonTotal)} DH',
                      style: textTheme.titleMedium?.copyWith(
                        color: colorScheme.onSecondaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Card(
              elevation: 2,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      difference >= 0 
                          ? colorScheme.errorContainer
                          : colorScheme.tertiaryContainer,
                      (difference >= 0 
                          ? colorScheme.error
                          : colorScheme.tertiary).withOpacity(0.1),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          difference >= 0 
                              ? Icons.trending_up 
                              : Icons.trending_down,
                          color: difference >= 0 
                              ? colorScheme.error
                              : colorScheme.tertiary,
                          size: 20,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          'Différence',
                          style: textTheme.titleSmall?.copyWith(
                            color: difference >= 0 
                                ? colorScheme.error
                                : colorScheme.tertiary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      '${NumberFormat('#,##0.00', 'fr_FR').format(difference.abs())} DH',
                      style: textTheme.titleMedium?.copyWith(
                        color: difference >= 0 
                            ? colorScheme.onErrorContainer
                            : colorScheme.onTertiaryContainer,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      '${percentageDifference.toStringAsFixed(1)}%',
                      style: textTheme.bodySmall?.copyWith(
                        color: difference >= 0 
                            ? colorScheme.onErrorContainer
                            : colorScheme.onTertiaryContainer,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader(String title, bool isPrimary) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            isPrimary 
                ? colorScheme.primaryContainer
                : colorScheme.secondaryContainer,
            (isPrimary 
                ? colorScheme.primary
                : colorScheme.secondary).withOpacity(0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
      ),
      child: Row(
        children: [
          Icon(
            isPrimary ? Icons.table_chart : Icons.compare_arrows,
            color: isPrimary 
                ? colorScheme.primary
                : colorScheme.secondary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              title,
              style: textTheme.titleMedium?.copyWith(
                color: isPrimary 
                    ? colorScheme.primary
                    : colorScheme.secondary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          if (widget.onExport != null)
            IconButton(
              onPressed: widget.onExport,
              icon: Icon(
                Icons.file_download,
                color: isPrimary 
                    ? colorScheme.primary
                    : colorScheme.secondary,
              ),
              tooltip: 'Exporter vers Excel',
            ),
        ],
      ),
    );
  }

  List<String> _getTableColumns() {
    switch (widget.mode) {
      case ComparisonMode.methodComparison:
        return ['Année', 'Base', 'Taux', 'Annuités', 'Annuités cumulées', 'VNC'];
      case ComparisonMode.taxVsAccounting:
        return ['Année', 'Amort. Comptable', 'Amort. Fiscal', 'Dotations', 'Reprises'];
      case ComparisonMode.scenarioComparison:
        return ['Année', 'Base', 'Taux', 'Annuités', 'Annuités cumulées', 'VNC'];
    }
  }

  Widget _buildTableHeaderRow() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final columns = _getTableColumns();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withOpacity(0.5),
        border: Border(
          bottom: BorderSide(
            color: colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Row(
        children: columns.map((column) {
          return Expanded(
            child: Text(
              column,
              style: textTheme.titleSmall?.copyWith(
                color: colorScheme.onSurface,
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
            ),
          );
        }).toList(),
      ),
    );
  }

  List<String> _getRowValues(AmortizationRow row) {
    switch (widget.mode) {
      case ComparisonMode.methodComparison:
        return [
          row.yearLabel,
          NumberFormat('#,##0.00', 'fr_FR').format(row.baseAmount),
          '${row.rate.toStringAsFixed(2)}%',
          NumberFormat('#,##0.00', 'fr_FR').format(row.annuity),
          NumberFormat('#,##0.00', 'fr_FR').format(row.cumulativeAnnuity),
          NumberFormat('#,##0.00', 'fr_FR').format(row.netBookValue),
        ];
      case ComparisonMode.taxVsAccounting:
        return [
          row.yearLabel,
          NumberFormat('#,##0.00', 'fr_FR').format(row.accountingAmortization ?? 0),
          NumberFormat('#,##0.00', 'fr_FR').format(row.fiscalAmortization ?? 0),
          row.derogationProvision != null && row.derogationProvision! > 0
              ? NumberFormat('#,##0.00', 'fr_FR').format(row.derogationProvision!)
              : '-',
          row.derogationReprise != null && row.derogationReprise! > 0
              ? NumberFormat('#,##0.00', 'fr_FR').format(row.derogationReprise!)
              : '-',
        ];
      case ComparisonMode.scenarioComparison:
        return [
          row.yearLabel,
          NumberFormat('#,##0.00', 'fr_FR').format(row.baseAmount),
          '${row.rate.toStringAsFixed(2)}%',
          NumberFormat('#,##0.00', 'fr_FR').format(row.annuity),
          NumberFormat('#,##0.00', 'fr_FR').format(row.cumulativeAnnuity),
          NumberFormat('#,##0.00', 'fr_FR').format(row.netBookValue),
        ];
    }
  }

  Color? _getCellColor(int rowIndex, int columnIndex, bool isPrimary, AmortizationRow primaryRow, AmortizationRow? comparisonRow) {
    final colorScheme = Theme.of(context).colorScheme;
    
    if (!_showDifferences || comparisonRow == null) {
      return _hoveredRowIndex == rowIndex 
          ? colorScheme.surfaceContainerHighest.withOpacity(0.3)
          : null;
    }

    // Compare values based on column and mode
    double? primaryValue;
    double? comparisonValue;

    switch (widget.mode) {
      case ComparisonMode.methodComparison:
      case ComparisonMode.scenarioComparison:
        switch (columnIndex) {
          case 1: // Base
            primaryValue = primaryRow.baseAmount;
            comparisonValue = comparisonRow.baseAmount;
            break;
          case 2: // Taux
            primaryValue = primaryRow.rate;
            comparisonValue = comparisonRow.rate;
            break;
          case 3: // Annuités
            primaryValue = primaryRow.annuity;
            comparisonValue = comparisonRow.annuity;
            break;
          case 4: // Annuités cumulées
            primaryValue = primaryRow.cumulativeAnnuity;
            comparisonValue = comparisonRow.cumulativeAnnuity;
            break;
          case 5: // VNC
            primaryValue = primaryRow.netBookValue;
            comparisonValue = comparisonRow.netBookValue;
            break;
        }
        break;
      case ComparisonMode.taxVsAccounting:
        switch (columnIndex) {
          case 1: // Amort. Comptable
            primaryValue = primaryRow.accountingAmortization;
            comparisonValue = comparisonRow.accountingAmortization;
            break;
          case 2: // Amort. Fiscal
            primaryValue = primaryRow.fiscalAmortization;
            comparisonValue = comparisonRow.fiscalAmortization;
            break;
          case 3: // Dotations
            primaryValue = primaryRow.derogationProvision;
            comparisonValue = comparisonRow.derogationProvision;
            break;
          case 4: // Reprises
            primaryValue = primaryRow.derogationReprise;
            comparisonValue = comparisonRow.derogationReprise;
            break;
        }
        break;
    }

    Color? baseColor = _hoveredRowIndex == rowIndex 
        ? colorScheme.surfaceContainerHighest.withOpacity(0.3)
        : null;

    if (primaryValue != null && comparisonValue != null) {
      final difference = (comparisonValue - primaryValue).abs();
      final threshold = primaryValue * 0.05; // 5% threshold
      
      if (difference > threshold) {
        if (isPrimary) {
          return comparisonValue > primaryValue 
              ? colorScheme.errorContainer.withOpacity(0.3)
              : colorScheme.tertiaryContainer.withOpacity(0.3);
        } else {
          return comparisonValue > primaryValue 
              ? colorScheme.tertiaryContainer.withOpacity(0.3)
              : colorScheme.errorContainer.withOpacity(0.3);
        }
      }
    }

    return baseColor;
  }

  Widget _buildTableRow(int index, AmortizationRow primaryRow, AmortizationRow? comparisonRow, bool isPrimary) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final row = isPrimary ? primaryRow : comparisonRow!;
    final values = _getRowValues(row);

    return InkWell(
      onTap: () {
        HapticFeedback.lightImpact();
      },
      onHover: (hovering) {
        setState(() {
          _hoveredRowIndex = hovering ? index : null;
        });
      },
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: index.isEven 
              ? colorScheme.surface
              : colorScheme.surfaceContainerHighest.withOpacity(0.1),
          border: Border(
            bottom: BorderSide(
              color: colorScheme.outline.withOpacity(0.1),
            ),
          ),
        ),
        child: Row(
          children: values.asMap().entries.map((entry) {
            final columnIndex = entry.key;
            final value = entry.value;
            
            return Expanded(
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
                decoration: BoxDecoration(
                  color: _getCellColor(index, columnIndex, isPrimary, primaryRow, comparisonRow),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  value,
                  style: textTheme.bodyMedium?.copyWith(
                    color: colorScheme.onSurface,
                    fontWeight: columnIndex == 0 ? FontWeight.w500 : FontWeight.normal,
                  ),
                  textAlign: columnIndex == 0 ? TextAlign.left : TextAlign.right,
                ),
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildTable(List<AmortizationRow> data, String title, bool isPrimary, ScrollController scrollController) {
    final colorScheme = Theme.of(context).colorScheme;

    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        children: [
          _buildTableHeader(title, isPrimary),
          _buildTableHeaderRow(),
          Expanded(
            child: ListView.builder(
              controller: scrollController,
              itemCount: data.length,
              itemBuilder: (context, index) {
                final primaryRow = index < widget.primaryData.length 
                    ? widget.primaryData[index] 
                    : widget.primaryData.last;
                final comparisonRow = index < widget.comparisonData.length 
                    ? widget.comparisonData[index] 
                    : null;
                
                return _buildTableRow(index, primaryRow, comparisonRow, isPrimary);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopLayout() {
    return Column(
      children: [
        _buildSummaryCards(),
        Row(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 16, right: 8, bottom: 16),
                child: _buildTable(
                  widget.primaryData,
                  widget.primaryTitle,
                  true,
                  _primaryScrollController,
                ),
              ),
            ),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.only(left: 8, right: 16, bottom: 16),
                child: _buildTable(
                  widget.comparisonData,
                  widget.comparisonTitle,
                  false,
                  _comparisonScrollController,
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMobileLayout() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Column(
      children: [
        _buildSummaryCards(),
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
          ),
          child: TabBar(
            controller: _tabController,
            tabs: [
              Tab(
                child: Text(
                  widget.primaryTitle,
                  style: textTheme.titleSmall,
                  textAlign: TextAlign.center,
                ),
              ),
              Tab(
                child: Text(
                  widget.comparisonTitle,
                  style: textTheme.titleSmall,
                  textAlign: TextAlign.center,
                ),
              ),
            ],
            labelColor: colorScheme.primary,
            unselectedLabelColor: colorScheme.onSurfaceVariant,
            indicator: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(12),
            ),
          ),
        ),
        const SizedBox(height: 16),
        Expanded(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTable(
                  widget.primaryData,
                  widget.primaryTitle,
                  true,
                  _primaryScrollController,
                ),
                _buildTable(
                  widget.comparisonData,
                  widget.comparisonTitle,
                  false,
                  _comparisonScrollController,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildControls() {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
        border: Border(
          bottom: BorderSide(
            color: colorScheme.outline.withOpacity(0.2),
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.compare_arrows,
            color: colorScheme.primary,
            size: 24,
          ),
          const SizedBox(width: 12),
          Text(
            'Comparaison: ${_getComparisonModeTitle()}',
            style: textTheme.titleMedium?.copyWith(
              color: colorScheme.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          Row(
            children: [
              Text(
                'Différences',
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurface,
                ),
              ),
              const SizedBox(width: 8),
              Switch(
                value: _showDifferences,
                onChanged: (value) {
                  setState(() {
                    _showDifferences = value;
                  });
                },
                activeColor: colorScheme.primary,
              ),
            ],
          ),
        ],
      ),
    );
  }

  String _getComparisonModeTitle() {
    switch (widget.mode) {
      case ComparisonMode.methodComparison:
        return 'Méthodes d\'amortissement';
      case ComparisonMode.taxVsAccounting:
        return 'Fiscal vs Comptable';
      case ComparisonMode.scenarioComparison:
        return 'Scénarios';
    }
  }

  @override
  Widget build(BuildContext context) {
    return Semantics(
      label: 'Comparaison d\'amortissement',
      child: Column(
        children: [
          _buildControls(),
          Expanded(
            child: _isTabletOrDesktop 
                ? _buildDesktopLayout()
                : _buildMobileLayout(),
          ),
        ],
      ),
    );
  }
}