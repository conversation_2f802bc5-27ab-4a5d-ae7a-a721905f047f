import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../models/is/is_input_data.dart';
import '../../models/is/tax_bracket.dart';
import '../../services/is_calculator_service.dart';
import '../../utils/calculation_utils.dart';
import '../../theme/app_theme.dart';

/// Comprehensive sector selection widget for Step 1 of the IS calculator wizard
/// 
/// This widget provides:
/// - Regime selection with search and filtering
/// - Automatic rate display and tax bracket preview
/// - Eligibility checking and conditions display
/// - Recommendations based on business characteristics
/// - Real-time validation and visual feedback
class SectorSelectionCard extends StatefulWidget {
  /// Currently selected regime
  final String? selectedRegime;
  
  /// Callback when regime selection changes
  final ValueChanged<String> onRegimeChanged;
  
  /// Whether to show regime suggestions
  final bool showRecommendations;
  
  /// Optional business characteristics for recommendations
  final Map<String, dynamic>? businessProfile;

  const SectorSelectionCard({
    super.key,
    this.selectedRegime,
    required this.onRegimeChanged,
    this.showRecommendations = true,
    this.businessProfile,
  });

  @override
  State<SectorSelectionCard> createState() => _SectorSelectionCardState();
}

class _SectorSelectionCardState extends State<SectorSelectionCard>
    with TickerProviderStateMixin {
  // State management
  List<String> _availableRegimes = [];
  List<String> _filteredRegimes = [];
  Map<String, Map<String, dynamic>> _regimeInfoCache = {};
  Map<String, List<TaxBracket>> _regimeBracketsCache = {};
  
  // UI state
  bool _isLoading = false;
  bool _isLoadingRegimeInfo = false;
  bool _showAdvancedOptions = false;
  String? _errorMessage;
  String? _recommendedRegime;
  
  // Controllers and focus nodes
  final TextEditingController _searchController = TextEditingController();
  final FocusNode _searchFocusNode = FocusNode();
  
  // Animation controllers
  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    
    // Initialize animation controllers
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
    
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOutCubic,
    ));
    
    // Add search listener
    _searchController.addListener(_onSearchChanged);
    
    // Load initial data
    _loadAvailableRegimes();
    
    // Start animations
    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _searchController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  /// Load available regimes from the service
  Future<void> _loadAvailableRegimes() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final regimes = await IsCalculatorService.getAvailableRegimes();
      setState(() {
        _availableRegimes = regimes;
        _filteredRegimes = regimes;
        _isLoading = false;
      });

      // Load recommendations if enabled
      if (widget.showRecommendations) {
        _loadRecommendations();
      }

      // Pre-load regime info for better UX
      _preloadRegimeInfo();
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Erreur lors du chargement des régimes: $e';
      });
    }
  }

  /// Load regime recommendations based on business profile
  Future<void> _loadRecommendations() async {
    if (widget.businessProfile == null) return;

    try {
      // Create a sample input data for recommendation
      final sampleInput = IsInputData(
        revenue: (widget.businessProfile!['revenue'] as num?)?.toDouble() ?? 0.0,
        revenueType: widget.businessProfile!['revenueType'] as String? ?? 'normal',
        accountingResult: (widget.businessProfile!['accountingResult'] as num?)?.toDouble() ?? 0.0,
      );

      final recommended = await IsCalculatorService.suggestOptimalRegime(sampleInput);
      setState(() {
        _recommendedRegime = recommended;
      });
    } catch (e) {
      // Silently handle recommendation errors
      debugPrint('Error loading recommendations: $e');
    }
  }

  /// Pre-load regime information for better performance
  Future<void> _preloadRegimeInfo() async {
    for (final regime in _availableRegimes.take(3)) {
      try {
        final info = await IsCalculatorService.getRegimeInfo(regime);
        final brackets = await IsCalculatorService.getRegimeBrackets(regime);
        
        setState(() {
          _regimeInfoCache[regime] = info;
          _regimeBracketsCache[regime] = brackets;
        });
      } catch (e) {
        // Continue with other regimes if one fails
        debugPrint('Error preloading regime info for $regime: $e');
      }
    }
  }

  /// Handle search input changes
  void _onSearchChanged() {
    final query = _searchController.text.toLowerCase().trim();
    
    setState(() {
      if (query.isEmpty) {
        _filteredRegimes = _availableRegimes;
      } else {
        _filteredRegimes = _availableRegimes.where((regime) {
          return regime.toLowerCase().contains(query);
        }).toList();
      }
    });
  }

  /// Handle regime selection
  Future<void> _onRegimeSelected(String regime) async {
    if (regime == widget.selectedRegime) return;

    setState(() {
      _isLoadingRegimeInfo = true;
    });

    try {
      // Load regime info if not cached
      if (!_regimeInfoCache.containsKey(regime)) {
        final info = await IsCalculatorService.getRegimeInfo(regime);
        final brackets = await IsCalculatorService.getRegimeBrackets(regime);
        
        setState(() {
          _regimeInfoCache[regime] = info;
          _regimeBracketsCache[regime] = brackets;
        });
      }

      // Notify parent of selection change
      widget.onRegimeChanged(regime);
      
      setState(() {
        _isLoadingRegimeInfo = false;
      });

      // Provide haptic feedback
      HapticFeedback.selectionClick();
    } catch (e) {
      setState(() {
        _isLoadingRegimeInfo = false;
        _errorMessage = 'Erreur lors du chargement des informations du régime: $e';
      });
    }
  }

  /// Build the main card content
  Widget _buildCardContent(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            colorScheme.surface,
            colorScheme.surfaceContainerHighest.withOpacity(0.3),
          ],
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildHeader(context),
            const SizedBox(height: 24),
            if (_errorMessage != null) ...[
              _buildErrorMessage(context),
              const SizedBox(height: 16),
            ],
            if (_isLoading) ...[
              _buildLoadingIndicator(context),
            ] else ...[
              _buildSearchField(context),
              const SizedBox(height: 20),
              _buildRegimeSelection(context),
              if (widget.selectedRegime != null && widget.selectedRegime!.isNotEmpty) ...[
                const SizedBox(height: 24),
                _buildRegimeDetails(context),
              ],
              if (widget.showRecommendations && _recommendedRegime != null) ...[
                const SizedBox(height: 20),
                _buildRecommendations(context),
              ],
              const SizedBox(height: 20),
              _buildAdvancedOptionsToggle(context),
              if (_showAdvancedOptions) ...[
                const SizedBox(height: 16),
                _buildAdvancedOptions(context),
              ],
            ],
          ],
        ),
      ),
    );
  }

  /// Build the card header
  Widget _buildHeader(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: colorScheme.primary.withOpacity(0.1),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Icon(
            Icons.business_center,
            color: colorScheme.primary,
            size: 28,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Sélection du Régime Fiscal',
                style: textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                'Choisissez le régime fiscal adapté à votre entreprise',
                style: textTheme.bodyMedium?.copyWith(
                  color: colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
        if (widget.selectedRegime != null && widget.selectedRegime!.isNotEmpty)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.check_circle,
                  size: 16,
                  color: colorScheme.onPrimaryContainer,
                ),
                const SizedBox(width: 4),
                Text(
                  'Sélectionné',
                  style: textTheme.labelSmall?.copyWith(
                    color: colorScheme.onPrimaryContainer,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
      ],
    );
  }

  /// Build error message display
  Widget _buildErrorMessage(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.errorContainer.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.error.withOpacity(0.3),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.error_outline,
            color: colorScheme.error,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _errorMessage!,
              style: textTheme.bodySmall?.copyWith(
                color: colorScheme.error,
              ),
            ),
          ),
          IconButton(
            onPressed: () {
              setState(() {
                _errorMessage = null;
              });
            },
            icon: Icon(
              Icons.close,
              size: 18,
              color: colorScheme.error,
            ),
          ),
        ],
      ),
    );
  }

  /// Build loading indicator
  Widget _buildLoadingIndicator(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Center(
      child: Column(
        children: [
          CircularProgressIndicator(
            color: colorScheme.primary,
          ),
          const SizedBox(height: 16),
          Text(
            'Chargement des régimes fiscaux...',
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// Build search field
  Widget _buildSearchField(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;

    return TextFormField(
      controller: _searchController,
      focusNode: _searchFocusNode,
      decoration: InputDecoration(
        labelText: 'Rechercher un régime',
        hintText: 'Tapez pour filtrer les régimes...',
        filled: true,
        fillColor: colorScheme.surfaceContainerHighest.withOpacity(0.3),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide.none,
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.outline.withOpacity(0.3),
          ),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: colorScheme.primary,
            width: 2,
          ),
        ),
        prefixIcon: Icon(
          Icons.search,
          color: colorScheme.primary,
        ),
        suffixIcon: _searchController.text.isNotEmpty
            ? IconButton(
                onPressed: () {
                  _searchController.clear();
                  _searchFocusNode.unfocus();
                },
                icon: Icon(
                  Icons.clear,
                  color: colorScheme.onSurfaceVariant,
                ),
              )
            : null,
      ),
    );
  }

  /// Build regime selection list
  Widget _buildRegimeSelection(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    if (_filteredRegimes.isEmpty) {
      return Container(
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              Icons.search_off,
              size: 48,
              color: colorScheme.onSurfaceVariant,
            ),
            const SizedBox(height: 12),
            Text(
              'Aucun régime trouvé',
              style: textTheme.titleMedium?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              'Essayez de modifier votre recherche',
              style: textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
      );
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Régimes disponibles (${_filteredRegimes.length})',
          style: textTheme.titleSmall?.copyWith(
            color: colorScheme.onSurface,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        ...List.generate(_filteredRegimes.length, (index) {
          final regime = _filteredRegimes[index];
          final isSelected = regime == widget.selectedRegime;
          final isRecommended = regime == _recommendedRegime;

          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: _buildRegimeOption(
              context,
              regime,
              isSelected: isSelected,
              isRecommended: isRecommended,
            ),
          );
        }),
      ],
    );
  }

  /// Build individual regime option
  Widget _buildRegimeOption(
    BuildContext context,
    String regime, {
    required bool isSelected,
    required bool isRecommended,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => _onRegimeSelected(regime),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: isSelected
                ? colorScheme.primaryContainer.withOpacity(0.3)
                : colorScheme.surfaceContainerHighest.withOpacity(0.3),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: isSelected
                  ? colorScheme.primary
                  : colorScheme.outline.withOpacity(0.2),
              width: isSelected ? 2 : 1,
            ),
          ),
          child: Row(
            children: [
              // Selection indicator
              Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: isSelected
                      ? colorScheme.primary
                      : Colors.transparent,
                  border: Border.all(
                    color: isSelected
                        ? colorScheme.primary
                        : colorScheme.outline,
                    width: 2,
                  ),
                ),
                child: isSelected
                    ? Icon(
                        Icons.check,
                        size: 12,
                        color: colorScheme.onPrimary,
                      )
                    : null,
              ),
              const SizedBox(width: 12),
              
              // Regime info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            regime,
                            style: textTheme.titleSmall?.copyWith(
                              color: isSelected
                                  ? colorScheme.primary
                                  : colorScheme.onSurface,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        if (isRecommended) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: colorScheme.tertiary.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'Recommandé',
                              style: textTheme.labelSmall?.copyWith(
                                color: colorScheme.tertiary,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                    if (_regimeInfoCache.containsKey(regime)) ...[
                      const SizedBox(height: 4),
                      Text(
                        _regimeInfoCache[regime]!['description'] as String? ?? '',
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              
              // Loading indicator or arrow
              if (_isLoadingRegimeInfo && isSelected) ...[
                const SizedBox(width: 8),
                SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    color: colorScheme.primary,
                  ),
                ),
              ] else ...[
                const SizedBox(width: 8),
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: colorScheme.onSurfaceVariant,
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Build regime details section
  Widget _buildRegimeDetails(BuildContext context) {
    final regime = widget.selectedRegime!;
    final regimeInfo = _regimeInfoCache[regime];
    final brackets = _regimeBracketsCache[regime];

    if (regimeInfo == null) {
      return _buildRegimeDetailsLoading(context);
    }

    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildRegimeInfoCard(context, regimeInfo),
            if (brackets != null && brackets.isNotEmpty) ...[
              const SizedBox(height: 16),
              _buildTaxBracketsCard(context, brackets),
            ],
            const SizedBox(height: 16),
            _buildEligibilityCard(context, regimeInfo),
          ],
        ),
      ),
    );
  }

  /// Build loading state for regime details
  Widget _buildRegimeDetailsLoading(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              color: colorScheme.primary,
            ),
          ),
          const SizedBox(width: 16),
          Text(
            'Chargement des détails du régime...',
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  /// Build regime information card
  Widget _buildRegimeInfoCard(
    BuildContext context,
    Map<String, dynamic> regimeInfo,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.primaryContainer.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.primary.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: colorScheme.primary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Informations du régime',
                style: textTheme.titleSmall?.copyWith(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            regimeInfo['description'] as String? ?? 'Aucune description disponible',
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
            ),
          ),
          if (regimeInfo['details'] != null) ...[
            const SizedBox(height: 8),
            Text(
              regimeInfo['details'] as String,
              style: textTheme.bodySmall?.copyWith(
                color: colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build tax brackets display card
  Widget _buildTaxBracketsCard(
    BuildContext context,
    List<TaxBracket> brackets,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.secondaryContainer.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.secondary.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.account_balance,
                color: colorScheme.secondary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Barème d\'imposition',
                style: textTheme.titleSmall?.copyWith(
                  color: colorScheme.secondary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...brackets.map((bracket) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              children: [
                Expanded(
                  flex: 3,
                  child: Text(
                    bracket.formattedRange,
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
                Expanded(
                  flex: 1,
                  child: Text(
                    bracket.formattedRate,
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.secondary,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.end,
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  /// Build eligibility conditions card
  Widget _buildEligibilityCard(
    BuildContext context,
    Map<String, dynamic> regimeInfo,
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final conditions = regimeInfo['conditions'] as Map<String, dynamic>? ?? {};

    if (conditions.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.tertiaryContainer.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.tertiary.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.checklist,
                color: colorScheme.tertiary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Conditions d\'éligibilité',
                style: textTheme.titleSmall?.copyWith(
                  color: colorScheme.tertiary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          ...conditions.entries.map((entry) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.check_circle_outline,
                  size: 16,
                  color: colorScheme.tertiary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '${entry.key}: ${entry.value}',
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.onSurface,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  /// Build recommendations section
  Widget _buildRecommendations(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.tertiary.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.tertiary.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: colorScheme.tertiary,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Recommandation',
                style: textTheme.titleSmall?.copyWith(
                  color: colorScheme.tertiary,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'Basé sur votre profil d\'entreprise, nous recommandons le régime "$_recommendedRegime".',
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurface,
            ),
          ),
          if (_recommendedRegime != widget.selectedRegime) ...[
            const SizedBox(height: 12),
            ElevatedButton.icon(
              onPressed: () => _onRegimeSelected(_recommendedRegime!),
              icon: const Icon(Icons.auto_fix_high, size: 16),
              label: const Text('Appliquer la recommandation'),
              style: ElevatedButton.styleFrom(
                backgroundColor: colorScheme.tertiary,
                foregroundColor: colorScheme.onTertiary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build advanced options toggle
  Widget _buildAdvancedOptionsToggle(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return TextButton.icon(
      onPressed: () {
        setState(() {
          _showAdvancedOptions = !_showAdvancedOptions;
        });
      },
      icon: Icon(
        _showAdvancedOptions ? Icons.expand_less : Icons.expand_more,
        color: colorScheme.primary,
      ),
      label: Text(
        _showAdvancedOptions ? 'Masquer les options avancées' : 'Options avancées',
        style: textTheme.labelLarge?.copyWith(
          color: colorScheme.primary,
        ),
      ),
    );
  }

  /// Build advanced options section
  Widget _buildAdvancedOptions(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Options avancées',
            style: textTheme.titleSmall?.copyWith(
              color: colorScheme.onSurface,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Implement regime comparison
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Comparaison des régimes - À venir'),
                      ),
                    );
                  },
                  icon: const Icon(Icons.compare_arrows, size: 16),
                  label: const Text('Comparer les régimes'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.secondaryContainer,
                    foregroundColor: colorScheme.onSecondaryContainer,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () {
                    // TODO: Implement detailed analysis
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Analyse détaillée - À venir'),
                      ),
                    );
                  },
                  icon: const Icon(Icons.analytics, size: 16),
                  label: const Text('Analyse détaillée'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.tertiaryContainer,
                    foregroundColor: colorScheme.onTertiaryContainer,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      surfaceTintColor: Theme.of(context).colorScheme.surfaceTint,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: _buildCardContent(context),
    );
  }
}