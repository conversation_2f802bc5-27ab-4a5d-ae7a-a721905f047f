import 'package:flutter/material.dart';

class IsWizardProgress extends StatelessWidget {
  final int currentStep;
  final int totalSteps;
  final List<String> stepLabels;
  final List<bool> stepCompleted;
  final VoidCallback? onStepTap;

  const IsWizardProgress({
    super.key,
    required this.currentStep,
    required this.totalSteps,
    required this.stepLabels,
    required this.stepCompleted,
    this.onStepTap,
  }) : assert(stepLabels.length == totalSteps),
       assert(stepCompleted.length == totalSteps),
       assert(currentStep >= 0 && currentStep < totalSteps);

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return Semantics(
      label: 'Progression du calculateur IS: étape ${currentStep + 1} sur $totalSteps',
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
        child: LayoutBuilder(
          builder: (context, constraints) {
            final isCompact = constraints.maxWidth < 600;
            final stepWidth = (constraints.maxWidth - 32.0) / totalSteps;
            
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Step indicators and connecting lines
                SizedBox(
                  height: 48.0,
                  child: Stack(
                    children: [
                      // Connecting lines
                      if (totalSteps > 1)
                        Positioned(
                          top: 23.0,
                          left: stepWidth * 0.5,
                          right: stepWidth * 0.5,
                          child: Row(
                            children: List.generate(
                              totalSteps - 1,
                              (index) => Expanded(
                                child: AnimatedContainer(
                                  duration: const Duration(milliseconds: 300),
                                  curve: Curves.easeInOut,
                                  height: 2.0,
                                  margin: EdgeInsets.symmetric(
                                    horizontal: stepWidth * 0.3,
                                  ),
                                  decoration: BoxDecoration(
                                    color: _getLineColor(index, colorScheme),
                                    borderRadius: BorderRadius.circular(1.0),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      
                      // Step indicators
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: List.generate(
                          totalSteps,
                          (index) => _buildStepIndicator(
                            index,
                            colorScheme,
                            textTheme,
                            isCompact,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                
                const SizedBox(height: 16.0),
                
                // Step labels
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: List.generate(
                    totalSteps,
                    (index) => Expanded(
                      child: _buildStepLabel(
                        index,
                        colorScheme,
                        textTheme,
                        isCompact,
                      ),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildStepIndicator(
    int index,
    ColorScheme colorScheme,
    TextTheme textTheme,
    bool isCompact,
  ) {
    final isCompleted = stepCompleted[index];
    final isCurrent = index == currentStep;
    final isPending = index > currentStep;
    
    Color backgroundColor;
    Color foregroundColor;
    Widget child;
    
    if (isCompleted) {
      backgroundColor = colorScheme.primary;
      foregroundColor = colorScheme.onPrimary;
      child = Icon(
        Icons.check,
        size: isCompact ? 16.0 : 20.0,
        color: foregroundColor,
      );
    } else if (isCurrent) {
      backgroundColor = colorScheme.primaryContainer;
      foregroundColor = colorScheme.onPrimaryContainer;
      child = Text(
        '${index + 1}',
        style: textTheme.labelLarge?.copyWith(
          color: foregroundColor,
          fontWeight: FontWeight.w600,
          fontSize: isCompact ? 14.0 : 16.0,
        ),
      );
    } else {
      backgroundColor = colorScheme.outline.withOpacity(0.2);
      foregroundColor = colorScheme.onSurfaceVariant;
      child = Text(
        '${index + 1}',
        style: textTheme.labelLarge?.copyWith(
          color: foregroundColor,
          fontWeight: FontWeight.w500,
          fontSize: isCompact ? 14.0 : 16.0,
        ),
      );
    }

    return Semantics(
      label: _getStepSemanticLabel(index, isCompleted, isCurrent),
      button: onStepTap != null,
      child: GestureDetector(
        onTap: onStepTap != null && (isCompleted || isCurrent) ? onStepTap : null,
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeInOut,
          width: isCompact ? 32.0 : 48.0,
          height: isCompact ? 32.0 : 48.0,
          decoration: BoxDecoration(
            color: backgroundColor,
            shape: BoxShape.circle,
            border: isCurrent
                ? Border.all(
                    color: colorScheme.primary,
                    width: 2.0,
                  )
                : null,
            boxShadow: isCompleted || isCurrent
                ? [
                    BoxShadow(
                      color: colorScheme.shadow.withOpacity(0.15),
                      blurRadius: 8.0,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: AnimatedSwitcher(
            duration: const Duration(milliseconds: 200),
            child: Container(
              key: ValueKey('step_$index${isCompleted ? '_completed' : ''}'),
              child: Center(child: child),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildStepLabel(
    int index,
    ColorScheme colorScheme,
    TextTheme textTheme,
    bool isCompact,
  ) {
    final isCompleted = stepCompleted[index];
    final isCurrent = index == currentStep;
    
    Color textColor;
    FontWeight fontWeight;
    
    if (isCompleted) {
      textColor = colorScheme.primary;
      fontWeight = FontWeight.w600;
    } else if (isCurrent) {
      textColor = colorScheme.onSurface;
      fontWeight = FontWeight.w600;
    } else {
      textColor = colorScheme.onSurfaceVariant;
      fontWeight = FontWeight.w500;
    }

    return AnimatedDefaultTextStyle(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      style: (textTheme.labelMedium ?? const TextStyle()).copyWith(
        color: textColor,
        fontWeight: fontWeight,
        fontSize: isCompact ? 12.0 : 14.0,
        letterSpacing: 0.1,
      ),
      child: Text(
        stepLabels[index],
        textAlign: TextAlign.center,
        maxLines: isCompact ? 1 : 2,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  Color _getLineColor(int lineIndex, ColorScheme colorScheme) {
    // Line connects step lineIndex to step lineIndex + 1
    final leftStepCompleted = stepCompleted[lineIndex];
    final rightStepCurrent = lineIndex + 1 == currentStep;
    final rightStepCompleted = stepCompleted[lineIndex + 1];
    
    if (leftStepCompleted && (rightStepCompleted || rightStepCurrent)) {
      return colorScheme.primary;
    } else if (leftStepCompleted || lineIndex < currentStep) {
      return colorScheme.primary.withOpacity(0.5);
    } else {
      return colorScheme.outline.withOpacity(0.3);
    }
  }

  String _getStepSemanticLabel(int index, bool isCompleted, bool isCurrent) {
    final stepName = stepLabels[index];
    if (isCompleted) {
      return 'Étape ${index + 1}: $stepName - Terminée';
    } else if (isCurrent) {
      return 'Étape ${index + 1}: $stepName - En cours';
    } else {
      return 'Étape ${index + 1}: $stepName - En attente';
    }
  }
}