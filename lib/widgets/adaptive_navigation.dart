import 'package:flutter/material.dart';

enum NavigationItem {
  home,
  planComptable,
  guide,
  tools,
  references,
  quiz,
  settings,
}

class AdaptiveNavigation extends StatelessWidget {
  final NavigationItem selectedItem;
  final ValueChanged<NavigationItem> onNavigationItemSelected;
  final Widget child;

  const AdaptiveNavigation({
    required this.selectedItem,
    required this.onNavigationItemSelected,
    required this.child,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    // Check if we're on a small screen (mobile)
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600; // Standard breakpoint for mobile
    
    if (isSmallScreen) {
      return _buildMobileLayout(context);
    } else {
      return _buildDesktopLayout(context);
    }
  }

  Widget _buildDesktopLayout(BuildContext context) {
    return Row(
      children: [
        _buildNavigationRail(context),
        Expanded(child: child),
      ],
    );
  }

  Widget _buildMobileLayout(BuildContext context) {
    return Scaffold(
      body: child,
      bottomNavigationBar: _buildBottomNavigationBar(context),
    );
  }

  Widget _buildBottomNavigationBar(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    
    return BottomNavigationBar(
      currentIndex: _getIndexForItem(selectedItem),
      onTap: (index) => onNavigationItemSelected(_getItemForIndex(index)),
      type: BottomNavigationBarType.fixed,
      selectedItemColor: colorScheme.primary,
      unselectedItemColor: colorScheme.onSurfaceVariant,
      items: [
        _buildBottomNavItem(Icons.home_rounded, 'Accueil'),
        _buildBottomNavItem(Icons.account_balance_rounded, 'PCG'),
        _buildBottomNavItem(Icons.menu_book_rounded, 'Guide'),
        _buildBottomNavItem(Icons.calculate_rounded, 'Outils'),
        _buildBottomNavItem(Icons.quiz_rounded, 'Quiz'),
        _buildBottomNavItem(Icons.settings_rounded, 'Paramètres'),
      ],
    );
  }

  BottomNavigationBarItem _buildBottomNavItem(IconData icon, String label) {
    return BottomNavigationBarItem(
      icon: Icon(icon),
      label: label,
    );
  }

  // Helper method to convert NavigationItem to bottom nav index
  int _getIndexForItem(NavigationItem item) {
    switch (item) {
      case NavigationItem.home:
        return 0;
      case NavigationItem.planComptable:
        return 1;
      case NavigationItem.guide:
        return 2;
      case NavigationItem.tools:
        return 3;
      case NavigationItem.quiz:
        return 4;
      case NavigationItem.settings:
        return 5;
      // References is not included in bottom navigation
      default:
        return 0;
    }
  }

  // Helper method to convert bottom nav index to NavigationItem
  NavigationItem _getItemForIndex(int index) {
    switch (index) {
      case 0:
        return NavigationItem.home;
      case 1:
        return NavigationItem.planComptable;
      case 2:
        return NavigationItem.guide;
      case 3:
        return NavigationItem.tools;
      case 4:
        return NavigationItem.quiz;
      case 5:
        return NavigationItem.settings;
      default:
        return NavigationItem.home;
    }
  }

  Widget _buildNavigationRail(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;

    return Container(
      width: 250,
      decoration: BoxDecoration(
        color: colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color: colorScheme.shadow.withOpacity(0.1),
            blurRadius: 8,
            offset: const Offset(2, 0),
          ),
        ],
      ),
      child: Column(
        children: [
          const SizedBox(height: 24),
          _buildNavItem(
            context: context,
            icon: Icons.home_outlined,
            selectedIcon: Icons.home_rounded,
            label: 'Accueil',
            item: NavigationItem.home,
          ),
          const SizedBox(height: 8),
          _buildDivider(context),
          const SizedBox(height: 8),
          _buildNavItem(
            context: context,
            icon: Icons.account_balance_outlined,
            selectedIcon: Icons.account_balance_rounded,
            label: 'PCG',
            item: NavigationItem.planComptable,
          ),
          const SizedBox(height: 16),
          _buildNavItem(
            context: context,
            icon: Icons.menu_book_outlined,
            selectedIcon: Icons.menu_book_rounded,
            label: 'Guide',
            item: NavigationItem.guide,
          ),
          const SizedBox(height: 16),
          _buildNavItem(
            context: context,
            icon: Icons.calculate_outlined,
            selectedIcon: Icons.calculate_rounded,
            label: 'Outils',
            item: NavigationItem.tools,
          ),
          const SizedBox(height: 16),
          _buildNavItem(
            context: context,
            icon: Icons.library_books_outlined,
            selectedIcon: Icons.library_books_rounded,
            label: 'Références',
            item: NavigationItem.references,
          ),
          const SizedBox(height: 16),
          _buildNavItem(
            context: context,
            icon: Icons.quiz_outlined,
            selectedIcon: Icons.quiz_rounded,
            label: 'Quiz',
            item: NavigationItem.quiz,
          ),
          const Spacer(),
          _buildDivider(context),
          const SizedBox(height: 8),
          _buildNavItem(
            context: context,
            icon: Icons.settings_outlined,
            selectedIcon: Icons.settings_rounded,
            label: 'Paramètres',
            item: NavigationItem.settings,
          ),
          const SizedBox(height: 24),
        ],
      ),
    );
  }

  Widget _buildNavItem({
    required BuildContext context,
    required IconData icon,
    required IconData selectedIcon,
    required String label,
    required NavigationItem item,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isSelected = selectedItem == item;

    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Material(
        color: Colors.transparent,
        borderRadius: BorderRadius.circular(16),
        child: InkWell(
          borderRadius: BorderRadius.circular(16),
          onTap: () => onNavigationItemSelected(item),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              color: isSelected
                  ? colorScheme.primaryContainer.withOpacity(0.2)
                  : Colors.transparent,
              border: Border.all(
                color: isSelected
                    ? colorScheme.primary.withOpacity(0.2)
                    : Colors.transparent,
                width: 1.5,
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? colorScheme.primary.withOpacity(0.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    isSelected ? selectedIcon : icon,
                    color: isSelected
                        ? colorScheme.primary
                        : colorScheme.onSurfaceVariant,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Text(
                    label,
                    style: theme.textTheme.titleSmall?.copyWith(
                      color: isSelected
                          ? colorScheme.primary
                          : colorScheme.onSurfaceVariant,
                      fontWeight:
                          isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
                if (isSelected)
                  Container(
                    width: 8,
                    height: 8,
                    decoration: BoxDecoration(
                      color: colorScheme.primary,
                      shape: BoxShape.circle,
                    ),
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildDivider(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 24),
      child: Divider(
        color: Theme.of(context).colorScheme.outlineVariant.withOpacity(0.5),
        height: 1,
      ),
    );
  }
}
