import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

/// Split-pane layout widget for exam interface
class SplitPaneLayout extends StatefulWidget {
  final Widget leftPane;
  final Widget rightPane;
  final double initialSplitRatio;
  final double minLeftWidth;
  final double minRightWidth;
  final bool showSplitter;

  const SplitPaneLayout({
    super.key,
    required this.leftPane,
    required this.rightPane,
    this.initialSplitRatio = 0.4,
    this.minLeftWidth = 300,
    this.minRightWidth = 400,
    this.showSplitter = true,
  });

  @override
  State<SplitPaneLayout> createState() => _SplitPaneLayoutState();
}

class _SplitPaneLayoutState extends State<SplitPaneLayout> {
  late double _splitRatio;
  bool _isDragging = false;

  @override
  void initState() {
    super.initState();
    _splitRatio = widget.initialSplitRatio;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final leftWidth = constraints.maxWidth * _splitRatio;
        final rightWidth = constraints.maxWidth * (1 - _splitRatio);

        return Row(
          children: [
            // Left pane
            SizedBox(
              width: leftWidth,
              child: widget.leftPane,
            ),
            // Splitter
            if (widget.showSplitter)
              GestureDetector(
                onHorizontalDragStart: (_) {
                  setState(() => _isDragging = true);
                  HapticFeedback.selectionClick();
                },
                onHorizontalDragUpdate: (details) {
                  final newRatio = (leftWidth + details.delta.dx) / constraints.maxWidth;
                  final newLeftWidth = constraints.maxWidth * newRatio;
                  final newRightWidth = constraints.maxWidth * (1 - newRatio);

                  if (newLeftWidth >= widget.minLeftWidth && 
                      newRightWidth >= widget.minRightWidth) {
                    setState(() => _splitRatio = newRatio);
                  }
                },
                onHorizontalDragEnd: (_) {
                  setState(() => _isDragging = false);
                  HapticFeedback.selectionClick();
                },
                child: MouseRegion(
                  cursor: SystemMouseCursors.resizeColumn,
                  child: Container(
                    width: 8,
                    color: _isDragging 
                        ? Theme.of(context).colorScheme.primary.withOpacity(0.3)
                        : Colors.transparent,
                    child: Center(
                      child: Container(
                        width: 2,
                        color: Theme.of(context).dividerColor,
                      ),
                    ),
                  ),
                ),
              ),
            // Right pane
            Expanded(
              child: widget.rightPane,
            ),
          ],
        );
      },
    );
  }
}

/// Question overview mini-map widget
class QuestionOverviewMiniMap extends StatelessWidget {
  final int totalQuestions;
  final int currentQuestion;
  final Set<int> answeredQuestions;
  final Set<int> flaggedQuestions;
  final Function(int) onQuestionTap;

  const QuestionOverviewMiniMap({
    super.key,
    required this.totalQuestions,
    required this.currentQuestion,
    required this.answeredQuestions,
    required this.flaggedQuestions,
    required this.onQuestionTap,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Questions (${currentQuestion + 1}/$totalQuestions)',
            style: Theme.of(context).textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: List.generate(totalQuestions, (index) {
              final isAnswered = answeredQuestions.contains(index);
              final isCurrent = index == currentQuestion;
              final isFlagged = flaggedQuestions.contains(index);
              
              return GestureDetector(
                onTap: () => onQuestionTap(index),
                child: Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: isCurrent 
                        ? colorScheme.primary
                        : isAnswered 
                            ? colorScheme.primaryContainer
                            : colorScheme.surface,
                    borderRadius: BorderRadius.circular(6),
                    border: Border.all(
                      color: isFlagged 
                          ? Colors.orange
                          : isCurrent
                              ? colorScheme.primary
                              : colorScheme.outline.withOpacity(0.3),
                      width: isFlagged ? 2 : 1,
                    ),
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: TextStyle(
                        color: isCurrent 
                            ? colorScheme.onPrimary
                            : isAnswered
                                ? colorScheme.onPrimaryContainer
                                : colorScheme.onSurface,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              );
            }),
          ),
          const SizedBox(height: 12),
          _buildLegend(context),
        ],
      ),
    );
  }

  Widget _buildLegend(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Column(
      children: [
        _buildLegendItem(
          context,
          colorScheme.primary,
          'Actuelle',
        ),
        _buildLegendItem(
          context,
          colorScheme.primaryContainer,
          'Répondue',
        ),
        _buildLegendItem(
          context,
          Colors.orange,
          'Marquée',
          isBorder: true,
        ),
        _buildLegendItem(
          context,
          colorScheme.surface,
          'Non répondue',
          isBorder: true,
        ),
      ],
    );
  }

  Widget _buildLegendItem(
    BuildContext context, 
    Color color, 
    String label, 
    {bool isBorder = false}
  ) {
    final colorScheme = Theme.of(context).colorScheme;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Container(
            width: 12,
            height: 12,
            decoration: BoxDecoration(
              color: isBorder ? colorScheme.surface : color,
              borderRadius: BorderRadius.circular(2),
              border: Border.all(
                color: isBorder ? color : color,
                width: 1,
              ),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
    );
  }
}

/// Progress indicator with time and completion stats
class ExamProgressIndicator extends StatelessWidget {
  final Duration timeElapsed;
  final Duration? timeLimit;
  final int currentQuestion;
  final int totalQuestions;
  final int answeredQuestions;

  const ExamProgressIndicator({
    super.key,
    required this.timeElapsed,
    this.timeLimit,
    required this.currentQuestion,
    required this.totalQuestions,
    required this.answeredQuestions,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    final progressPercentage = totalQuestions > 0 
        ? answeredQuestions / totalQuestions 
        : 0.0;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Time indicator
          Row(
            children: [
              Icon(
                Icons.access_time,
                size: 16,
                color: colorScheme.primary,
              ),
              const SizedBox(width: 8),
              Text(
                _formatDuration(timeElapsed),
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (timeLimit != null) ...[
                Text(
                  ' / ${_formatDuration(timeLimit!)}',
                  style: textTheme.bodySmall?.copyWith(
                    color: colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ],
          ),
          const SizedBox(height: 12),
          
          // Progress bar
          LinearProgressIndicator(
            value: progressPercentage,
            backgroundColor: colorScheme.surfaceContainerHighest,
            valueColor: AlwaysStoppedAnimation<Color>(colorScheme.primary),
          ),
          const SizedBox(height: 8),
          
          // Progress text
          Text(
            '$answeredQuestions/$totalQuestions questions répondues',
            style: textTheme.bodySmall,
          ),
          Text(
            'Question ${currentQuestion + 1}/$totalQuestions',
            style: textTheme.bodySmall?.copyWith(
              color: colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  String _formatDuration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes % 60;
    final seconds = duration.inSeconds % 60;
    
    if (hours > 0) {
      return '${hours}h ${minutes.toString().padLeft(2, '0')}m';
    } else {
      return '${minutes.toString().padLeft(2, '0')}:${seconds.toString().padLeft(2, '0')}';
    }
  }
}

/// Navigation breadcrumb widget
class ExamBreadcrumb extends StatelessWidget {
  final String examTitle;
  final int currentQuestion;
  final int totalQuestions;

  const ExamBreadcrumb({
    super.key,
    required this.examTitle,
    required this.currentQuestion,
    required this.totalQuestions,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          Icon(
            Icons.quiz,
            size: 16,
            color: colorScheme.primary,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              examTitle,
              style: textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w500,
              ),
              overflow: TextOverflow.ellipsis,
            ),
          ),
          const SizedBox(width: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: colorScheme.primaryContainer,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              'Q${currentQuestion + 1}',
              style: textTheme.labelSmall?.copyWith(
                color: colorScheme.onPrimaryContainer,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

/// Keyboard shortcuts help overlay
class KeyboardShortcutsHelp extends StatelessWidget {
  const KeyboardShortcutsHelp({super.key});

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: colorScheme.outline.withOpacity(0.2)),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Raccourcis clavier',
            style: textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          _buildShortcut(context, 'N', 'Question suivante'),
          _buildShortcut(context, 'P', 'Question précédente'),
          _buildShortcut(context, '1-4', 'Sélectionner réponse A-D'),
          _buildShortcut(context, 'F', 'Marquer/Démarquer'),
          _buildShortcut(context, 'S', 'Voir énoncé'),
          _buildShortcut(context, 'Esc', 'Fermer panneau'),
        ],
      ),
    );
  }

  Widget _buildShortcut(BuildContext context, String key, String description) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: colorScheme.outline.withOpacity(0.3)),
            ),
            child: Text(
              key,
              style: textTheme.bodySmall?.copyWith(
                fontFamily: 'monospace',
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              description,
              style: textTheme.bodySmall,
            ),
          ),
        ],
      ),
    );
  }
}