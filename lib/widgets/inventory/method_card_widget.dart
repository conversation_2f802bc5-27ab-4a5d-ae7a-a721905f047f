import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../services/theme_service.dart';

class MethodCardWidget extends StatelessWidget {
  final Map<String, dynamic> method;

  const MethodCardWidget({
    super.key,
    required this.method,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final isDark = context.watch<ThemeService>().isDarkMode;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: colorScheme.outline.withOpacity(0.2),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            method['nom'],
            style: textTheme.titleSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: colorScheme.primary,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            method['description'],
            style: textTheme.bodyMedium?.copyWith(
              color: colorScheme.onSurface,
            ),
          ),
          if (method['avantages'] != null) ...[
            const SizedBox(height: 12),
            Text(
              'Avantages:',
              style: textTheme.titleSmall?.copyWith(
                color: colorScheme.secondary,
                fontWeight: FontWeight.bold,
              ),
            ),
            ...method['avantages'].map<Widget>((avantage) => Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.check_circle_outline,
                        size: 16,
                        color: colorScheme.secondary,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          avantage,
                          style: textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
          if (method['inconvenients'] != null) ...[
            const SizedBox(height: 12),
            Text(
              'Inconvénients:',
              style: textTheme.titleSmall?.copyWith(
                color: colorScheme.error,
                fontWeight: FontWeight.bold,
              ),
            ),
            ...method['inconvenients'].map<Widget>((inconvenient) => Padding(
                  padding: const EdgeInsets.only(top: 4),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(
                        Icons.warning_amber_outlined,
                        size: 16,
                        color: colorScheme.error,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          inconvenient,
                          style: textTheme.bodyMedium,
                        ),
                      ),
                    ],
                  ),
                )),
          ],
          if (method['variantes'] != null) ...[
            const SizedBox(height: 16),
            Text(
              'Variantes:',
              style: textTheme.titleSmall?.copyWith(
                color: colorScheme.secondary,
                fontWeight: FontWeight.bold,
              ),
            ),
            ...method['variantes'].map<Widget>((variante) => Container(
                  margin: const EdgeInsets.only(top: 12),
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: colorScheme.surface,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colorScheme.outline.withOpacity(0.1),
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        variante['nom'],
                        style: textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        variante['description'],
                        style: textTheme.bodyMedium,
                      ),
                      if (variante['formule'] != null) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color:
                                colorScheme.primaryContainer.withOpacity(0.3),
                            borderRadius: BorderRadius.circular(4),
                          ),
                          child: Text(
                            variante['formule'],
                            style: textTheme.bodyMedium?.copyWith(
                              fontFamily: 'monospace',
                              color: colorScheme.primary,
                            ),
                          ),
                        ),
                      ],
                      if (variante['avantages'] != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Avantages:',
                          style: textTheme.bodySmall?.copyWith(
                            color: colorScheme.secondary,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        ...variante['avantages']
                            .map<Widget>((avantage) => Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Icon(
                                        Icons.check_circle_outline,
                                        size: 14,
                                        color: colorScheme.secondary,
                                      ),
                                      const SizedBox(width: 4),
                                      Expanded(
                                        child: Text(
                                          avantage,
                                          style: textTheme.bodySmall,
                                        ),
                                      ),
                                    ],
                                  ),
                                )),
                      ],
                      if (variante['inconvenients'] != null) ...[
                        const SizedBox(height: 8),
                        Text(
                          'Inconvénients:',
                          style: textTheme.bodySmall?.copyWith(
                            color: colorScheme.error,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        ...variante['inconvenients']
                            .map<Widget>((inconvenient) => Padding(
                                  padding: const EdgeInsets.only(top: 4),
                                  child: Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Icon(
                                        Icons.warning_amber_outlined,
                                        size: 14,
                                        color: colorScheme.error,
                                      ),
                                      const SizedBox(width: 4),
                                      Expanded(
                                        child: Text(
                                          inconvenient,
                                          style: textTheme.bodySmall,
                                        ),
                                      ),
                                    ],
                                  ),
                                )),
                      ],
                    ],
                  ),
                )),
          ],
          if (method['schema'] != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colorScheme.outline.withOpacity(0.1),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Exemple Pratique:',
                    style: textTheme.titleSmall?.copyWith(
                      color: colorScheme.primary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  if (method['schema']['type'] == 'FIFODiagram')
                    _buildFIFOExample(context, method['schema'])
                  else if (method['schema']['type'] == 'CUMPDiagram')
                    _buildCUMPExample(context, method['schema']),
                ],
              ),
            ),
          ],
          if (method['cas_pratique'] != null) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.secondaryContainer.withOpacity(0.3),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: colorScheme.outline.withOpacity(0.1),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        Icons.lightbulb_outline,
                        color: colorScheme.secondary,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Application Pratique',
                        style: textTheme.titleSmall?.copyWith(
                          color: colorScheme.secondary,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Text(
                    method['cas_pratique']['scenario'],
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...method['cas_pratique']['operations']
                      .map<Widget>((operation) {
                    return Padding(
                      padding: const EdgeInsets.only(top: 8),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${operation['date']} - ${operation['description']}',
                            style: textTheme.bodyMedium?.copyWith(
                              color: colorScheme.secondary,
                            ),
                          ),
                          if (operation['details'] != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 4, left: 16),
                              child: Text(
                                operation['details'],
                                style: textTheme.bodyMedium,
                              ),
                            ),
                          if (operation['calcul_cump'] != null)
                            Container(
                              margin: const EdgeInsets.only(top: 4),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: colorScheme.primaryContainer
                                    .withOpacity(0.3),
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Text(
                                operation['calcul_cump'],
                                style: textTheme.bodyMedium?.copyWith(
                                  fontFamily: 'monospace',
                                  color: colorScheme.primary,
                                ),
                              ),
                            ),
                          if (operation['ecriture'] != null)
                            Container(
                              margin: const EdgeInsets.only(top: 4),
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                color: colorScheme.surfaceContainerHighest,
                                borderRadius: BorderRadius.circular(4),
                              ),
                              child: Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          '${operation['ecriture']['compte']} - ${operation['ecriture']['libelle']}',
                                          style: textTheme.bodySmall,
                                        ),
                                      ],
                                    ),
                                  ),
                                  Row(
                                    children: [
                                      if (operation['ecriture']['debit'] > 0)
                                        Text(
                                          '${operation['ecriture']['debit']} DH',
                                          style: textTheme.bodySmall?.copyWith(
                                            color: colorScheme.primary,
                                          ),
                                        ),
                                      const SizedBox(width: 16),
                                      if (operation['ecriture']['credit'] > 0)
                                        Text(
                                          '${operation['ecriture']['credit']} DH',
                                          style: textTheme.bodySmall?.copyWith(
                                            color: colorScheme.secondary,
                                          ),
                                        ),
                                    ],
                                  ),
                                ],
                              ),
                            ),
                        ],
                      ),
                    );
                  }),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFIFOExample(BuildContext context, Map<String, dynamic> schema) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final exemple = schema['exemple'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          exemple['scenario'],
          style: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          'Mouvements:',
          style: textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        ...exemple['mouvements'].map<Widget>((mouvement) {
          return Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  '${mouvement['date']} - ${mouvement['type']}:',
                  style: textTheme.bodyMedium,
                ),
                Text(
                  '${mouvement['quantite']} × ${mouvement['prix']} = ${mouvement['total']} DH',
                  style: textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: colorScheme.primary,
                  ),
                ),
              ],
            ),
          );
        }),
        if (exemple['stock_final'] != null) ...[
          const Divider(),
          Text(
            'Stock Final:',
            style: textTheme.titleSmall?.copyWith(
              color: colorScheme.secondary,
            ),
          ),
          const SizedBox(height: 8),
          ...exemple['stock_final']['composition'].map<Widget>((lot) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 4),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    lot['lot'],
                    style: textTheme.bodyMedium,
                  ),
                  Text(
                    '${lot['quantite']} × ${lot['prix']} = ${lot['total']} DH',
                    style: textTheme.bodyMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            );
          }),
          const Divider(),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Total:',
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${exemple['stock_final']['total_quantite']} unités - ${exemple['stock_final']['valeur_totale']} DH',
                style: textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: colorScheme.primary,
                ),
              ),
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildCUMPExample(BuildContext context, Map<String, dynamic> schema) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    final exemple = schema['exemple'];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          exemple['scenario'],
          style: textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          'Mouvements:',
          style: textTheme.titleSmall,
        ),
        const SizedBox(height: 8),
        ...exemple['mouvements'].map<Widget>((mouvement) {
          return Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHighest.withOpacity(0.3),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${mouvement['date']} - ${mouvement['type']}',
                      style: textTheme.bodyMedium,
                    ),
                    Text(
                      '${mouvement['quantite']} unités',
                      style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                if (mouvement['prix'] != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 4),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Prix unitaire:',
                          style: textTheme.bodySmall,
                        ),
                        Text(
                          '${mouvement['prix']} DH',
                          style: textTheme.bodySmall?.copyWith(
                            color: colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                if (mouvement['nouveau_cump'] != null)
                  Container(
                    margin: const EdgeInsets.only(top: 4),
                    padding: const EdgeInsets.all(4),
                    decoration: BoxDecoration(
                      color: colorScheme.primaryContainer.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          'Nouveau CUMP:',
                          style: textTheme.bodySmall,
                        ),
                        Text(
                          '${mouvement['nouveau_cump']} DH',
                          style: textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: colorScheme.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          );
        }),
        if (exemple['analyse_impact'] != null) ...[
          const SizedBox(height: 12),
          Text(
            'Impact:',
            style: textTheme.titleSmall?.copyWith(
              color: colorScheme.secondary,
            ),
          ),
          const SizedBox(height: 8),
          ...exemple['analyse_impact'].map<Widget>((impact) => Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    Icon(
                      Icons.arrow_right,
                      size: 16,
                      color: colorScheme.secondary,
                    ),
                    const SizedBox(width: 4),
                    Expanded(
                      child: Text(
                        impact,
                        style: textTheme.bodyMedium,
                      ),
                    ),
                  ],
                ),
              )),
        ],
      ],
    );
  }
}
