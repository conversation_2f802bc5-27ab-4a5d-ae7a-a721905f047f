import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

class WhatsNewDialog extends StatefulWidget {
  final String appVersion;
  final VoidCallback onDismiss;

  const WhatsNewDialog({
    super.key,
    required this.appVersion,
    required this.onDismiss,
  });

  @override
  State<WhatsNewDialog> createState() => _WhatsNewDialogState();
}

class _WhatsNewDialogState extends State<WhatsNewDialog> {
  int _currentPage = 0;
  final PageController _pageController = PageController();

  final List<Map<String, dynamic>> _features = [
    {
      'title': 'Prière pour la Palestine 🇵🇸',
      'description': 'Prions pour nos frères et sœurs à Gaza et en Palestine dans ces temps difficiles. Que la paix et la justice prévalent.',
      'icon': Icons.volunteer_activism,
      'color': Colors.green,
      'isPalestineFeature': true,
    },
    {
      'title': 'Examens',
      'description': 'Préparez-vous avec nos examens pratiques sur divers sujets de comptabilité marocaine.',
      'icon': Icons.school,
      'color': Colors.blue,
      'isPalestineFeature': false,
    },
    {
      'title': 'Quiz Interactifs',
      'description': 'Testez vos connaissances avec nos quiz interactifs et suivez votre progression.',
      'icon': Icons.quiz,
      'color': Colors.green,
      'isPalestineFeature': false,
    },
    {
      'title': 'Profil Utilisateur',
      'description': 'Suivez votre progression et consultez votre historique d\'apprentissage.',
      'icon': Icons.person,
      'color': Colors.orange,
      'isPalestineFeature': false,
    },
    {
      'title': 'Guides Comptables',
      'description': 'Accédez à des guides détaillés sur la comptabilité marocaine.',
      'icon': Icons.menu_book,
      'color': Colors.purple,
      'isPalestineFeature': false,
    },
  ];

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return Dialog(
      insetPadding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 24.0),
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24)),
      child: Container(
        width: MediaQuery.of(context).size.width * 0.85,
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            // Header
            Row(
              children: [
                Icon(
                  Icons.new_releases,
                  color: colorScheme.primary,
                  size: 28,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Nouveautés',
                        style: textTheme.headlineSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      Text(
                        'Version ${widget.appVersion}',
                        style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(
                    Icons.close,
                    color: colorScheme.onSurfaceVariant,
                  ),
                  onPressed: widget.onDismiss,
                ),
              ],
            ),
            
            const SizedBox(height: 24),
            const Divider(),
            const SizedBox(height: 16),
            
            // Content
            Expanded(
              child: PageView.builder(
                controller: _pageController,
                itemCount: _features.length,
                onPageChanged: (index) {
                  setState(() {
                    _currentPage = index;
                  });
                },
                itemBuilder: (context, index) {
                  final feature = _features[index];
                  return _buildFeaturePage(
                    context,
                    title: feature['title'],
                    description: feature['description'],
                    icon: feature['icon'],
                    color: feature['color'],
                    isPalestineFeature: feature['isPalestineFeature'] ?? false,
                  );
                },
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Dots indicator
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: List.generate(
                _features.length,
                (index) => AnimatedContainer(
                  duration: 300.ms,
                  margin: const EdgeInsets.symmetric(horizontal: 4),
                  width: _currentPage == index ? 16 : 8,
                  height: 8,
                  decoration: BoxDecoration(
                    color: _currentPage == index
                        ? colorScheme.primary
                        : colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(4),
                  ),
                ),
              ),
            ),
            
            const SizedBox(height: 24),
            
            // Button
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: widget.onDismiss,
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 14),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(16),
                  ),
                ),
                child: Text(
                  'Continuer',
                  style: textTheme.labelLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    ).animate().scale(
      duration: 400.ms,
      curve: Curves.easeOutBack,
      delay: 200.ms,
    ).fadeIn(
      duration: 300.ms,
    );
  }

  Widget _buildFeaturePage(
    BuildContext context, {
    required String title,
    required String description,
    required IconData icon,
    required Color color,
    bool isPalestineFeature = false,
  }) {
    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;
    
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        // Feature icon in a decorative container
        if (isPalestineFeature) 
          // Special Palestine flag decoration
          Container(
            width: 130,
            height: 130,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: const LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.black,
                  Colors.white,
                  Colors.green
                ],
                stops: [0.33, 0.33, 0.67],
              ),
            ),
            child: Center(
              child: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.8),
                  shape: BoxShape.circle,
                ),
                child: Center(
                  child: Icon(
                    icon,
                    size: 30,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ).animate()
            .scale(delay: 100.ms, duration: 600.ms, curve: Curves.elasticOut)
            .fade(duration: 500.ms)
        else
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Icon(
                icon,
                size: 60,
                color: color,
              ),
            ),
          ).animate()
            .scale(delay: 100.ms, duration: 600.ms, curve: Curves.elasticOut)
            .fade(duration: 500.ms),
          
        const SizedBox(height: 24),
        
        // Feature title
        Text(
          title,
          style: textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: isPalestineFeature ? Colors.green.shade800 : colorScheme.onSurface,
          ),
          textAlign: TextAlign.center,
        ).animate()
          .slideY(begin: 0.2, end: 0, delay: 200.ms, duration: 400.ms, curve: Curves.easeOutCubic)
          .fade(delay: 200.ms, duration: 400.ms),
          
        const SizedBox(height: 12),
        
        // Feature description
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Text(
            description,
            style: textTheme.bodyLarge?.copyWith(
              color: isPalestineFeature
                  ? Colors.black87
                  : colorScheme.onSurfaceVariant,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ).animate()
          .slideY(begin: 0.2, end: 0, delay: 300.ms, duration: 400.ms, curve: Curves.easeOutCubic)
          .fade(delay: 300.ms, duration: 400.ms),
      ],
    );
  }
}